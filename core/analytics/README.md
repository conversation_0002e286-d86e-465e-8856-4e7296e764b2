# Core Analytics Module

This module provides a centralized analytics framework for the application, following Android development best practices.

**Package**: `one.srp.core.analytics`

## Features

- **Multi-provider support**: Easily integrate multiple analytics providers (Firebase, AppFlyer, custom analytics, etc.)
- **Type-safe events**: Strongly typed event system with Kotlin serialization
- **Coroutine-based**: Asynchronous event tracking with proper error handling
- **Hilt integration**: Dependency injection ready
- **ProGuard optimized**: Proper obfuscation rules for release builds

## Architecture

### Core Components

- **AnalyticsManager**: Central coordinator for all analytics operations
- **AnalyticsProvider**: Interface for implementing different analytics backends
- **AnalyticsEvent**: Type-safe event definitions with serialization support
- **EnhancedAnalyticsEvent**: Extended events with metadata (category, priority, timestamps)

### Event Categories

- `USER_ACTION`: User interactions and behaviors
- `SCREEN_VIEW`: Screen navigation and views
- `SYSTEM`: System-level events
- `ERROR`: Error tracking and debugging
- `PERFORMANCE`: Performance metrics
- `BUSINESS`: Business logic and conversion events

## Usage

### Basic Setup

```kotlin
@Inject
lateinit var analyticsManager: AnalyticsManager

// Initialize in Application class
analyticsManager.initialize()
```

### Tracking Events

```kotlin
// Simple event
val event = UserActionEvent(
    action = "button_click",
    target = "login_button"
)
analyticsManager.trackEvent(event)

// Screen view
val screenEvent = ScreenViewEvent(
    screenName = "home_screen",
    screenClass = "HomeActivity"
)
analyticsManager.trackEvent(screenEvent)
```

### Custom Providers

Implement the `AnalyticsProvider` interface to add new analytics backends:

```kotlin
class CustomAnalyticsProvider : BaseAnalyticsProvider() {
    override val providerName = "CustomProvider"
    
    override suspend fun initialize() {
        // Initialize your analytics SDK
        _isInitialized = true
    }
    
    override suspend fun trackEvent(event: AnalyticsEvent) {
        if (!isTrackingEnabled()) return
        // Send event to your analytics backend
    }
}

// Register the provider
analyticsManager.registerProvider(CustomAnalyticsProvider())
```

## Dependencies

This module uses:
- Kotlin Coroutines for async operations
- Hilt for dependency injection
- Kotlinx Serialization for event serialization
- Timber for logging
- DataStore for preferences
- Firebase Analytics SDK
- Retrofit for network requests

### Required Dependencies

Add to your `core/analytics/build.gradle.kts`:

```kotlin
dependencies {
    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit2.kotlinx.serialization.converter)
    implementation(libs.okhttp.logging.interceptor)

    // Other dependencies...
}
```

## Providers

### Built-in Providers

1. **FirebaseAnalyticsProvider**: Integrates with Firebase Analytics SDK
2. **CustomApiAnalyticsProvider**: Integrates with custom API endpoints

### Application Integration

To integrate with your existing MetricService, create an application-level implementation:

```kotlin
// app/src/main/java/.../analytics/AppAnalyticsModule.kt
@Module
@InstallIn(SingletonComponent::class)
abstract class AppAnalyticsModule {

    @Binds
    @Singleton
    abstract fun bindCustomAnalyticsApiClient(
        metricServiceApiClient: MetricServiceAnalyticsApiClient
    ): CustomAnalyticsApiClient
}
```

## Quick Start

### 1. Initialize in Application

```kotlin
@HiltAndroidApp
class MyApplication : Application() {
    @Inject lateinit var analyticsProviderFactory: AnalyticsProviderFactory

    override fun onCreate() {
        super.onCreate()
        lifecycleScope.launch {
            analyticsProviderFactory.initializeProviders()
        }
    }
}
```

### 2. Track Events

```kotlin
@Inject lateinit var analyticsManager: AnalyticsManager

val event = UserActionEvent(action = "click", target = "button")
analyticsManager.trackEvent(event)
```

## Documentation

- `PROVIDER_INTEGRATION.md`: Detailed integration guide
- `IMPLEMENTATION_SUMMARY.md`: Complete implementation overview
- `AnalyticsUsageExample.kt`: Usage examples

## ProGuard

The module includes comprehensive ProGuard rules to ensure proper obfuscation while preserving necessary analytics functionality.
