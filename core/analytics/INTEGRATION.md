# Analytics Module Integration Guide

## 1. 项目配置

### settings.gradle.kts
```kotlin
include(":core:analytics")
```

### app/build.gradle.kts
```kotlin
dependencies {
    implementation(project(":core:analytics"))
}
```

### gradle/libs.versions.toml
确保包含以下插件：
```toml
[plugins]
android-library = { id = "com.android.library", version.ref = "agp" }
```

### 根目录 build.gradle.kts
```kotlin
plugins {
    alias(libs.plugins.android-library) apply false
    // ... 其他插件
}
```

## 2. 在 Application 中初始化

```kotlin
@HiltAndroidApp
class MainApplication : Application() {
    
    @Inject
    lateinit var analyticsManager: AnalyticsManager
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化 Analytics
        lifecycleScope.launch {
            analyticsManager.initialize()
        }
    }
}
```

## 3. 在 Activity/Fragment 中使用

```kotlin
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var analyticsManager: AnalyticsManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 跟踪屏幕浏览
        val screenEvent = ScreenViewEvent(
            screenName = "main_screen",
            screenClass = "MainActivity"
        )
        analyticsManager.trackEvent(screenEvent)
        
        // 跟踪用户行为
        val actionEvent = UserActionEvent(
            action = "button_click",
            target = "login_button"
        )
        analyticsManager.trackEvent(actionEvent)
    }
}
```

## 4. 自定义 Analytics Provider

```kotlin
class FirebaseAnalyticsProvider @Inject constructor(
    private val firebaseAnalytics: FirebaseAnalytics
) : BaseAnalyticsProvider() {
    
    override val providerName = "Firebase"
    
    override suspend fun initialize() {
        // Firebase 通常自动初始化
        _isInitialized = true
    }
    
    override suspend fun trackEvent(event: AnalyticsEvent) {
        if (!isTrackingEnabled()) return
        
        val bundle = Bundle().apply {
            event.parameters.forEach { (key, value) ->
                when (value) {
                    is String -> putString(key, value)
                    is Int -> putInt(key, value)
                    is Long -> putLong(key, value)
                    is Double -> putDouble(key, value)
                    is Boolean -> putBoolean(key, value)
                    else -> putString(key, value.toString())
                }
            }
        }
        
        firebaseAnalytics.logEvent(event.eventName, bundle)
    }
    
    override suspend fun setUserProperties(properties: Map<String, Any>) {
        if (!isTrackingEnabled()) return
        
        properties.forEach { (key, value) ->
            firebaseAnalytics.setUserProperty(key, value.toString())
        }
    }
    
    override suspend fun setUserId(userId: String?) {
        if (!isTrackingEnabled()) return
        firebaseAnalytics.setUserId(userId)
    }
}
```

## 5. 注册 Provider

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object AnalyticsProvidersModule {
    
    @Provides
    @Singleton
    fun provideFirebaseAnalyticsProvider(
        @ApplicationContext context: Context
    ): FirebaseAnalyticsProvider {
        val firebaseAnalytics = FirebaseAnalytics.getInstance(context)
        return FirebaseAnalyticsProvider(firebaseAnalytics)
    }
    
    @Provides
    @Singleton
    fun provideAnalyticsManager(
        firebaseProvider: FirebaseAnalyticsProvider
    ): AnalyticsManager {
        return AnalyticsManager().apply {
            registerProvider(firebaseProvider)
        }
    }
}
```

## 6. ProGuard 配置

模块已包含必要的 ProGuard 规则，会自动应用到主应用。

## 7. 测试

```kotlin
@Test
fun testAnalyticsEvent() {
    val event = UserActionEvent(
        action = "test_action",
        target = "test_target"
    )
    
    assertEquals("user_action", event.eventName)
    assertTrue(event.parameters.containsKey("action"))
    assertEquals("test_action", event.parameters["action"])
}
```
