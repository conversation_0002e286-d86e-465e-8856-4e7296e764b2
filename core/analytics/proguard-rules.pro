# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Keep all analytics related classes and interfaces
-keep class one.srp.core.analytics.** { *; }

# Keep analytics event data classes
-keep class * implements one.srp.core.analytics.AnalyticsEvent { *; }

# Keep analytics provider implementations
-keep class * implements one.srp.core.analytics.providers.AnalyticsProvider { *; }

# Keep analytics manager
-keep class one.srp.core.analytics.AnalyticsManager { *; }

# Keep Hilt generated classes for analytics module
-keep class one.srp.core.analytics.**_HiltModules* { *; }
-keep class one.srp.core.analytics.**_Factory { *; }
-keep class one.srp.core.analytics.**_MembersInjector { *; }

# Keep serialization annotations for analytics events
-keepattributes *Annotation*
-keepclassmembers class * {
    @kotlinx.serialization.SerialName <fields>;
}

# Keep enum classes used in analytics
-keepclassmembers enum one.srp.core.analytics.** {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
