# Analytics Providers Integration Guide

本文档介绍如何在应用中集成和使用新的 Firebase 和自定义 API 埋点上报 providers。

## 概述

新增的 Analytics Providers 包括：

1. **FirebaseAnalyticsProvider** - 集成 Firebase Analytics SDK
2. **CustomApiAnalyticsProvider** - 集成自定义 API 接口（基于现有的 MetricService）

## 依赖配置

### 1. Core Analytics Module 依赖

在 `core/analytics/build.gradle.kts` 中已包含必要的依赖：

```kotlin
dependencies {
    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit2.kotlinx.serialization.converter)
    implementation(libs.okhttp.logging.interceptor)

    // 其他依赖...
}
```

### 2. 应用层集成

在应用的 `build.gradle.kts` 中确保包含 analytics module：

```kotlin
dependencies {
    implementation(project(":core:analytics"))
    // 其他依赖...
}
```

## 快速开始

### 1. 应用层配置

首先，确保应用层有正确的 Analytics 配置。创建 `AppAnalyticsModule.kt`：

```kotlin
@Module
@InstallIn(SingletonComponent::class)
abstract class AppAnalyticsModule {

    @Binds
    @Singleton
    abstract fun bindCustomAnalyticsApiClient(
        metricServiceApiClient: MetricServiceAnalyticsApiClient
    ): CustomAnalyticsApiClient
}
```

创建 `MetricServiceAnalyticsApiClient.kt` 来集成现有的 MetricService：

```kotlin
@Singleton
class MetricServiceAnalyticsApiClient @Inject constructor() : CustomAnalyticsApiClient {

    override suspend fun postAnalyticsEvent(requestBody: RequestBody) {
        val response = MetricService.api.postApiMetric(requestBody)
        if (!response.isSuccessful) {
            throw Exception("MetricService API error: ${response.code()}")
        }
    }

    // 实现其他方法...
}
```

### 2. 在 Application 类中初始化

```kotlin
@HiltAndroidApp
class MyApplication : Application() {

    @Inject
    lateinit var analyticsProviderFactory: AnalyticsProviderFactory

    override fun onCreate() {
        super.onCreate()

        // 初始化所有 Analytics Providers
        lifecycleScope.launch {
            analyticsProviderFactory.initializeProviders()
        }
    }
}
```

### 3. 在 Activity/Fragment 中使用

```kotlin
class MainActivity : AppCompatActivity() {
    
    @Inject
    lateinit var analyticsManager: AnalyticsManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 跟踪屏幕浏览事件
        val screenEvent = ScreenViewEvent(
            screenName = "main_screen",
            screenClass = "MainActivity"
        )
        analyticsManager.trackEvent(screenEvent)
        
        // 跟踪用户行为事件
        val userActionEvent = UserActionEvent(
            action = "button_click",
            target = "login_button"
        )
        analyticsManager.trackEvent(userActionEvent)
    }
}
```

### 4. 设置用户属性和 ID

```kotlin
class UserManager @Inject constructor(
    private val analyticsProviderFactory: AnalyticsProviderFactory
) {
    
    suspend fun onUserLogin(userId: String, userProperties: Map<String, Any>) {
        // 设置用户 ID
        analyticsProviderFactory.setUserId(userId)
        
        // 设置用户属性
        analyticsProviderFactory.setUserProperties(userProperties)
    }
    
    suspend fun onUserLogout() {
        analyticsProviderFactory.setUserId(null)
    }
}
```

## 高级配置

### 1. 控制特定 Provider 的启用状态

```kotlin
// 禁用 Firebase Analytics
analyticsProviderFactory.setProviderEnabled("firebase", false)

// 启用自定义 API Analytics
analyticsProviderFactory.setProviderEnabled("customapi", true)
```

### 2. 全局控制 Analytics 跟踪

```kotlin
// 全局禁用 Analytics 跟踪
analyticsProviderFactory.setAnalyticsEnabled(false)

// 全局启用 Analytics 跟踪
analyticsProviderFactory.setAnalyticsEnabled(true)
```

### 3. 检查 Provider 状态

```kotlin
val providersStatus = analyticsProviderFactory.getProvidersStatus()
providersStatus.forEach { (providerName, isInitialized) ->
    Timber.d("Provider $providerName initialized: $isInitialized")
}
```

## 自定义事件

### 1. 创建自定义事件

```kotlin
@Serializable
data class PurchaseEvent(
    val itemId: String,
    val itemName: String,
    val price: Double,
    val currency: String,
    override val category: EventCategory = EventCategory.BUSINESS,
    override val priority: EventPriority = EventPriority.HIGH,
    override val timestamp: Long = System.currentTimeMillis(),
    override val userId: String? = null,
    override val sessionId: String? = null
) : BaseAnalyticsEvent(), EnhancedAnalyticsEvent {
    
    override val eventName: String = "purchase"
    
    override val parameters: Map<String, Any>
        get() = buildMap {
            put("item_id", itemId)
            put("item_name", itemName)
            put("price", price)
            put("currency", currency)
            put("category", category.name)
            put("priority", priority.name)
            put("timestamp", timestamp)
            userId?.let { put("user_id", it) }
            sessionId?.let { put("session_id", it) }
        }
}
```

### 2. 跟踪自定义事件

```kotlin
val purchaseEvent = PurchaseEvent(
    itemId = "item_123",
    itemName = "Premium Subscription",
    price = 9.99,
    currency = "USD"
)

analyticsManager.trackEvent(purchaseEvent)
```

## 与现有 MetricHelper 的兼容性

新的 Analytics Providers 与现有的 `MetricHelper` 完全兼容。您可以：

1. **逐步迁移** - 继续使用现有的 `MetricHelper`，同时逐步采用新的 Analytics 架构
2. **并行使用** - 在同一个应用中同时使用两套系统
3. **完全替换** - 最终完全迁移到新的 Analytics 架构

### 迁移示例

```kotlin
// 旧的方式（MetricHelper）
val metricEvent = MetricEvent.Custom(
    eventName = "user_action",
    block = {
        param("action", "click")
        param("target", "button")
    },
    params = { Bundle() }
)
sendMetric(metricEvent)

// 新的方式（Analytics Providers）
val userActionEvent = UserActionEvent(
    action = "click",
    target = "button"
)
analyticsManager.trackEvent(userActionEvent)
```

## 错误处理和调试

### 1. 日志记录

所有 Providers 都使用 Timber 进行日志记录：

```kotlin
// 在 Debug 模式下查看详细日志
if (BuildConfig.DEBUG) {
    Timber.plant(Timber.DebugTree())
}
```

### 2. 失败重试机制

CustomApiAnalyticsProvider 内置了失败重试机制：

- 连续失败 3 次后暂停发送 1 分钟
- 自动重置失败计数器
- 详细的错误日志记录

### 3. 性能监控

```kotlin
// 检查 Provider 初始化状态
val status = analyticsProviderFactory.getProvidersStatus()
if (!status.values.all { it }) {
    Timber.w("Some analytics providers failed to initialize: $status")
}
```

## 最佳实践

1. **在 Application 类中初始化** - 确保 Providers 在应用启动时就被初始化
2. **使用依赖注入** - 通过 Hilt 注入 AnalyticsManager 和 AnalyticsProviderFactory
3. **异步操作** - 所有 Analytics 操作都是异步的，不会阻塞 UI 线程
4. **错误处理** - Provider 内部已处理所有异常，不会影响应用稳定性
5. **性能优化** - 使用批量操作和失败重试机制优化网络请求

## 故障排除

### 常见问题

1. **Provider 未初始化**
   - 检查是否在 Application 类中调用了 `initializeProviders()`
   - 确认 Hilt 依赖注入配置正确

2. **事件未发送**
   - 检查 Analytics 是否全局启用
   - 确认特定 Provider 是否启用
   - 查看日志中的错误信息

3. **Firebase Analytics 不工作**
   - 确认 Firebase 配置文件 `google-services.json` 已正确添加
   - 检查 Firebase Analytics 依赖是否正确添加

4. **自定义 API 请求失败**
   - 检查网络连接
   - 确认 API 端点配置正确
   - 查看失败重试日志
