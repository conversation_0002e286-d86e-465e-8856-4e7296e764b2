package one.srp.core.analytics.providers

import one.srp.core.analytics.events.MetricEvent

/**
 * Interface for analytics providers (Firebase, AppFlyer, custom analytics, etc.)
 */
interface AnalyticsProvider {
    /**
     * Get the provider name
     */
    val providerName: String

    /**
     * Check if the provider is initialized
     */
    val isInitialized: <PERSON><PERSON><PERSON>

    /**
     * Initialize the analytics provider
     */
    suspend fun initialize()

    /**
     * Track an analytics event
     */
    suspend fun logEvent(event: MetricEvent)
}
