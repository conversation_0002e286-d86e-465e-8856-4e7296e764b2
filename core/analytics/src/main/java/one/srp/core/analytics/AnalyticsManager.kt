package one.srp.core.analytics

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.providers.AnalyticsProvider
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AnalyticsManager @Inject constructor() {
    private val analyticsScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val providers = mutableListOf<AnalyticsProvider>()

    private val _isInitialized = MutableStateFlow(false)
    val isInitialized: StateFlow<Boolean> = _isInitialized.asStateFlow()

    /**
     * Register an analytics provider
     */
    fun registerProvider(provider: AnalyticsProvider) {
        providers.add(provider)
        Timber.d("Registered analytics provider: ${provider.providerName}")
    }

    /**
     * Get all registered providers
     */
    fun getProviders(): List<AnalyticsProvider> = providers.toList()

    /**
     * Initialize the analytics manager and all registered providers
     */
    suspend fun initialize() {
        try {
            providers.forEach { provider ->
                try {
                    provider.initialize()
                    Timber.d("Analytics provider ${provider.providerName} initialized successfully")
                } catch (e: Exception) {
                    Timber.w(e, "Failed to initialize analytics provider ${provider.providerName}")
                }
            }
            _isInitialized.value = true
            Timber.d("AnalyticsManager initialized with ${providers.size} providers")
        } catch (e: Exception) {
            Timber.w(e, "Failed to initialize AnalyticsManager")
        }
    }

    /**
     * Track an analytics event across all providers
     */
    fun logEvent(event: MetricEvent) {
        analyticsScope.launch {
            providers.forEach { provider ->
                try {
                    if (provider.isInitialized) {
                        provider.logEvent(event)
                        Timber.d("Event ${event.eventName} tracked via ${provider.providerName}")
                    } else {
                        Timber.w("Provider ${provider.providerName} not initialized, skipping event ${event.eventName}")
                    }
                } catch (e: Exception) {
                    Timber.e(
                        e,
                        "Failed to track event ${event.eventName} via ${provider.providerName}"
                    )
                }
            }
        }
    }

    /**
     * Set user properties across all providers
     */
    fun setUserProperties(properties: Map<String, Any>) {
        analyticsScope.launch {
            providers.forEach { provider ->
                try {
                    if (provider.isInitialized) {
                        provider.setUserProperties(properties)
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed to set user properties via ${provider.providerName}")
                }
            }
        }
    }
}
