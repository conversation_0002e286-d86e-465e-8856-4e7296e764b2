package one.srp.core.analytics.utils

import android.os.Bundle
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.types.STRING_DEFAULT


fun processStringForMetric(
    text: String?,
    limit: Int = 85,
    default: String = STRING_DEFAULT,
): String {
    return text?.let { text ->
        if (text.length <= limit) return text
        text.substring(0, limit)
    } ?: run {
        default
    }
}


fun processMetricEventToBundle(event: MetricEvent): Bundle = Bundle().apply {
    putString("event_name", event.eventName)
    putString("refer", event.refer.value)

    putString("item_list_name", event.itemListName.value)
    val bundles = event.items.map { item ->
        Bundle().apply {
            putString("item_category", item.itemCategory.value)
            item.itemId?.let { putString("item_id", it) }
            item.itemName?.let { putString("item_name", it) }
            putInt("index", item.index)
        }
    }
    putParcelableArrayList("items", ArrayList(bundles))

    event.method?.let { putString("method", it.value) }
    event.actionType?.let { putString("action_type", it.value) }

    putString("platform", event.platform)
    putString("version", event.version)
    putString("event_version", event.eventVersion)

    putLong("gensmo_timestamp", event.gensmoTimestamp)
    putString("ab_info", event.abInfo)
    putString("gensmo_user_id", event.gensmoUserId)
    putString("gensmo_user_type", event.gensmoUserType)
    putString("gensmo_active_id", event.gensmoActiveId)
    putString("gensmo_user_source_id", event.gensmoUserSourceId)
    putString("appsflyer_id", event.appsflyerId)
}
