package one.srp.core.analytics.types

enum class EventRefer(val value: String) {
    Default(STRING_DEFAULT),

    OpenGuide("open_guide"),
    OpenScreen("open_screen"),
    Home("home"),
    Profile("profile"),
    Saved("saved"),
    History("history"),
    Preferences("preferences"),
    FeedDetail("feed_detail"),
    HashtagDetail("hashtag_detail"),
    SearchBoot("search_boot"),
    CollageGen("collage_gen"),
    CreateReplica("create_replica"),
    PicUploadOption("pic_upload_option"),
    CameraSetFace("camera_set_face"),
    SelectModel("select_model"),
    AvatarManage("avatar_manage"),
    TryOnSelectPanel("try_on_select_panel"),
    AiWardrobe("ai_wardrobe"),
    TryOnGen("try_on_gen"),
    EditBoxScenario("edit_box_scenario"),
    ProductDetail("product_detail"),
    ProductDetailFromSearch("product_detail_from_search"),
    PseudoProductDetail("pseudo_product_detail"),
    ShopList("shop_list"),
    Share("share"),
    LoginHalfScreen("login_half_screen"),
    LoginFullScreen("login_full_screen"),
    SystemAlbum("system_album"),
    CameraMode("camera_mode"),
    App("app"),
    Channel("channel"),
    Library("library"),
    OpenVideo("open_video"),
    OpenTagSelect("open_tag_select"),
    OpenCollage("open_collage"),
    CommentList("comment_list"),
    OtherProfile("other_profile"),
    PostBoot("post_boot"),
}