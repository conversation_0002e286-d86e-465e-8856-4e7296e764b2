package one.srp.core.analytics.types

enum class EventItemCategory(val value: String) {
    GeneralCollage("general_collage"),
    TryOnCollage("try_on_collage"),
    TryOnTask("try_on_task"),
    TryOnScenarioCollage("try_on_scenario_collage"),
    ComboShoppingCollage("combo_shopping_collage"),
    SimilarCollage("similar_collage"),

    Product("product"),
    PseudoProduct("pseudo_product"),
    InputItem("input_item"),

    Avatar("avatar"),

    Creator("creator"),
    PopupNotificationPermissionRequest("popup_notification_permission_request"),
    PopupNotificationSettingsGuide("popup_notification_settings_guide"),
    TriggerCondition("trigger_condition"),

    CommentBubbleUserReview("comment_bubble_user_review"),
    CommentBubbleScenarioAdvice("comment_bubble_scenario_advice"),
    CommentBubbleSearchAdvice("comment_bubble_search_advice"),
    RecoQueryTextOnly("reco_query_text_only"),
    RecoInspo("reco_inspo"),

    UserQueryTextOnly("user_query_text_only"),
    UserQueryTextPic("user_query_text_pic"),
    UserQueryPicOnly("user_query_pic_only"),

    MessageAvatarReady("message_avatar_ready"),
    CollageGenTask("collage_gen_task"),
    Channel("channel"),

    Activity("activity"),
    Hashtag("hashtag"),
    User("user"),
    UserComment("user_comment"),
    UserPostText("user_post_text"),
    OpenTag("open_tag"),
    MessageNotificationFollow("message_notification_follow"),
    MessageNotificationComment("message_notification_comment"),
    MessagePostComplete("message_post_complete"),
    SearchTask("search_task"),
    Popup("popup"),
}
