package one.srp.core.analytics.types

enum class EventActionType(val value: String) {
    Default(STRING_DEFAULT),

    CollageGen("collage_gen"),
    UploadImage("upload_image"),
    TryOn("try_on"),
    TryOnSelect("try_on_select"),
    CreateReplica("create_replica"),
    LoadMore("load_more"),
    Save("save"),
    Unsave("unsave"),
    FeedbackLike("feedback_like"),
    FeedbackUnlike("feedback_unlike"),
    Download("download"),
    ProductExternalJump("product_external_jump"),
    ExternalShare("external_share"),
    AvatarGenerating("avatar_generating"),
    UpdateAvatar("update_avatar"),
    LoginBlock("login_block"),
    EditBoxScenario("edit_box_scenario"),
    EnterCollageGen("enter_collage_gen"),
    InitializeSearch("initialize_search"),
    InitializePost("initialize_post"),
    EnterTryOnDetail("enter_try_on_detail"),
    EnterProductDetail("enter_product_detail"),
    InitializeSceneEditBox("initialize_scene_edit_box"),
    EntryActivity("entry_activity_page"),
    CollageGenComplete("collage_gen_complete"),
    TryOnComplete("try_on_complete"),
    NotificationSettingsJump("notification_settings_jump"),
    NotificationPermissionTurnOn("notification_permission_turn_on"),
    AvatarGen("avatar_gen"),
    Like("like"),
    CancelLike("cancel_like"),
    NoAction("no_action"),
    EnterProfile("enter_profile"),
    EnterHome("enter_home"),
    EnterProfileCollects("enter_profile_collects"),
    EnterProfilePosts("enter_profile_posts"),
    EnterCollageDetail("enter_collage_detail"),
    EnterAvatarManagement("enter_avatar_management"),
    EnterHistory("enter_history"),
    EnterTryOnHistory("enter_try_on_history"),
    EnterProfileEdit("enter_profile_edit"),
    OtherExternalJump("other_external_jump"),
    EnterSetting("enter_setting"),
    EnterHashtagDetail("enter_hashtag_detail"),
    EnterFeedDetail("enter_feed_detail"),
    Delete("delete"),
    LoginSuccess("login_success"),
    EnterCommentList("enter_comment_list"),
    CommentInput("comment_input"),
    CommentSend("comment_send"),
    Unfollow("unfollow"),
    Follow("follow"),
    EnterProfileOtherUser("enter_profile_other_user"),
    Post("post"),
    EnterChannel("enter_channel"),
    EnterSharePage("enter_share_page"),
    CollageSelect("collage_select"),
    TryOnNoAvatar("try_on_no_avatar"),
}