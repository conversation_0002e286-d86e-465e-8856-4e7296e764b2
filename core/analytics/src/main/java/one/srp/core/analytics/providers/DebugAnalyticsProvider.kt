package one.srp.core.analytics.providers

import one.srp.core.analytics.events.MetricEvent
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DebugAnalyticsProvider @Inject constructor() : AnalyticsProvider {
    override val providerName: String = "Debug"

    override var isInitialized = false

    override suspend fun initialize() {
        isInitialized = true
    }

    override suspend fun logEvent(event: MetricEvent) {
        try {
            Timber.d("[Metric]: $event")
        } catch (e: Exception) {
            Timber.w(e, "[Metric] Failed to log debug Analytics event: ${event.eventName}")
        }
    }
}
