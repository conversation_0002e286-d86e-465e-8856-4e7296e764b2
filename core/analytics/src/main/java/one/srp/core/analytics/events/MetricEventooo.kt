package one.srp.core.analytics.events

import android.os.Bundle
import com.google.firebase.analytics.ParametersBuilder
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.utils.processStringForMetric


sealed class MetricEventooo(
    open var refer: EventRefer,
    open val eventName: String,
    open val block: suspend ParametersBuilder.() -> Unit,
    open val params: suspend () -> Bundle,
) {
    data class SelectItem(
        val itemListName: EventItemListName = EventItemListName.Default,
        val method: EventMethod,
        val actionType: EventActionType? = null,
        val items: List<EventItem> = emptyList(),
        override var refer: EventRefer = MetricData.getRefer(),
        val preRefer: EventRefer = MetricData.getPreRefer(),
        val overrideItemListName: String? = null,
    ) : MetricEventooo(
        refer = refer,
        eventName = "select_item",
        block = {
            buildParams().builder(this)
            runtimeParams().builder(this)

            param("method", method.value)
            param("action_type", processStringForMetric(actionType?.value))

            param("refer", refer.value)
//            param("pre_refer", preRefer.value)

            overrideItemListName?.let { param("item_list_name", it) } ?: run {
                param("item_list_name", itemListName.value)
            }
            param("items", items.map {
                Bundle().apply {
                    putString("item_id", processStringForMetric(it.itemId))
                    putString("item_name", processStringForMetric(it.itemName))
                    putString("item_category", it.itemCategory.value)
                    putInt("index", it.index)
                }
            }.toTypedArray())
        },
        params = {
            Bundle().apply {
                putString("event_name", "select_item")

                buildParams().bundle(this)
                runtimeParams().bundle(this)

                putString("method", method.value)
                putString("action_type", processStringForMetric(actionType?.value))

                putString("refer", refer.value)
//                putString("pre_refer", preRefer.value)

                overrideItemListName?.let { putString("item_list_name", it) } ?: run {
                    putString("item_list_name", itemListName.value)
                }
                putParcelableArray(
                    "items", items.map {
                        Bundle().apply {
                            putString("item_id", processStringForMetric(it.itemId))
                            putString("item_name", processStringForMetric(it.itemName))
                            putString("item_category", it.itemCategory.value)
                            putInt("index", it.index)
                        }
                    }.toTypedArray()
                )
            }
        })

    data class ViewItemList(
        val itemListName: EventItemListName = EventItemListName.Default,
        val items: List<EventItem> = emptyList(),
        override var refer: EventRefer = MetricData.getRefer(),
        val preRefer: EventRefer = MetricData.getPreRefer(),
    ) : MetricEventooo(
        refer = refer,
        eventName = "view_item_list",
        block = {
            buildParams().builder(this)
            runtimeParams().builder(this)

            param("refer", refer.value)
//            param("pre_refer", preRefer.value)
            param("item_list_name", itemListName.value)
            param("items", items.map {
                Bundle().apply {
                    putString("item_id", processStringForMetric(it.itemId))
                    putString("item_name", processStringForMetric(it.itemName))
                    putString("item_category", it.itemCategory.value)
                    putInt("index", it.index)
                }
            }.toTypedArray())
        },
        params = {
            Bundle().apply {
                putString("event_name", "view_item_list")

                buildParams().bundle(this)
                runtimeParams().bundle(this)

                putString("refer", refer.value)
//                putString("pre_refer", preRefer.value)
                putString("item_list_name", itemListName.value)
                putParcelableArray(
                    "items", items.map {
                        Bundle().apply {
                            putString("item_id", processStringForMetric(it.itemId))
                            putString("item_name", processStringForMetric(it.itemName))
                            putString("item_category", it.itemCategory.value)
                            putInt("index", it.index)
                        }
                    }.toTypedArray()
                )
            }
        })

    data class ViewItem(
        val itemListName: EventItemListName = EventItemListName.Default,
        val items: List<EventItem> = emptyList(),
        override var refer: EventRefer = MetricData.getRefer(),
        val preRefer: EventRefer = MetricData.getPreRefer(),
    ) : MetricEventooo(
        refer = refer,
        eventName = "view_item",
        block = {
            buildParams().builder(this)
            runtimeParams().builder(this)

            param("refer", refer.value)
//            param("pre_refer", preRefer.value)
            param("item_list_name", itemListName.value)
            param("items", items.map {
                Bundle().apply {
                    putString("item_id", processStringForMetric(it.itemId))
                    putString("item_name", processStringForMetric(it.itemName))
                    putString("item_category", it.itemCategory.value)
                    putInt("index", it.index)
                }
            }.toTypedArray())
        },
        params = {
            Bundle().apply {
                putString("event_name", "view_item")

                buildParams().bundle(this)
                runtimeParams().bundle(this)

                putString("refer", refer.value)
//                putString("pre_refer", preRefer.value)
                putString("item_list_name", itemListName.value)
                putParcelableArray(
                    "items", items.map {
                        Bundle().apply {
                            putString("item_id", processStringForMetric(it.itemId))
                            putString("item_name", processStringForMetric(it.itemName))
                            putString("item_category", it.itemCategory.value)
                            putInt("index", it.index)
                        }
                    }.toTypedArray()
                )
            }
        })

    data class Custom(
        override var refer: EventRefer = MetricData.getRefer(),
        override val eventName: String,
        override val block: suspend ParametersBuilder.() -> Unit,
        override val params: suspend () -> Bundle,
    ) : MetricEventooo(refer, eventName, block, params)
}