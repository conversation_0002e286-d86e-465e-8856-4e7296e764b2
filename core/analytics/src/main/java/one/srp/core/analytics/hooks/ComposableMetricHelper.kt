package one.srp.core.analytics.hooks

import androidx.compose.runtime.Composable
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import one.srp.core.analytics.types.EventRefer


@Composable
fun rememberMetricHelper(
    refer: EventRefer,
    instance: FirebaseAnalytics = Firebase.analytics,
): (event: MetricEvent) -> Unit {
    return remember {
        { event ->
            CoroutineScope(Dispatchers.IO).launch {
                sendMetric(event, instance)
            }
        }
    }
}