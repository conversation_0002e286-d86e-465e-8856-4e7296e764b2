package one.srp.core.analytics.events

import kotlinx.serialization.Serializable
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.types.METRIC_VERSION

@Serializable
sealed class MetricEvent(
    open val eventName: String,
    open val refer: EventRefer,

    open val itemListName: EventItemListName,
    open val items: List<EventItem>,

    open val method: EventMethod?,
    open val actionType: EventActionType?,

    open val platform: String,
    open val version: String,
    open val eventVersion: String = METRIC_VERSION,

    open val gensmoTimestamp: Long,
    open val abInfo: String,
    open val gensmoUserId: String,
    open val gensmoUserType: String,
    open val gensmoActiveId: String,
    open val gensmoUserSourceId: String,
    open val appsflyerId: String,
)