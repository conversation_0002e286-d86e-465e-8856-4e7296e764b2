# Consumer ProGuard rules for analytics module
# These rules will be applied to consumers of this library

# Keep all public analytics interfaces and classes
-keep public class one.srp.core.analytics.** { *; }

# Keep analytics event data classes
-keep class * implements one.srp.core.analytics.AnalyticsEvent { *; }

# Keep analytics provider implementations
-keep class * implements one.srp.core.analytics.providers.AnalyticsProvider { *; }

# Keep serialization for analytics events
-keepattributes *Annotation*
-keepclassmembers class * {
    @kotlinx.serialization.SerialName <fields>;
}
