package one.srp.core.network.clients

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit


fun createMetricClient(): OkHttpClient {
    val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    return OkHttpClient.Builder().apply {
        // 埋点请求：快速失败，避免影响用户体验
        connectTimeout(5, TimeUnit.SECONDS)
        callTimeout(15, TimeUnit.SECONDS)

        addInterceptor(loggingInterceptor)
    }.build()
}
