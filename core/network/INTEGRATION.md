# Core Network Module Integration Guide

## 概述

`core:network` 模块提供了 metric API 的网络功能，基于原有的 `app/src/main/java/one/srp/gensmo/data/remote/apis/Metric.kt` 文件实现。

## 集成步骤

### 1. 添加模块依赖

在需要使用 network 模块的地方（如 app 模块）的 `build.gradle.kts` 中添加依赖：

```kotlin
dependencies {
    implementation(project(":core:network"))
}
```

### 2. 使用 MetricApi

通过 Hilt 依赖注入使用 MetricApi：

```kotlin
@Inject
lateinit var metricApi: MetricApi

// 使用示例
suspend fun postMetric(eventData: RequestBody) {
    try {
        val response = metricApi.postApiMetric(eventData)
        if (response.isSuccessful) {
            // 处理成功响应
        } else {
            // 处理错误响应
        }
    } catch (e: Exception) {
        // 处理网络异常
    }
}
```

### 3. 配置 Base URL

在 `core/network/src/main/java/one/srp/core/network/di/NetworkModule.kt` 中修改 base URL：

```kotlin
.baseUrl("https://your-actual-api-base-url.com/")
```

### 4. ProGuard 配置

模块已包含必要的 ProGuard 规则，会自动应用到使用该模块的项目中。

## 迁移指南

如果你当前在使用原有的 `Metric.kt` 文件，可以按以下步骤迁移：

1. 添加 `core:network` 模块依赖
2. 将注入的 `MetricApi` 替换为来自 `one.srp.core.network.api.MetricApi`
3. 移除原有的 `one.srp.gensmo.data.remote.apis.Metric.kt` 文件（可选）

## 注意事项

- 模块使用 Hilt 进行依赖注入，确保你的应用已配置 Hilt
- 网络请求包含预配置的 CF-Access 头部信息
- 支持 HTTP 请求日志记录，便于调试
