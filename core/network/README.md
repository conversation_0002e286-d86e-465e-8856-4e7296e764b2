# Core Network Module

This module provides network functionality for the Gensmo application, specifically focused on metric API operations.

## Features

- **MetricApi**: Interface for posting metric data to the analytics endpoint
- **Dependency Injection**: Hilt-based DI configuration for network components
- **Logging**: HTTP request/response logging for debugging

## Components

### API
- `Metric<PERSON>pi`: Retrofit interface for metric data submission

### Dependency Injection
- `NetworkModule`: Provides configured Retrofit instances and API interfaces
- `@MetricRetrofit`: Qualifier for metric-specific network components

## Usage

The module is automatically configured through Hilt dependency injection. Simply inject `MetricApi` where needed:

```kotlin
@Inject
lateinit var metricApi: MetricApi
```

## Configuration

The base URL and other network configurations can be modified in `NetworkModule.kt`.
