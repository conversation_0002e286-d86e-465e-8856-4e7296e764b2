<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="100dp"
    android:viewportWidth="200"
    android:viewportHeight="100">
  <group>
    <clip-path
        android:pathData="M0,0h200v100h-200z"/>
    <path
        android:pathData="M67.78,29.3C69.81,24.79 68.45,19.48 64.49,16.51L54.67,9.13C49.26,5.05 41.46,7.06 38.67,13.24L12.94,70.45C10.16,76.64 13.83,83.8 20.47,85.15L32.51,87.6C37.36,88.59 42.24,86.09 44.27,81.58L44.45,81.18L95.94,86.72C101.93,89.06 108.41,85.92 110.92,80.33C113.44,74.74 111.49,67.81 105.76,64.88L67.46,30.02L67.78,29.3ZM102.59,69.62C102.59,69.62 102.59,69.62 102.6,69.63C102.6,69.63 102.6,69.63 102.6,69.63C102.6,69.63 102.6,69.63 102.59,69.62ZM97.4,81.19C97.4,81.19 97.39,81.19 97.39,81.2C97.39,81.2 97.39,81.2 97.39,81.2C97.39,81.2 97.39,81.2 97.39,81.2C97.39,81.2 97.39,81.2 97.39,81.2C97.39,81.19 97.39,81.19 97.39,81.19C97.39,81.19 97.39,81.19 97.4,81.19C97.4,81.19 97.4,81.19 97.4,81.19Z"
        android:fillColor="#ffffff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M61.1,21.02C62.95,22.4 63.58,24.88 62.64,26.99L39.12,79.26C38.18,81.37 35.9,82.53 33.64,82.07L21.6,79.63C18.5,78.99 16.78,75.65 18.08,72.76L43.82,15.55C45.12,12.67 48.76,11.73 51.28,13.63L61.1,21.02Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="56.05"
            android:startY="17.24"
            android:endX="70.16"
            android:endY="71.39"
            android:type="linear">
          <item android:offset="0" android:color="#FFFF5FA2"/>
          <item android:offset="0.4" android:color="#FFFF9433"/>
          <item android:offset="0.9" android:color="#FFFFD772"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M102.57,69.57L97.37,81.14L40.9,75.06C37.52,76.76 33.6,77.02 30.02,75.78L27.21,74.81C24.45,73.85 23.12,70.74 24.33,68.09L44.04,25.04C45.43,22 49.37,21.19 51.84,23.44L102.57,69.57Z"
        android:fillColor="#333333"/>
    <path
        android:pathData="M102.6,69.63L97.39,81.2L31.89,74.15C28.55,73.8 26.54,70.28 27.92,67.22L46.05,26.91C47.43,23.85 51.39,23.03 53.87,25.29L102.6,69.63Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="81.8"
            android:startY="38.77"
            android:endX="69.54"
            android:endY="89.88"
            android:type="linear">
          <item android:offset="0" android:color="#FFFF5FA2"/>
          <item android:offset="0.4" android:color="#FFFF9433"/>
          <item android:offset="0.9" android:color="#FFFFD772"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M97.71,81.34C94.69,79.99 93.41,76.29 94.85,73.1C96.29,69.9 99.9,68.41 102.92,69.77C105.94,71.13 107.22,74.82 105.78,78.01C104.34,81.21 100.73,82.7 97.71,81.34Z"
        android:fillColor="#333333"/>
    <path
        android:pathData="M101.6,72.27a3.53,3.35 114.22,1 0,-2.89 6.43a3.53,3.35 114.22,1 0,2.89 -6.43z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="101.8"
            android:startY="72.51"
            android:endX="100.64"
            android:endY="79.17"
            android:type="linear">
          <item android:offset="0" android:color="#FFFF5FA2"/>
          <item android:offset="0.4" android:color="#FFFF9433"/>
          <item android:offset="0.9" android:color="#FFFFD772"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M90,30C92.33,38 100.9,56 116.5,64"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.18,39C108.21,42.69 110.02,51.65 117.11,58.02"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:strokeWidth="1"
        android:pathData="M174.5,30.5m-2,0a2,2 0,1 1,4 0a2,2 0,1 1,-4 0"
        android:fillColor="#F9D745"
        android:strokeColor="#F9D745"/>
    <path
        android:strokeWidth="1"
        android:pathData="M144.63,14.4m-2.9,0a2.9,2.9 0,1 1,5.8 0a2.9,2.9 0,1 1,-5.8 0"
        android:fillColor="#F9D745"
        android:strokeColor="#F9D745"/>
    <path
        android:strokeWidth="1"
        android:pathData="M189.1,53.66C183.62,53.66 181.02,48.51 181.02,44C181.02,48.51 178.71,53.66 173.23,53.66C178.71,53.66 181.02,56.84 181.02,64.4C181.02,56.84 183.62,53.66 189.1,53.66Z"
        android:strokeLineJoin="round"
        android:fillColor="#ffffff"
        android:strokeColor="#ffffff"/>
    <path
        android:pathData="M142.07,27.83m-2.83,0a2.83,2.83 0,1 1,5.67 0a2.83,2.83 0,1 1,-5.67 0"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M127.04,27.02L125.52,29.82C124.95,30.88 124.09,31.74 123.04,32.32L120.25,33.86L123.05,35.38C124.1,35.95 124.97,36.82 125.55,37.87L127.08,40.66L128.6,37.86C129.17,36.8 130.04,35.94 131.09,35.36L133.87,33.82L131.08,32.3C130.02,31.73 129.15,30.86 128.58,29.81L127.04,27.02Z"
        android:fillColor="#FFDB3E"/>
    <path
        android:pathData="M148.78,88.73C160.78,86.78 168.91,75.47 166.96,63.48C165.01,51.49 153.71,43.35 141.71,45.3C129.72,47.26 121.58,58.56 123.54,70.55C125.49,82.54 136.79,90.68 148.78,88.73Z"
        android:strokeWidth="4"
        android:fillColor="#333333"
        android:strokeColor="#ffffff"/>
    <path
        android:pathData="M148.18,85C158.11,83.39 164.85,74.02 163.24,64.09C161.62,54.16 152.26,47.41 142.32,49.03C132.39,50.65 125.65,60.01 127.26,69.94C128.88,79.88 138.24,86.62 148.18,85Z"
        android:fillColor="#FFD772"/>
    <path
        android:pathData="M149.83,68.82L157.62,66.13"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M152.64,63.49L154.81,71.45"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M137.83,62.82L145.62,60.13"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M140.64,57.49L142.81,65.45"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M147.9,78.44L145.08,80.28L144.05,75.62L141.23,77.45L140.2,72.79L136.32,75.32L135.51,71"
        android:strokeLineJoin="round"
        android:strokeWidth="3"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M187.5,78.5m-1.5,0a1.5,1.5 0,1 1,3 0a1.5,1.5 0,1 1,-3 0"
        android:fillColor="#333333"/>
    <path
        android:pathData="M170.18,90.81L173.29,93.37C174.91,89.19 179.27,80.94 183.72,81.39"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M161.01,94.91C161.4,93.29 162.99,90.14 166.19,90.51"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#333333"
        android:strokeLineCap="round"/>
  </group>
</vector>
