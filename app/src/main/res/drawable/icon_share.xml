<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <group>
    <clip-path
        android:pathData="M0,0h24v24h-24z"/>
    <path
        android:pathData="M18.375,0H5.625C2.518,0 0,2.518 0,5.625V18.375C0,21.482 2.518,24 5.625,24H18.375C21.482,24 24,21.482 24,18.375V5.625C24,2.518 21.482,0 18.375,0Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="0"
            android:startY="0"
            android:endX="28.968"
            android:endY="11.701"
            android:type="linear">
          <item android:offset="0" android:color="#FF0224FF"/>
          <item android:offset="1" android:color="#FFB862F1"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.136,12.785C18.136,16.55 15.084,19.603 11.319,19.603C7.553,19.603 4.501,16.55 4.501,12.785C4.501,9.019 7.553,5.967 11.319,5.967"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M16.091,10.739L19.5,7.331L16.091,3.922"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M11.318,14.148V11.14C11.318,9.036 13.024,7.331 15.127,7.331C16.126,7.331 17.188,7.331 18.136,7.331"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
</vector>
