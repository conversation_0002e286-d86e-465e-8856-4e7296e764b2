<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="133dp"
    android:height="115dp"
    android:viewportWidth="133"
    android:viewportHeight="115">
  <group>
    <clip-path
        android:pathData="M0.5,0h132v115h-132z"/>
    <path
        android:pathData="M68.46,112.16C90.5,112.16 108.36,111.05 108.36,109.67C108.36,108.3 90.5,107.18 68.46,107.18C46.42,107.18 28.56,108.3 28.56,109.67C28.56,111.05 46.42,112.16 68.46,112.16Z"
        android:fillColor="#F2F2F2"/>
    <path
        android:pathData="M87.08,43.79C77.96,43.79 68.68,43.42 60,41C51.49,38.64 43.66,34.06 36.69,28.75C32.13,25.3 27.98,22.54 22.06,22.96C16.26,23.27 10.72,25.43 6.24,29.13C-1.3,35.73 -0.16,47.96 2.85,56.53C7.38,69.45 21.17,78.46 32.89,84.29C46.43,91.05 61.31,94.98 76.22,97.24C89.29,99.23 106.09,100.67 117.41,92.14C127.82,84.29 130.67,66.38 128.12,54.29C127.5,50.72 125.6,47.5 122.77,45.23C115.46,39.88 104.56,43.46 96.35,43.63C93.3,43.69 90.19,43.77 87.08,43.79Z"
        android:fillColor="#F2F2F2"/>
    <path
        android:pathData="M101.2,94.08C101.79,94.08 102.27,93.6 102.27,93.01C102.27,92.42 101.79,91.95 101.2,91.95C100.62,91.95 100.14,92.42 100.14,93.01C100.14,93.6 100.62,94.08 101.2,94.08Z"
        android:fillColor="#CFCFCF"/>
    <path
        android:pathData="M97.66,65.7C98.24,65.7 98.72,65.23 98.72,64.64C98.72,64.05 98.24,63.57 97.66,63.57C97.07,63.57 96.59,64.05 96.59,64.64C96.59,65.23 97.07,65.7 97.66,65.7Z"
        android:fillColor="#CFCFCF"/>
    <path
        android:pathData="M93.76,18.97V23.65"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.42,21.31H96.1"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M44.41,93.81V98.49"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M42.08,96.15H46.75"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M63.57,86.15C62.73,82.77 61.51,78.56 63.57,76.15"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M75.21,74.6C72.99,74.6 71.9,73.96 69.46,70.16"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M66.09,34.15C66.93,37.53 64.6,42.09 62.53,44.5"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M46.36,41.87C47.93,42.79 51.29,44.97 52.11,46.31"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M51.78,28.77C55.78,31.81 58.53,41.47 57.25,46.87"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.58,90.44C126.79,88.2 124.36,83.42 120.96,82.17C116.72,80.6 118.16,89.46 120.31,90.07C122.3,90.64 125.07,85.35 123.73,80.36C122.66,76.37 117.95,72.57 115.74,71.17"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.29,69.14L114,66.66C112.17,65.81 109.99,66.6 109.14,68.43L108.81,69.13C107.95,70.96 108.74,73.13 110.57,73.99L115.87,76.47C117.7,77.32 119.87,76.53 120.73,74.7L121.06,74C121.91,72.17 121.12,70 119.29,69.14Z"
        android:fillColor="#BABABA"/>
    <path
        android:pathData="M101.32,54.72L83.85,46.55C82.94,46.12 81.85,46.52 81.42,47.43L81.33,47.62C80.91,48.53 81.3,49.62 82.22,50.05L99.69,58.22C100.61,58.65 101.69,58.25 102.12,57.34L102.21,57.15C102.63,56.24 102.24,55.15 101.32,54.72Z"
        android:fillColor="#BABABA"/>
    <path
        android:pathData="M95.23,67.75L77.76,59.58C76.84,59.15 75.75,59.55 75.33,60.46L75.24,60.65C74.81,61.56 75.21,62.65 76.12,63.08L93.6,71.25C94.51,71.68 95.6,71.28 96.03,70.37L96.11,70.18C96.54,69.27 96.14,68.18 95.23,67.75Z"
        android:fillColor="#BABABA"/>
    <path
        android:pathData="M118.74,61.62L104.58,55C102.75,54.15 100.57,54.94 99.72,56.76L93.67,69.68C92.82,71.51 93.61,73.69 95.44,74.54L109.6,81.17C111.43,82.02 113.61,81.23 114.46,79.4L120.5,66.48C121.36,64.66 120.57,62.48 118.74,61.62Z"
        android:fillColor="#D2D2D2"/>
    <path
        android:pathData="M111.4,56.88L97.24,50.25C95.41,49.4 93.24,50.19 92.38,52.02L85.17,67.43C84.32,69.26 85.11,71.44 86.93,72.29L101.1,78.91C102.93,79.77 105.1,78.98 105.96,77.15L113.17,61.74C114.02,59.91 113.23,57.73 111.4,56.88Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#ffffff"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.32,56.25L115.21,56.2C113.99,55.63 112.54,56.15 111.97,57.37L101.87,78.97C101.3,80.19 101.82,81.64 103.04,82.21L103.16,82.26C104.38,82.83 105.83,82.3 106.4,81.08L116.49,59.49C117.06,58.27 116.54,56.82 115.32,56.25Z"
        android:fillColor="#BABABA"/>
    <path
        android:pathData="M31.05,80.89C24.1,86.27 17.94,88.81 13.35,85.66C8.76,82.52 9.15,78.53 10.72,76.7C12.3,74.88 14.26,76.78 12.95,78.08C11.17,79.86 4.69,79.62 4.04,73.85"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#00000000"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M33.04,71.3L28.1,75.36C26.39,76.76 26.14,79.28 27.55,80.98L28.08,81.64C29.49,83.34 32,83.59 33.71,82.19L38.65,78.13C40.36,76.73 40.6,74.21 39.2,72.5L38.66,71.85C37.26,70.14 34.74,69.89 33.04,71.3Z"
        android:fillColor="#BABABA"/>
    <path
        android:pathData="M38.13,54.47L27.21,63.44C25.51,64.84 25.26,67.36 26.66,69.07L38.48,83.44C39.88,85.15 42.4,85.39 44.1,83.99L55.02,75.02C56.73,73.61 56.98,71.1 55.57,69.39L43.76,55.02C42.36,53.31 39.84,53.06 38.13,54.47Z"
        android:strokeLineJoin="round"
        android:strokeWidth="1.08918"
        android:fillColor="#ffffff"
        android:strokeColor="#BABABA"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M39.62,50.14L39.52,50.23C38.38,51.16 38.22,52.84 39.15,53.98L55.7,74.12C56.63,75.25 58.31,75.42 59.45,74.48L59.55,74.4C60.69,73.46 60.85,71.78 59.92,70.65L43.37,50.51C42.44,49.37 40.76,49.21 39.62,50.14Z"
        android:fillColor="#BABABA"/>
  </group>
</vector>
