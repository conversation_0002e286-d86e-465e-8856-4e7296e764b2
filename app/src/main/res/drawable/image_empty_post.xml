<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="118dp"
    android:height="114dp"
    android:viewportWidth="118"
    android:viewportHeight="114">
  <path
      android:pathData="M79.29,44.98C70.94,44.98 62.43,44.64 54.48,42.43C46.68,40.26 39.51,36.06 33.12,31.2C28.94,28.03 25.14,25.52 19.71,25.9C14.4,26.18 9.32,28.16 5.22,31.56C-1.69,37.61 -0.65,48.81 2.12,56.66C6.27,68.51 18.9,76.76 29.64,82.11C42.04,88.31 55.68,91.9 69.34,93.98C81.31,95.79 96.7,97.12 107.08,89.29C116.61,82.11 119.23,65.7 116.89,54.62C116.32,51.34 114.58,48.38 111.99,46.3C105.29,41.41 95.31,44.67 87.78,44.84C84.99,44.9 82.14,44.96 79.29,44.98Z"
      android:fillColor="#F2F2F2"/>
  <path
      android:pathData="M58.85,113.39C79.08,113.39 95.49,112.37 95.49,111.11C95.49,109.84 79.08,108.82 58.85,108.82C38.62,108.82 22.22,109.84 22.22,111.11C22.22,112.37 38.62,113.39 58.85,113.39Z"
      android:fillColor="#F2F2F2"/>
  <path
      android:pathData="M104.57,13.85C105.11,13.85 105.54,13.41 105.54,12.87C105.54,12.33 105.11,11.89 104.57,11.89C104.03,11.89 103.59,12.33 103.59,12.87C103.59,13.41 104.03,13.85 104.57,13.85Z"
      android:fillColor="#CFCFCF"/>
  <path
      android:pathData="M39.51,5.65C40.05,5.65 40.49,5.21 40.49,4.67C40.49,4.13 40.05,3.7 39.51,3.7C38.97,3.7 38.53,4.13 38.53,4.67C38.53,5.21 38.97,5.65 39.51,5.65Z"
      android:fillColor="#CFCFCF"/>
  <path
      android:pathData="M51.34,98.8C51.88,98.8 52.31,98.36 52.31,97.82C52.31,97.28 51.88,96.84 51.34,96.84C50.8,96.84 50.36,97.28 50.36,97.82C50.36,98.36 50.8,98.8 51.34,98.8Z"
      android:fillColor="#CFCFCF"/>
  <path
      android:strokeWidth="1"
      android:pathData="M77.44,1V5.29"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M75.3,3.15H79.59"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M7.53,19.07V23.36"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M5.37,21.21H9.67"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M10.76,78.87V83.17"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M8.6,81.02H12.9"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M99.43,34.86V39.15"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M97.28,37.01H101.57"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M69.52,9.82L19.43,16.4C16.18,16.82 13.89,19.8 14.32,23.05L21.96,81.21C22.38,84.45 25.36,86.74 28.61,86.31L78.7,79.74C81.94,79.31 84.23,76.33 83.8,73.08L76.17,14.92C75.74,11.68 72.76,9.39 69.52,9.82Z"
      android:fillColor="#D2D2D2"/>
  <path
      android:strokeWidth="1"
      android:pathData="M84.11,18.35H33.51C30.23,18.35 27.57,21.01 27.57,24.29V83.03C27.57,86.32 30.23,88.97 33.51,88.97H84.11C87.39,88.97 90.05,86.32 90.05,83.03V24.29C90.05,21.01 87.39,18.35 84.11,18.35Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M35.13,64.83L45.25,48.33C45.35,48.09 45.52,47.88 45.73,47.73C45.95,47.59 46.2,47.51 46.47,47.51C46.73,47.51 46.98,47.59 47.2,47.73C47.41,47.88 47.58,48.09 47.68,48.33L53.42,57.68C53.52,57.92 53.69,58.13 53.9,58.27C54.12,58.42 54.37,58.5 54.63,58.5C54.89,58.5 55.15,58.42 55.37,58.27C55.58,58.13 55.75,57.92 55.85,57.68L65.67,41.71C65.76,41.47 65.93,41.26 66.15,41.11C66.36,40.97 66.62,40.89 66.88,40.89C67.14,40.89 67.39,40.97 67.61,41.11C67.83,41.26 67.99,41.47 68.09,41.71L82.5,65.2L35.13,64.83Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M55.31,46.11C57.07,46.11 58.49,44.68 58.49,42.93C58.49,41.17 57.07,39.74 55.31,39.74C53.55,39.74 52.12,41.17 52.12,42.93C52.12,44.68 53.55,46.11 55.31,46.11Z"
      android:strokeLineJoin="round"
      android:fillColor="#ffffff"
      android:strokeColor="#BABABA"
      android:strokeLineCap="round"/>
</vector>
