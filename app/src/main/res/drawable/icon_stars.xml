<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="36dp"
    android:height="36dp"
    android:viewportWidth="36"
    android:viewportHeight="36">
  <path
      android:pathData="M4.849,19.032C4.415,18.936 4.416,18.316 4.851,18.222L9.142,17.291C12.32,16.602 14.802,14.12 15.491,10.943L16.421,6.651C16.515,6.216 17.135,6.215 17.232,6.65L18.188,10.975C18.886,14.136 21.359,16.601 24.522,17.289L28.802,18.222C29.237,18.316 29.238,18.935 28.804,19.032L24.489,19.99C21.344,20.688 18.888,23.144 18.189,26.289L17.231,30.604C17.135,31.038 16.516,31.037 16.421,30.603L15.489,26.322C14.8,23.16 12.335,20.687 9.175,19.988L4.849,19.032Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.442"
          android:startY="7.921"
          android:endX="-1.88"
          android:endY="38.748"
          android:type="linear">
        <item android:offset="0" android:color="#FF4924EF"/>
        <item android:offset="1" android:color="#FFF3B0BD"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M3.799,26.503C3.611,26.462 3.612,26.194 3.8,26.153L5.747,25.731C6.776,25.507 7.58,24.703 7.804,23.674L8.226,21.727C8.267,21.539 8.535,21.538 8.576,21.726L9.009,23.684C9.235,24.708 10.037,25.507 11.061,25.73L13.003,26.153C13.19,26.194 13.191,26.461 13.003,26.503L11.051,26.937C10.032,27.163 9.236,27.959 9.01,28.978L8.576,30.93C8.534,31.118 8.267,31.117 8.226,30.93L7.803,28.988C7.58,27.964 6.781,27.162 5.757,26.936L3.799,26.503Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.442"
          android:startY="7.921"
          android:endX="-1.88"
          android:endY="38.748"
          android:type="linear">
        <item android:offset="0" android:color="#FF4924EF"/>
        <item android:offset="1" android:color="#FFF3B0BD"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M20.031,11.063C19.793,11.01 19.793,10.671 20.031,10.62L22.498,10.085C23.802,9.802 24.82,8.783 25.103,7.479L25.638,5.013C25.69,4.775 26.029,4.774 26.081,5.012L26.63,7.492C26.917,8.79 27.932,9.801 29.23,10.084L31.689,10.62C31.926,10.671 31.927,11.01 31.69,11.063L29.216,11.612C27.925,11.899 26.917,12.907 26.631,14.198L26.081,16.671C26.029,16.909 25.69,16.908 25.638,16.67L25.103,14.211C24.82,12.913 23.808,11.898 22.511,11.611L20.031,11.063Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="29.442"
          android:startY="7.921"
          android:endX="-1.88"
          android:endY="38.748"
          android:type="linear">
        <item android:offset="0" android:color="#FF4924EF"/>
        <item android:offset="1" android:color="#FFF3B0BD"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
