<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="50dp"
    android:height="36dp"
    android:viewportWidth="50"
    android:viewportHeight="36">
  <path
      android:strokeWidth="1"
      android:pathData="M20,0.5L32,0.5A17.5,17.5 0,0 1,49.5 18L49.5,18A17.5,17.5 0,0 1,32 35.5L20,35.5A17.5,17.5 0,0 1,2.5 18L2.5,18A17.5,17.5 0,0 1,20 0.5z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="26"
          android:startY="0"
          android:endX="26"
          android:endY="36"
          android:type="linear">
        <item android:offset="0" android:color="#FFF6F6F6"/>
        <item android:offset="1" android:color="#FFE9E9E9"/>
      </gradient>
    </aapt:attr>
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="2"
          android:startY="9.5"
          android:endX="46.5"
          android:endY="27.5"
          android:type="linear">
        <item android:offset="0" android:color="#FFC1C1C1"/>
        <item android:offset="0.255" android:color="#FFFEFFFF"/>
        <item android:offset="0.822" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFC1C1C1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16,17.899L18.628,16.735C21.351,15.527 23.527,13.351 24.735,10.628L25.899,8L27.064,10.628C28.272,13.351 30.448,15.527 33.171,16.735L35.799,17.899L33.171,19.064C30.448,20.271 28.272,22.448 27.064,25.171L25.899,27.799L24.735,25.171C23.527,22.448 21.351,20.271 18.628,19.064L16,17.899Z"
      android:fillColor="#BB2649"/>
  <path
      android:pathData="M16,17.899L18.628,16.735C21.351,15.527 23.527,13.351 24.735,10.628L25.899,8L27.064,10.628C28.272,13.351 30.448,15.527 33.171,16.735L35.799,17.899L33.171,19.064C30.448,20.271 28.272,22.448 27.064,25.171L25.899,27.799L24.735,25.171C23.527,22.448 21.351,20.271 18.628,19.064L16,17.899Z"
      android:strokeWidth="0.5"
      android:fillColor="#00000000">
    <aapt:attr name="android:strokeColor">
      <gradient 
          android:startX="20.214"
          android:startY="12.214"
          android:endX="31.893"
          android:endY="22.608"
          android:type="linear">
        <item android:offset="0" android:color="#FFC1C1C1"/>
        <item android:offset="0.316" android:color="#FFFEFFFF"/>
        <item android:offset="0.777" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#FFC1C1C1"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M30.74,11.002L31.988,10.449C32.442,10.248 32.804,9.885 33.006,9.431L33.559,8.184L34.112,9.431C34.313,9.885 34.676,10.248 35.13,10.449L36.377,11.002L35.13,11.555C34.676,11.757 34.313,12.119 34.112,12.573L33.559,13.821L33.006,12.573C32.804,12.119 32.442,11.757 31.988,11.555L30.74,11.002Z"
      android:fillColor="#BB2649"/>
</vector>
