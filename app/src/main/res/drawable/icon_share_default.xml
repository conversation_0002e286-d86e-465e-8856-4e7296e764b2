<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="41dp"
    android:height="40dp"
    android:viewportWidth="41"
    android:viewportHeight="40">
  <path
      android:pathData="M20.491,1.004L20.491,1.004A18.997,18.997 0,0 1,39.489 20.001L39.489,20.001A18.997,18.997 0,0 1,20.491 38.999L20.491,38.999A18.997,18.997 0,0 1,1.494 20.001L1.494,20.001A18.997,18.997 0,0 1,20.491 1.004z"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#6302FC"/>
  <path
      android:pathData="M20.49,21.112C21.103,21.112 21.601,20.614 21.601,20.001C21.601,19.387 21.103,18.89 20.49,18.89C19.876,18.89 19.379,19.387 19.379,20.001C19.379,20.614 19.876,21.112 20.49,21.112Z"
      android:fillColor="#6302FC"/>
  <path
      android:pathData="M28.266,21.112C28.88,21.112 29.377,20.614 29.377,20.001C29.377,19.387 28.88,18.89 28.266,18.89C27.653,18.89 27.155,19.387 27.155,20.001C27.155,20.614 27.653,21.112 28.266,21.112Z"
      android:fillColor="#6302FC"/>
  <path
      android:pathData="M12.713,21.112C13.326,21.112 13.824,20.614 13.824,20.001C13.824,19.387 13.326,18.89 12.713,18.89C12.099,18.89 11.602,19.387 11.602,20.001C11.602,20.614 12.099,21.112 12.713,21.112Z"
      android:fillColor="#6302FC"/>
  <path
      android:pathData="M20.49,21.112C21.103,21.112 21.601,20.614 21.601,20.001C21.601,19.387 21.103,18.89 20.49,18.89C19.876,18.89 19.379,19.387 19.379,20.001C19.379,20.614 19.876,21.112 20.49,21.112Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#6302FC"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M28.266,21.112C28.88,21.112 29.377,20.614 29.377,20.001C29.377,19.387 28.88,18.89 28.266,18.89C27.653,18.89 27.155,19.387 27.155,20.001C27.155,20.614 27.653,21.112 28.266,21.112Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#6302FC"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M12.713,21.112C13.326,21.112 13.824,20.614 13.824,20.001C13.824,19.387 13.326,18.89 12.713,18.89C12.099,18.89 11.602,19.387 11.602,20.001C11.602,20.614 12.099,21.112 12.713,21.112Z"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#6302FC"
      android:strokeLineCap="round"/>
</vector>
