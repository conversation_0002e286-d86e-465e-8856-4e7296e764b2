package one.srp.gensmo.viewmodel.user

import android.app.Activity
import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.R
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.screens.user.login.UserInfo
import one.srp.gensmo.utils.auth.AuthManager
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authManager: AuthManager
) : ViewModel() {

    private val _uiState = MutableStateFlow<LoginUiState>(LoginUiState.Initial)
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()

    private var onLoginSuccess: () -> Unit = {}

    init {
        checkAuthState()
    }

    fun checkAuthState() {
        viewModelScope.launch {
            try {
                val isLoggedIn = UserDataStoreManager.isUserLoggedIn()
                _uiState.value = if (isLoggedIn) {
                    val userInfo = authManager.getUserInfo()
                    LoginUiState.LoggedIn(userInfo!!)
                } else {
                    LoginUiState.LoggedOut
                }
            } catch (e: Exception) {
                _uiState.value = LoginUiState.Error("检查登录状态失败：${e.localizedMessage}")
            }
        }
    }

    fun startLogin(context: Context) {
        viewModelScope.launch {
            val clientId = context.getString(R.string.default_web_client_id)
            authManager.startGoogleLogin(
                clientId = clientId,
                activity = context as Activity,
                onSuccess = {
                    checkAuthState()
                    onLoginSuccess()
                },
                onError = { message ->
                    _uiState.value = LoginUiState.Error(message)
                },
                onLoading = {
                    _uiState.value = LoginUiState.Loading
                }
            )
        }
    }

    fun startAppleLogin(context: Context) {
        viewModelScope.launch {
            authManager.startAppleLogin(
                activity = context as Activity,
                onSuccess = {
                    checkAuthState()
                    onLoginSuccess()
                },
                onError = { message ->
                    _uiState.value = LoginUiState.Error(message)
                },
                onLoading = {
                    _uiState.value = LoginUiState.Loading
                }
            )
        }
    }

    fun signOut() {
        viewModelScope.launch {
            try {
                authManager.signOut()
                _uiState.value = LoginUiState.LoggedOut
            } catch (e: Exception) {
                _uiState.value = LoginUiState.Error("登出失败：${e.localizedMessage}")
            }
        }
    }

    fun setOnLoginSuccess(callback: () -> Unit) {
        onLoginSuccess = callback
    }
}

sealed class LoginUiState {
    data object Initial : LoginUiState()
    data object LoggedOut : LoginUiState()
    data object Loading : LoginUiState()
    data class LoggedIn(val userInfo: UserInfo) : LoginUiState()
    data class Error(val message: String) : LoginUiState()
}