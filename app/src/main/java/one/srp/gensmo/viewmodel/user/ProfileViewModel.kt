package one.srp.gensmo.viewmodel.user

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import androidx.lifecycle.DefaultLifecycleObserver
import kotlinx.coroutines.launch
import timber.log.Timber
import androidx.lifecycle.viewModelScope
import one.srp.gensmo.utils.auth.AuthManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.data.remote.AccountService
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val authManager: AuthManager
) : ViewModel(), DefaultLifecycleObserver {
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()
    
    private val _userName = MutableStateFlow("Guest")
    val userName: StateFlow<String> = _userName.asStateFlow()
    
    private val _userAvatar = MutableStateFlow<String?>("icon_random_thumb")
    val userAvatar: StateFlow<String?> = _userAvatar.asStateFlow()

    fun logout() {
        viewModelScope.launch {
            try {
                authManager.signOut()
                _isLoggedIn.value = false  // 更新登录状态
                _userName.value = "Guest"   // 重置用户名
                _userAvatar.value = "icon_unregister"  // 设置为未注册图标
                UserDataStoreManager.initializeGuestTokenIfNeeded()
            } catch (e: Exception) {
                Timber.e(e, "登出失败")
            }
        }
    }

    fun getUserInfo() {
        viewModelScope.launch {
            val userInfo = UserDataStoreManager.getUserInfo()
            if (userInfo != null) {
                _userName.value = userInfo.name ?: "Hello"
                _userAvatar.value = userInfo.avatar ?: "icon_random_thumb"
            } else {
                _userName.value = "Guest"
                _userAvatar.value = "icon_unregister"
            }
        }
    }

    fun checkLoginStatus() {
        viewModelScope.launch {
            try {
                val isUserLoggedIn = UserDataStoreManager.isUserLoggedIn()
                _isLoggedIn.value = isUserLoggedIn
                Timber.d("用户登录状态: $isUserLoggedIn")
            } catch (e: Exception) {
                Timber.e(e, "检查登录状态失败")
                _isLoggedIn.value = false
            }
        }
    }

    fun deleteAccount() {
        viewModelScope.launch {
            try {
                val response = AccountService.api.deleteUser()
                if (response.isSuccessful) {
                    Timber.d("账户删除成功")
                    logout()
                } else {
                    Timber.e("账户删除失败: ${response.errorBody()?.string()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "删除账户失败")
            }
        }
    }
    
}