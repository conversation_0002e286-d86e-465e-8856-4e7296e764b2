package one.srp.gensmo.viewmodel

import android.content.Context
import android.content.pm.PackageManager
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.lifecycle.ViewModel
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeWebView
import com.smallbuer.jsbridge.core.CallBackFunction
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import one.srp.gensmo.utils.bridge.JSBridgeManager
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject
import android.content.ContentValues
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Environment
import android.provider.MediaStore
import android.util.Base64
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import androidx.lifecycle.viewModelScope
import org.json.JSONObject
import one.srp.gensmo.ui.components.notification.ToastManager
import one.srp.gensmo.ui.components.notification.ToastType

/**
 * Activity页面的 ViewModel
 * 负责管理Activity页面的webview状态和业务逻辑
 */
@HiltViewModel
class ActivityViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val jsBridgeManager: JSBridgeManager
) : ViewModel() {
    
    // 使用弱引用来避免内存泄漏
    private var webViewRef: WeakReference<BridgeWebView>? = null
    
    init {
        Timber.d("ActivityViewModel 已初始化")
    }
    
    // 创建并初始化WebView
    fun createWebView(): BridgeWebView {
        webViewRef?.get()?.let {
            return it
        }
        
        val newWebView = BridgeWebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.useWideViewPort = true
            settings.loadWithOverviewMode = true
            settings.cacheMode = WebSettings.LOAD_DEFAULT
            settings.setSupportZoom(true)
            settings.builtInZoomControls = true
            settings.displayZoomControls = false
        }
        webViewRef = WeakReference(newWebView)
        Timber.d("Activity WebView已在ViewModel中创建")
        return newWebView
    }

    fun getWebView(): BridgeWebView? {
        return webViewRef?.get()
    }
    
    // 初始化JSBridge并加载URL
    fun initializeWebView(activityUrl: String, onInitialized: () -> Unit) {
        val webView = createWebView()
        webView.addHandlerLocal("getAppVersion", AppVersionHandler())
        webView.addHandlerLocal("saveImageToGallery", SaveImageHandler())
        webView.addHandlerLocal("shareActivity", ShareHandler())
        
        jsBridgeManager.apply {
            Timber.d("ActivityViewModel JSBridgeManager配置已更新")
            
            init(webView) {
                Timber.d("loadUrl: $activityUrl")
                webView.loadUrl(activityUrl)
                Timber.d("Activity页面已加载")
                onInitialized()
            }
        }
    }

    private inner class AppVersionHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("Activity AppVersionHandler已触发")
            val appVersion = try {
                val packageInfo = context?.packageManager?.getPackageInfo(context.packageName, 0)
                packageInfo?.versionName ?: "unknown"
            } catch (e: PackageManager.NameNotFoundException) {
                Timber.e(e, "获取应用版本号失败")
                "unknown"
            }
            
            function?.onCallBack(appVersion)
            Timber.d("返回应用版本: $appVersion")
        }
    }

    private inner class SaveImageHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("SaveImageHandler已触发，接收到数据: ${data?.take(100)}...")
            
            if (data.isNullOrEmpty()) {
                function?.onCallBack("""{"success": false, "error": "图片数据为空"}""")
                return
            }
            
            viewModelScope.launch {
                try {
                    // 解析JSON对象，提取imageData字段
                    val imageData = try {
                        val jsonObject = JSONObject(data)
                        jsonObject.optString("imageData", "")
                    } catch (e: Exception) {
                        // 如果不是JSON格式，直接使用原始数据
                        Timber.d("数据不是JSON格式，直接使用原始数据")
                        data
                    }
                    
                    if (imageData.isEmpty()) {
                        function?.onCallBack("""{"success": false, "error": "未找到有效的图片数据"}""")
                        return@launch
                    }
                    
                    val result = saveImageToGallery(imageData)
                    function?.onCallBack(result)
                } catch (e: Exception) {
                    Timber.e(e, "保存图片时发生异常")
                    function?.onCallBack("""{"success": false, "error": "${e.message}"}""")
                }
            }
        }
        
        private suspend fun saveImageToGallery(imageData: String): String = withContext(Dispatchers.IO) {
            try {
                // 去掉base64前缀
                val base64Data = imageData.replace("data:image/png;base64,", "")
                    .replace("data:image/jpeg;base64,", "")
                    .replace("data:image/jpg;base64,", "")
                
                // 解码base64数据
                val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)
                val bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                    ?: return@withContext """{"success": false, "error": "无法解码图片数据"}"""
                
                val success = saveImageToGalleryWithMediaStore(bitmap)
                
                if (success) {
                    Timber.d("图片保存成功")
                    // 显示成功提示
                    ToastManager.show(
                        text = "Image saved to gallery",
                        type = ToastType.SUCCESS
                    )
                    """{"success": true, "message": "图片已保存到相册"}"""
                } else {
                    """{"success": false, "error": "保存图片失败"}"""
                }
            } catch (e: Exception) {
                Timber.e(e, "保存图片过程中发生错误")
                """{"success": false, "error": "${e.message}"}"""
            }
        }
        
        // 使用MediaStore API保存图片
        private fun saveImageToGalleryWithMediaStore(bitmap: Bitmap): Boolean {
            return try {
                val resolver = context.contentResolver
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, "IMG_${System.currentTimeMillis()}.png")
                    put(MediaStore.MediaColumns.MIME_TYPE, "image/png")
                    put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
                }
                
                val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                uri != null && resolver.openOutputStream(uri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                } != null
            } catch (e: Exception) {
                Timber.e(e, "使用MediaStore保存图片失败")
                false
            }
        }
    }

    private inner class ShareHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("ShareHandler已触发，接收到数据: ${data?.take(100)}...")
            
            if (data.isNullOrEmpty()) {
                function?.onCallBack("""{"success": false, "error": "分享数据为空"}""")
                return
            }
            
            try {
                // 解析JSON数据获取title和link
                val jsonObject = JSONObject(data)
                val title = jsonObject.optString("text", "")
                val link = jsonObject.optString("url", "")
                
                if (link.isEmpty()) {
                    function?.onCallBack("""{"success": false, "error": "分享链接为空"}""")
                    return
                }
                
                // 构造分享内容
                val shareText = if (title.isNotEmpty()) {
                    "$title\n$link"
                } else {
                    link
                }
                
                // 调用分享功能
                context?.let { ctx ->
                    val intent = android.content.Intent(android.content.Intent.ACTION_SEND).apply {
                        type = "text/plain"
                        putExtra(android.content.Intent.EXTRA_TEXT, shareText)
                        if (title.isNotEmpty()) {
                            putExtra(android.content.Intent.EXTRA_TITLE, title)
                        }
                    }
                    val chooserIntent = android.content.Intent.createChooser(intent, "分享到")
                    chooserIntent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                    ctx.startActivity(chooserIntent)
                    
                    Timber.d("分享面板已打开，标题: $title, 链接: $link")
                    function?.onCallBack("""{"success": true, "message": "分享面板已打开"}""")
                } ?: run {
                    function?.onCallBack("""{"success": false, "error": "Context为空"}""")
                }
                
            } catch (e: Exception) {
                Timber.e(e, "解析分享数据时发生错误")
                function?.onCallBack("""{"success": false, "error": "解析数据失败: ${e.message}"}""")
            }
        }
    }
   
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        webViewRef?.clear()
        webViewRef = null
        jsBridgeManager.release()
        Timber.d("ActivityViewModel已清除，WebView引用已释放")
    }
} 