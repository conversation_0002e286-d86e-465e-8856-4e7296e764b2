package one.srp.gensmo.viewmodel.tryon

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject
import timber.log.Timber
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import one.srp.gensmo.data.repository.TryOnRepository
import one.srp.gensmo.data.model.UpdateReplicaParams

@HiltViewModel
class ClosetViewModel @Inject constructor(
    private val tryOnRepository: TryOnRepository,
    asyncTaskManager: AsyncTaskManager
) : ViewModel() {
    
    data class UiState(
        val isLoading: Boolean = false,
        val error: String? = null,
        val previewedModelUrl: String? = null,
        val previewedModelId: String? = null,
        val isTaskLoading: Boolean = false,
        val progress: AsyncTaskManager.Progress = AsyncTaskManager.Progress(0, 0),
        val isLoadingMore: Boolean = false,
        val showTaskLoadingCard: Boolean = false,
    )

    val taskStatus: StateFlow<String?> = asyncTaskManager.replicaTaskStatus
    val previewUrl: StateFlow<String?> = asyncTaskManager.previewUrl
    private var lastStatus: String? = null

    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    private val _currentModelId = MutableStateFlow<String?>(null)
    val currentModelId: StateFlow<String?> = _currentModelId.asStateFlow()
    
    // 从 Repository 获取历史记录数据
    val replicaHistory = tryOnRepository.replicaHistory
    val hasMoreReplicaHistory = tryOnRepository.hasMoreReplicaHistory

    init {
        Timber.d("ClosetViewModel init")
        viewModelScope.launch {
            asyncTaskManager.isTaskLoading.collect { isTaskLoading ->
                _uiState.update { it.copy(isTaskLoading = isTaskLoading) }
            }
        }
        viewModelScope.launch {
            asyncTaskManager.progress.collect { progress ->
                _uiState.update { it.copy(progress = progress) }
            }
        }
        viewModelScope.launch {
            replicaHistory.collect { history ->
                if (history.isEmpty() && _uiState.value.isTaskLoading) {
                    _uiState.update { it.copy(showTaskLoadingCard = true) }
                }
            }
        }
        observeTaskStatus()
    }

    fun getReplica() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            try {
                val response = TryOnService.api.getReplica()
                if (response.isSuccessful) {
                    if (response.body() == null) {
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                previewedModelUrl = null,
                                previewedModelId = null
                            )
                        }
                        UserDataStoreManager.saveModelInfo(null, null)
                        _currentModelId.value = null
                    } else {
                        val body = response.body()!!
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                previewedModelUrl = body.modelUrl,
                                previewedModelId = body.modelId
                            )
                        }
                        UserDataStoreManager.saveModelInfo(body.modelUrl, body.modelId)
                        _currentModelId.value = body.modelId
                    }
                } else {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = "获取数据失败: ${response.message()}"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "发生错误: ${e.message}"
                    )
                }
            }
        }
    }

    fun deleteReplicaModel(modelId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            try {
                val response = TryOnService.api.deleteReplicaModel(modelId)
                if (response.isSuccessful) {
                    // 获取删除前的历史记录，找到被删除模型的相邻模型
                    val history = replicaHistory.first()
                    val adjacentModel = history.flatMap { it.replicaList }
                        .windowed(2, 1, false)
                        .find { it[0].modelId == modelId || it[1].modelId == modelId }
                        ?.let { if (it[0].modelId == modelId) it[1] else it[0] }
                    
                    // 从历史记录中删除模型
                    tryOnRepository.removeModelFromHistory(modelId)
                    
                    if (modelId == _currentModelId.value) {
                        // 如果删除的是当前模型，使用相邻模型
                        if (adjacentModel != null) {
                            updateReplica(adjacentModel.modelId, adjacentModel.modelUrl)
                        } else {
                            _uiState.update { 
                                it.copy(
                                    isLoading = false,
                                    previewedModelUrl = null,
                                    previewedModelId = null
                                )
                            }
                            UserDataStoreManager.saveModelInfo(null, null)
                            _currentModelId.value = null
                        }
                    } else {
                        // 如果删除的不是当前模型，更新预览状态为相邻模型
                        if (adjacentModel != null) {
                            updatePreviewedReplica(adjacentModel.modelUrl, adjacentModel.modelId)
                        } else {
                            clearPreviewedReplica()
                        }
                    }
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { it.copy(isLoading = false, error = "delete replica model failed: ${response.message()}") }
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(isLoading = false, error = "delete replica model failed: ${e.message}") }
            }
        }
    }

    fun updateReplica(modelId: String, modelUrl: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true) }
            try {
                val response = TryOnService.api.updateReplica(UpdateReplicaParams(modelId, modelUrl))
                if (response.isSuccessful) {
                    getReplica()
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { it.copy(isLoading = false, error = "update replica failed: ${response.message()}") }
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(isLoading = false, error = "update replica failed: ${e.message}") }
            }
        }
    }


    fun observeTaskStatus() {
        viewModelScope.launch {
            taskStatus.collect { status ->
                if (status == lastStatus) {
                    return@collect
                }
                lastStatus = status
                
                Timber.d("任务状态: $status")
                when (status) {
                    "completed" -> {
                        Timber.d("任务完成")
                        UserDataStoreManager.clearModelInfo()
                        _uiState.update { it.copy(previewedModelUrl = previewUrl.value) }
                        getReplica()
                        getReplicaHistory()
                    }
                    "failure", "failed" -> {
                        Timber.d("任务失败")
                    }
                    else -> {
                        // 不做任何事情
                    }
                }
            }
        }
    }

    fun getReplicaHistory(limit: Int = 10, lastId: String? = null) {
        viewModelScope.launch {
            tryOnRepository.loadReplicaHistory(limit, lastId)
        }
    }

    fun loadMoreReplicaHistory() {
        if (hasMoreReplicaHistory.value && tryOnRepository.getLastReplicaHistoryId() != null) {
            viewModelScope.launch {
                _uiState.update { it.copy(isLoadingMore = true) }
                try {
                    getReplicaHistory(lastId = tryOnRepository.getLastReplicaHistoryId())
                } finally {
                    _uiState.update { it.copy(isLoadingMore = false) }
                }
            }
        }
    }
    
    fun updatePreviewedReplica(modelUrl: String, modelId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(previewedModelUrl = modelUrl, previewedModelId = modelId) }
        }
    }
    
    fun clearPreviewedReplica() {
        viewModelScope.launch {
            _uiState.update { it.copy(previewedModelUrl = null, previewedModelId = null) }
        }
    }

    fun updateShowTaskLoadingCard(show: Boolean) {
        viewModelScope.launch {
            _uiState.update { it.copy(showTaskLoadingCard = show) }
        }
    }
}
