package one.srp.gensmo.viewmodel.camera

import androidx.lifecycle.ViewModel
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import one.srp.gensmo.R

data class CameraMode(
    val title: String,
    val iconResId: Int,
    val query: String
)

class CameraViewModel : ViewModel() {
    
    private val cameraModes = listOf(
        CameraMode("DECOR", R.drawable.icon_home, "Decor items that complement this piece"),
        CameraMode("FIND SIMILAR", R.drawable.icon_match, "Find similar style for this item"),
        CameraMode("STYLE THIS PIECE", R.drawable.icon_outfit, "Outfit styling for this piece"),
        CameraMode("GENERAL", R.drawable.icon_query, ""),
        CameraMode("COOKING", R.drawable.icon_recipe, "Identify the main ingredients in this dish")
    )
    
    var selectedMode by mutableStateOf(cameraModes[3])  // 默认选择 GENERAL
        private set
        
    fun selectMode(mode: CameraMode) {
        selectedMode = mode
    }
    
    fun getCameraModes() = cameraModes
} 