package one.srp.gensmo.viewmodel.hashtag

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.emptyFlow
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.remote.HashtagPagingSource
import javax.inject.Inject

@HiltViewModel
class HashtagDetailViewModel @Inject constructor() : ViewModel() {
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _hashtag = MutableStateFlow("")
    val hashtag: StateFlow<String> = _hashtag.asStateFlow()
    
    private var hashtagPagingFlow: Flow<PagingData<FeedItem>>? = null
    
    fun setHashtag(hashtag: String) {
        if (_hashtag.value != hashtag) {
            _hashtag.value = hashtag
            // 重置分页流
            hashtagPagingFlow = null
        }
    }
    
    fun getPagingFlow(): Flow<PagingData<FeedItem>> {
        // 如果 hashtag 为空，返回空流
        if (_hashtag.value.isBlank()) {
            return emptyFlow()
        }
        
        return hashtagPagingFlow ?: run {
            val newFlow = Pager(
                config = PagingConfig(
                    pageSize = 20,
                    enablePlaceholders = false,
                    initialLoadSize = 20
                ),
                pagingSourceFactory = { 
                    HashtagPagingSource(
                        hashtag = _hashtag.value
                    )
                }
            ).flow.cachedIn(viewModelScope)
            
            hashtagPagingFlow = newFlow
            newFlow
        }
    }
    
    fun clear() {
        _hashtag.value = ""
        _isLoading.value = false
        hashtagPagingFlow = null
    }
} 