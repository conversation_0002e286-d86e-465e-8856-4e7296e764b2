package one.srp.gensmo.viewmodel.feed

import androidx.lifecycle.ViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.DefaultLifecycleObserver

@HiltViewModel
class InputBoxViewModel @Inject constructor() : ViewModel(), DefaultLifecycleObserver {
    private val _isNavigating = mutableStateOf(false)
    val isNavigating: State<Boolean> = _isNavigating

    fun resetNavigatingState() {
        _isNavigating.value = false
    }
    fun getNavigatingState(): Boolean {
        return _isNavigating.value
    }

    fun navigateToCamera() {
        _isNavigating.value = true
        startCamera()
    }

   

    private fun startCamera() {
        // 相机启动逻辑
    }
}