package one.srp.gensmo.viewmodel.user

import android.content.Context
import android.view.ViewGroup
import android.webkit.WebSettings
import androidx.activity.ComponentActivity
import androidx.lifecycle.ViewModel
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.BridgeWebView
import com.smallbuer.jsbridge.core.CallBackFunction
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.history._components.QueryEditorDialog
import one.srp.gensmo.utils.bridge.JSBridgeManager
import one.srp.gensmo.utils.env.EnvConf
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import org.json.JSONObject
import timber.log.Timber
import java.lang.ref.WeakReference
import javax.inject.Inject

/**
 * 历史页面的 ViewModel
 * 负责管理历史页面的状态和业务逻辑
 */
@HiltViewModel
class HistoryViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val jsBridgeManager: JSBridgeManager
) : ViewModel() {
    
    // 使用弱引用来避免内存泄漏
    private var webViewRef: WeakReference<BridgeWebView>? = null
    
    init {
        Timber.d("HistoryViewModel 已初始化")
    }
    
    // 创建并初始化WebView
    fun createWebView(): BridgeWebView {
        webViewRef?.get()?.let {
            return it
        }
        
        val newWebView = BridgeWebView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            settings.javaScriptEnabled = true
            settings.domStorageEnabled = true
            settings.useWideViewPort = true
            settings.loadWithOverviewMode = true
            settings.cacheMode = WebSettings.LOAD_DEFAULT
        }
        webViewRef = WeakReference(newWebView)
        Timber.d("WebView已在ViewModel中创建")
        return newWebView
    }

    fun getWebView(): BridgeWebView? {
        return webViewRef?.get()
    }
    
    // 初始化JSBridge并加载URL
    fun initializeWebView(navActions: NavActions, collageSearchViewModel: CollageSearchViewModel, activity: ComponentActivity, onInitialized: () -> Unit) {
        val webView = createWebView()
        webView.addHandlerLocal( "jumpToSearch", SearchHandler(navActions, collageSearchViewModel))
        webView.addHandlerLocal( "openQueryEditor", QueryEditorHandler(navActions, collageSearchViewModel, activity))
        jsBridgeManager.apply {
            Timber.d("JSBridgeManager配置已更新")
            
            init(webView) {
                val baseUrl = EnvConf.origin
                Timber.d("loadUrl: $baseUrl/user/history?click_src=profile")
                webView.loadUrl("$baseUrl/user/history?click_src=profile")
                Timber.d("历史页面已加载")
                onInitialized()
            }
        }
    }

     private inner class SearchHandler(
        private val navActions: NavActions,
        private val collageSearchViewModel: CollageSearchViewModel
     ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            try {
                val jsonData = JSONObject(data ?: "{}")
                val query = jsonData.optString("query", "")
                val imageUrl = jsonData.optString("imageURL", "")
                val taskId = jsonData.optString("taskId", "")
                val mode = jsonData.optString("mode", "")

                if (mode == "result") {
                    collageSearchViewModel.clear()
                    navActions.navigateToCollageTask(taskId)
                } else {
                    navActions.navigateToCollageSearch(query, imageUrl)
                }

                function?.onCallBack("success")
            } catch (e: Exception) {
                Timber.e(e, "跳转到搜索页面失败")
                function?.onCallBack("error")
            }
        }
    }

     private inner class QueryEditorHandler(
        private val navActions: NavActions,
        private val collageSearchViewModel: CollageSearchViewModel,
        private val activity: ComponentActivity
     ) : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            try {
                val jsonData = JSONObject(data ?: "{}")
                val imageUrl = jsonData.optString("imageURL", "")
                val query = jsonData.optString("query", "")
                val taskId = jsonData.optString("taskId", "")
                
                activity.runOnUiThread {
                    try {
                        QueryEditorDialog(
                            context = activity,
                            imageUrl = imageUrl,
                            initialQuery = query,
                            taskId = taskId,
                            onSave = { query, imageUrl ->
                                Timber.d("query: $query")
                                Timber.d("imageUrl: $imageUrl")
                                collageSearchViewModel.clear()
                                navActions.navigateToCollageSearch(query = query, imageUrl = imageUrl)
                            },
                            onDismiss = {
                                function?.onCallBack("cancelled")
                            }
                        ).show()
                    } catch (e: Exception) {
                        Timber.e(e, "在UI线程中打开查询编辑器失败")
                        function?.onCallBack("error: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "打开查询编辑器失败")
                function?.onCallBack("error")
            }
        }
    }
    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        webViewRef?.clear()
        webViewRef = null
        jsBridgeManager.release()
        Timber.d("HistoryViewModel已清除，WebView引用已释放")
    }
}
