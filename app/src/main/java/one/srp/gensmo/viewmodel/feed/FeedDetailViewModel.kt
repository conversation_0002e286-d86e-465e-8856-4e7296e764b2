package one.srp.gensmo.viewmodel.feed

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.remote.FeedService
import one.srp.gensmo.data.repository.PublishRepository
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.data.utils.JSON
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class FeedDetailViewModel @Inject constructor(
    private val asyncTaskManager: AsyncTaskManager,
    private val publishRepository: PublishRepository,
) : ViewModel() {

    private val _searchStatus = MutableStateFlow(TaskStatus.Idle)
    val searchStatus: StateFlow<TaskStatus> = _searchStatus.asStateFlow()

    private val _searchResult = MutableStateFlow<FeedItem?>(null)
    val searchResult: StateFlow<FeedItem?> = _searchResult.asStateFlow()

    fun getCollageDetail(id: String) {
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Loading)
            _searchResult.emit(null)

            try {
                val res = FeedService.api.getFeedDetail("collage", id, UserDataStoreManager.getUserId())

                if (res.isSuccessful) {
                    res.body()?.let { body ->
                        val feedItem = JSON.decodeFromString<FeedItem>(
                            JSON.encodeToString(body.data)
                        )
                        feedItem.moodboards?.let {
                            it.parsedContent = JSON.decodeFromString<MoodboardContent>(it.content)
                        }

                        _searchResult.emit(feedItem)
                        _searchStatus.emit(TaskStatus.Success)
                    } ?: run {
                        Timber.e("Parse failed")
                        _searchStatus.emit(TaskStatus.Fail)
                    }
                } else {
                    Timber.e("Fetch failed")
                    _searchStatus.emit(TaskStatus.Fail)
                }
            } catch (err: Exception) {
                Timber.e(err)
                _searchStatus.emit(TaskStatus.Fail)
            }
        }
    }

    fun getTryOnDetail(id: String) {
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Loading)
            _searchResult.emit(null)

            try {
                val res = FeedService.api.getFeedDetail("try_on", id, UserDataStoreManager.getUserId())

                if (res.isSuccessful) {
                    res.body()?.let { body ->
                        val feedItem = JSON.decodeFromString<FeedItem>(
                            JSON.encodeToString(body.data)
                        )
                        feedItem.moodboards?.let {
                            it.parsedContent = JSON.decodeFromString<MoodboardContent>(it.content)
                        }

                        _searchResult.emit(feedItem)
                        _searchStatus.emit(TaskStatus.Success)
                    } ?: run {
                        Timber.e("Parse failed")
                        _searchStatus.emit(TaskStatus.Fail)
                    }
                } else {
                    Timber.e("Fetch failed")
                    _searchStatus.emit(TaskStatus.Fail)
                }
            } catch (err: Exception) {
                Timber.e(err)
                _searchStatus.emit(TaskStatus.Fail)
            }
        }
    }

    fun isTryOnTaskRunning(): Boolean {
        return asyncTaskManager.isTaskLoading.value
    }

    fun clear() {
        viewModelScope.launch {
            _searchStatus.emit(TaskStatus.Idle)
            _searchResult.emit(null)
        }
    }

    fun deletePost(
        feedType: String,
        documentId: String,
        title: String,
        description: String,
        onSuccess: () -> Unit = {},
        onFail: (String) -> Unit = {}
    ) {
        viewModelScope.launch {
            publishRepository.deletePublishedPost(
                feedType = feedType,
                documentId = documentId,
                title = title,
                description = description
            ).collect { result ->
                result.fold(
                    onSuccess = {
                        Timber.d("删除帖子成功")
                        onSuccess()
                    },
                    onFailure = { throwable ->
                        Timber.e(throwable, "删除帖子失败")
                        onFail(throwable.message ?: "删除失败")
                    }
                )
            }
        }
    }

}
