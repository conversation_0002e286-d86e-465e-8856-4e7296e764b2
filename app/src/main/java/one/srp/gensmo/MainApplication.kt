package one.srp.gensmo

import android.app.Application
import androidx.lifecycle.ProcessLifecycleOwner
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import one.srp.gensmo.data.store.ABDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.SignatureValidator
import one.srp.gensmo.utils.integration.AppLifecycleObserver
import one.srp.gensmo.utils.integration.OneSignalManager
import one.srp.gensmo.utils.integration.initDevTools
import one.srp.gensmo.utils.integration.initMetricSDKs
import timber.log.Timber
import javax.inject.Inject

@HiltAndroidApp
class MainApplication : Application() {

    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private lateinit var appLifecycleObserver: AppLifecycleObserver

    @Inject
    lateinit var oneSignalManager: OneSignalManager

    override fun onCreate() {
        super.onCreate()

        initDevTools()

        if (!SignatureValidator.validate(this)) {
            if (!BuildConfig.DEBUG) {
                throw RuntimeException("Signature validation failed.")
            } else {
                Timber.e("FATAL: Signature validation failed!")
            }
        }

        initMetricSDKs(this, packageManager, packageName)

        UserDataStoreManager.initialize(this, oneSignalManager)
        ABDataStoreManager.initialize(this)

        runBlocking {
            DeviceDataStoreManager.initialize(this@MainApplication)
        }

        applicationScope.launch {
            UserDataStoreManager.initializeGuestTokenIfNeeded()
        }

        appLifecycleObserver = AppLifecycleObserver()
        ProcessLifecycleOwner.get().lifecycle.addObserver(appLifecycleObserver)

        // Initialize OneSignal
        oneSignalManager.initialize(this)
    }

    override fun onTerminate() {
        super.onTerminate()
        applicationScope.cancel()
        oneSignalManager.cleanup()
    }
}
