package one.srp.gensmo.ui.screens.user.history

import android.annotation.SuppressLint
import android.view.ViewGroup
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import androidx.hilt.navigation.compose.hiltViewModel
import timber.log.Timber
import androidx.compose.runtime.saveable.rememberSaveable
import one.srp.gensmo.viewmodel.user.HistoryViewModel
import androidx.activity.ComponentActivity
import androidx.compose.ui.platform.LocalContext

@SuppressLint("SetJavaScriptEnabled")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserHistoryScreen(
    navActions: NavActions,
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel()
) {
    // 通过 ViewModel 获取 WebView
    val viewModel: HistoryViewModel = hiltViewModel()
    
    // 获取当前context
    val context = LocalContext.current
    
    // 使用 remember 保存 WebView 的父视图引用
    val webViewParent = remember { mutableStateOf<ViewGroup?>(null) }
    
    // 使用 rememberSaveable 替代 remember 来保存 WebView 是否已经加载过的状态
    val webViewLoaded = rememberSaveable { mutableStateOf(false) }
    
    // 初始化WebView - 只在第一次进入时初始化
    DisposableEffect(Unit) {
        val activity = context as? ComponentActivity
        if (activity != null && !webViewLoaded.value) {
            viewModel.initializeWebView(
                navActions, 
                collageSearchViewModel,
                activity,
                onInitialized = { 
                    // 标记为已加载，避免重复初始化
                    webViewLoaded.value = true 
                }
            )
        } else {
            Timber.d("WebView 已经初始化过，跳过初始化")
        }
        
        onDispose {
            // 在离开屏幕前，从父视图中移除WebView，以便重用
            // 但不销毁WebView实例
            webViewParent.value?.let { parent ->
                val webView = viewModel.getWebView()
                if (webView?.parent == parent) {
                    parent.removeView(webView)
                    Timber.d("DisposableEffect onDispose: WebView已从父视图移除但保留实例")
                }
            }
        }
    }
    
    Column(modifier = Modifier.fillMaxSize()) {
        TopAppBar(
            title = { Text("History") },
            navigationIcon = {
                IconButton(onClick = { navActions.back() }) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            }
        )
        
        Box(modifier = Modifier
            .weight(1f)
            .fillMaxWidth()
        ) {
            // 使用自定义的AndroidView实现，确保WebView只被创建一次
            AndroidView(
                factory = { context ->
                    // 创建一个容器来持有WebView
                    val container = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    
                    // 获取或创建WebView
                    val webView = viewModel.getWebView() ?: viewModel.createWebView()
                    
                    // 如果WebView已有父视图，先移除
                    (webView.parent as? ViewGroup)?.removeView(webView)
                    
                    // 创建一个FrameLayout作为WebView的容器
                    val frameLayout = android.widget.FrameLayout(context).apply {
                        layoutParams = container
                        addView(webView, container)
                    }
                    
                    // 保存容器引用以便后续使用
                    webViewParent.value = frameLayout
                    
                    frameLayout
                },
                modifier = Modifier.fillMaxSize(),
                // 添加更新回调，确保在重组时不会重新创建WebView
                update = { _ ->
                    // 只更新布局参数，不重新创建WebView
                    Timber.d("AndroidView update回调被调用")
                }
            )
        }
    }
}
