package one.srp.gensmo.ui.screens.user.profile

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LargeTopAppBar
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._components.PublicProfileAssets
import one.srp.gensmo.ui.screens.user.profile._components.PublicProfileHeader
import one.srp.gensmo.ui.screens.user.profile._viewmodel.PublicProfileViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserPublicProfileScreen(
    navActions: NavActions = NavActions(),
    userId: String?,
    onClearFeed: () -> Unit = {},
    viewModel: PublicProfileViewModel = hiltViewModel(),
) {
    LaunchedEffect(userId) {
        userId?.let { viewModel.loadUserProfile(it) }
    }

    val topAppBarScrollBehavior = TopAppBarDefaults.exitUntilCollapsedScrollBehavior()

    Scaffold(
        topBar = {
            LargeTopAppBar(
                expandedHeight = 300.dp,
                collapsedHeight = 0.dp,
                scrollBehavior = topAppBarScrollBehavior,
                colors = TopAppBarDefaults.topAppBarColors(MaterialTheme.colorScheme.background),
                title = {
                    PublicProfileHeader(
                        navActions = navActions,
                        viewModel = viewModel,
                        modifier = Modifier.fillMaxWidth()
                    )
                },
            )
        }
    ) { paddingValues ->
        Box(modifier = Modifier.padding(paddingValues)) {
            PublicProfileAssets(
                modifier = Modifier.nestedScroll(topAppBarScrollBehavior.nestedScrollConnection),
                navActions = navActions,
                onClearFeed = onClearFeed,
                viewModel = viewModel
            )
        }
    }
}
