package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import one.srp.gensmo.data.model.EditorTag
import androidx.compose.foundation.layout.Arrangement
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.background


@Composable
fun SearchBudgetTag(
    budget: EditorTag,
    onDelete: (EditorTag) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.padding(vertical = 4.dp),
        shape = RoundedCornerShape(16.dp),
        color = Color(0xFFEFEFEF),
        contentColor = Color(0xFF1E1E1E)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Box(
                modifier = Modifier
                    .height(16.dp)
                    .background(
                        color = Color(0xFF434343),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .padding(horizontal = 2.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = budget.icon,
                    style = AppThemeTextStyle.Body12H,
                    color = Color.White
                )
            }
            Text(
                text = budget.text,
                style = AppThemeTextStyle.Body12H
            )
            IconButton(
                onClick = { onDelete(budget) },
                modifier = Modifier.size(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "删除预算",
                    tint = Color(0xFF666666),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}
