package one.srp.gensmo.ui.screens.camera
import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.viewmodel.camera.CameraViewModel
import one.srp.gensmo.ui.components.camera.CameraPicker

@Composable
fun CameraScreen(
    navActions: NavActions,
    onPhotoTaken: (String, String) -> Unit,
    viewModel: CameraViewModel = viewModel(),
    cameraModeTitle: String? = null,
) {
    CameraPicker(
        onPhotoTaken = onPhotoTaken,
        onMiss = { navActions.back() },
        viewModel = viewModel,
        cameraModeTitle = cameraModeTitle
    )
}