package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.screens.user.profile._viewmodel.AvatarThumbViewModel
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.runtime.remember
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.ui.navigation.NavActions

@Composable
fun AvatarThumb(
    modifier: Modifier = Modifier,
    viewModel: AvatarThumbViewModel = hiltViewModel(),
    navActions: NavActions? = null
) {
    // 添加LaunchedEffect以在组件进入组合时加载模型信息
    LaunchedEffect(Unit) {
        viewModel.loadModelInfo()
        navActions?.let { viewModel.setNavActions(it) }
    }
    
    // 使用 Compose 的状态收集，确保当 ViewModel 中的 imageUrl 变化时 UI 会重组
    val imageUrlState = viewModel.imageUrlState.collectAsState()
    val imageUrl = imageUrlState.value

    var progressState = viewModel.progress.collectAsState()
    var progress = progressState.value

    var isLoadingState = viewModel.isTaskLoading.collectAsState()
    var isLoading = isLoadingState.value
    // 用于保存头像位置的状态
    var avatarBounds = remember { mutableStateOf<IntSize?>(null) }
    var avatarPosition = remember { mutableStateOf<IntOffset?>(null) }

    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(36.dp)
                .onGloballyPositioned { coordinates ->
                    // 保存头像的位置和大小信息
                    avatarBounds.value = coordinates.size
                    avatarPosition.value = IntOffset(
                        x = coordinates.positionInRoot().x.toInt(),
                        y = coordinates.positionInRoot().y.toInt()
                    )
                }
        ) {
            if (isLoading) {
                val rawPercentage = if (progress.total > 0) {
                    progress.current.toFloat() / progress.total.toFloat()
                } else 0f
                
                // 添加动画效果
                val animatedPercentage = animateFloatAsState(
                    targetValue = rawPercentage,
                    animationSpec = tween(durationMillis = 500),
                    label = "进度动画"
                ).value
                
                Image(
                    painter = painterResource(id = R.drawable.icon_replica_creating),
                    contentDescription = "Creating Replica Avatar",
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(3.dp) // 为进度边框留出空间
                        .drawBehind {
                            // 绘制背景圆环
                            drawCircle(
                                color = Color.Gray.copy(alpha = 0.3f),
                                style = Stroke(width = 2.dp.toPx())
                            )
                            
                            // 使用动画值绘制进度圆环
                            val sweepAngle = 360f * animatedPercentage
                            drawArc(
                                color = Color(0xFF4924EF),
                                startAngle = -90f,
                                sweepAngle = sweepAngle,
                                useCenter = false,
                                style = Stroke(
                                    width = 2.dp.toPx(),
                                    cap = StrokeCap.Round
                                )
                            )
                        }
                )
            } else {
                if (imageUrl != null) {
                    AsyncImage(
                        model = coil3.request.ImageRequest.Builder(LocalContext.current)
                            .data(imageUrl)
                            .size(coil3.size.Size.ORIGINAL)
                            .build(),
                        contentDescription = "Replica Avatar",
                        contentScale = ContentScale.Crop,
                        alignment = Alignment.TopCenter,
                        modifier = Modifier
                            .clip(CircleShape)
                            .graphicsLayer { 
                                scaleX = 3f
                                scaleY = 3f
                                transformOrigin = TransformOrigin(0.5f, 0.0f)
                            }
                    )
                } else {
                    Image(
                        painter = painterResource(id = R.drawable.icon_replica),
                        contentDescription = "Default Replica Avatar",
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}
