package one.srp.gensmo.ui.screens.user.history._components

import android.app.AlertDialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.TypedValue
import android.view.Gravity
import android.view.ViewGroup
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.view.WindowManager
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.toColorInt
import coil3.load
import coil3.request.crossfade
import coil3.request.transformations
import coil3.transform.RoundedCornersTransformation
import one.srp.gensmo.R
import timber.log.Timber

class QueryEditorDialog(
    private val context: Context,
    private val imageUrl: String,
    private val initialQuery: String,
    private val taskId: String,
    private val onSave: (String, String?) -> Unit,
    private val onDismiss: () -> Unit
) {
    private lateinit var dialog: AlertDialog
    private lateinit var queryEditText: EditText
    private var isImageClosed = false
    
    fun show() {
        // 获取屏幕宽度
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        
        // 创建一个容器布局，确保宽度为屏幕宽度，并添加顶部圆角
        val container = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = ViewGroup.LayoutParams(screenWidth, dpToPx(232))
            
            // 创建顶部圆角背景
            val cornerRadius = dpToPx(16).toFloat()
            background = android.graphics.drawable.GradientDrawable().apply {
                setColor(Color.WHITE)
                // 只设置顶部左右圆角
                cornerRadii = floatArrayOf(
                    cornerRadius, cornerRadius,  // 左上角
                    cornerRadius, cornerRadius,  // 右上角
                    0f, 0f,                      // 右下角
                    0f, 0f                       // 左下角
                )
            }
        }
        
        // 创建标题栏布局（包含关闭按钮和标题）
        val titleBar = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT).apply {
                topMargin = dpToPx(16)
                bottomMargin = dpToPx(8)
            }
            gravity = Gravity.CENTER_VERTICAL
        }
        
        // 添加关闭按钮
        val closeButton = TextView(context).apply {
            text = "✕"
            textSize = 20f
            setTextColor(Color.BLACK)
            setPadding(dpToPx(16), dpToPx(0), dpToPx(16), dpToPx(0))
            setOnClickListener {
                dialog.dismiss()
                onDismiss()
            }
        }
        titleBar.addView(closeButton)
        
        // 添加标题
        val titleTextView = TextView(context).apply {
            text = context.getString(R.string.text_edit_to_continue)
            textSize = 18f
            typeface = android.graphics.Typeface.create("sans-serif-medium", android.graphics.Typeface.NORMAL)
            setTextColor(Color.BLACK)
            gravity = Gravity.CENTER
            layoutParams = LinearLayout.LayoutParams(0, WRAP_CONTENT, 1f)
        }
        titleBar.addView(titleTextView)
        
        // 添加一个空的视图，使标题居中
        val spacer = android.view.View(context).apply {
            layoutParams = LinearLayout.LayoutParams(closeButton.paddingLeft + closeButton.paddingRight, 0)
        }
        titleBar.addView(spacer)
        
        // 将标题栏添加到容器
        container.addView(titleBar)
        
        // 创建带边框的输入容器，改用 LinearLayout 使其垂直排列
        val inputContainer = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(screenWidth - dpToPx(32), WRAP_CONTENT).apply {
                marginStart = dpToPx(16)   // 左边距
                marginEnd = dpToPx(16)     // 右边距
                bottomMargin = dpToPx(16)  // 底部边距
            }
            
            // 创建一个带渐变边框的背景
            val shape = android.graphics.drawable.LayerDrawable(
                arrayOf(
                    // 外层渐变背景
                    android.graphics.drawable.GradientDrawable().apply {
                        cornerRadius = dpToPx(8).toFloat()
                        gradientType = android.graphics.drawable.GradientDrawable.RADIAL_GRADIENT
                        gradientRadius = dpToPx(200).toFloat()  // 渐变半径
                        colors = intArrayOf(
                            "#4924EF".toColorInt(),  // 中心颜色
                            "#F3B0BD".toColorInt()   // 边缘颜色
                        )
                    },
                    // 内层白色背景，比外层小4dp（边框宽度）
                    android.graphics.drawable.GradientDrawable().apply {
                        cornerRadius = dpToPx(8).toFloat()
                        setColor(Color.WHITE)
                    }
                )
            ).apply {
                // 设置内层边距，创建边框效果
                setLayerInset(1, dpToPx(4), dpToPx(4), dpToPx(4), dpToPx(4))
            }
            
            background = shape
            setPadding(dpToPx(16), 0, dpToPx(16), 0)
        }

        // 创建输入框 - 固定高度为100dp
        queryEditText = EditText(context).apply {
            setText(initialQuery)
            hint = "输入你的查询内容"
            background = null
            layoutParams = LinearLayout.LayoutParams(MATCH_PARENT, dpToPx(100))
            gravity = Gravity.TOP
            textSize = 16f
            setPadding(0, dpToPx(12), 0, dpToPx(12))
            setOnEditorActionListener { _, _, _ ->
                onSave(
                    queryEditText.text.toString(),
                    if (isImageClosed) null else imageUrl
                )
                dialog.dismiss()
                true
            }
        }

        // 创建图片预览容器
        val imageContainer = FrameLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(MATCH_PARENT, dpToPx(60)).apply {
                bottomMargin = dpToPx(8)
            }
            setPadding(0, dpToPx(8), 0, 0)
            clipChildren = false
            clipToPadding = false
        }

        // 添加图片预览
        val imagePreview = ImageView(context).apply {
            layoutParams = FrameLayout.LayoutParams(dpToPx(44), dpToPx(44)).apply {
                gravity = Gravity.START
            }
            // 先设置默认背景色，而不是在加载失败后再设置
            setBackgroundColor(Color.GRAY)
            
            // 确保图片URL不为空才加载图片
            if (imageUrl.isNotEmpty()) {
                try {
                    Timber.d("尝试加载图片: $imageUrl")
                    
                    // 直接使用最简单的加载方式，避免使用可能有问题的占位图
                    load(imageUrl) {
                        crossfade(true)
                        transformations(RoundedCornersTransformation(dpToPx(4).toFloat()))
                        // 不设置占位图和错误图，依赖于默认背景色
                    }
                } catch (e: Exception) {
                    Timber.e(e, "加载图片失败: $imageUrl")
                    // 不做额外处理，依赖于已设置的背景色
                }
            } else {
                Timber.d("图片URL为空，不加载图片")
            }
            
            visibility = if (imageUrl.isNotEmpty()) android.view.View.VISIBLE else android.view.View.GONE
        }

        // 添加图片预览上的关闭图标
        val closeImageIcon = TextView(context).apply {
            text = "✕"
            textSize = 10f
            setTextColor(Color.WHITE)
            gravity = Gravity.CENTER
            background = android.graphics.drawable.GradientDrawable().apply {
                setColor("#80000000".toColorInt())
                setShape(android.graphics.drawable.GradientDrawable.OVAL)
            }
            layoutParams = FrameLayout.LayoutParams(dpToPx(16), dpToPx(16)).apply {
                gravity = Gravity.START
                leftMargin = dpToPx(44) - dpToPx(8)
                topMargin = -dpToPx(8)
            }
            visibility = if (imageUrl.isNotEmpty()) android.view.View.VISIBLE else android.view.View.GONE
            setOnClickListener {
                imagePreview.visibility = android.view.View.GONE
                this.visibility = android.view.View.GONE
                isImageClosed = true
            }
        }

        // 修改搜索按钮为 ImageView 并调整其位置
        val searchButton = ImageView(context).apply {
            layoutParams = FrameLayout.LayoutParams(dpToPx(44), dpToPx(44)).apply {
                gravity = Gravity.END  // 将按钮放置在右侧
            }
            setImageResource(R.drawable.btn_search)
            scaleType = ImageView.ScaleType.CENTER_INSIDE
            setOnClickListener {
                onSave(
                    queryEditText.text.toString(),
                    if (isImageClosed) null else imageUrl
                )
                dialog.dismiss()
            }
        }

        // 将组件添加到容器中
        inputContainer.addView(queryEditText)
        imageContainer.addView(imagePreview)
        imageContainer.addView(closeImageIcon)
        imageContainer.addView(searchButton)  // 将 searchButton 添加到 imageContainer
        inputContainer.addView(imageContainer)
        
        // 将输入容器添加到主容器
        container.addView(inputContainer)
        
        // 创建对话框，使用底部显示的样式
        dialog = AlertDialog.Builder(context, android.R.style.Theme_Material_Light_Dialog)
            .setView(container)
            .setCancelable(true)
            .create()
        
        // 设置对话框窗口属性
        try {
            dialog.window?.apply {
                // 设置软键盘模式，保持键盘弹出
                setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
                // 设置对话框显示在底部
                attributes.gravity = Gravity.BOTTOM
                // 设置对话框宽度为屏幕宽度
                attributes.width = screenWidth
                // 设置对话框高度为固定值
                attributes.height = dpToPx(232)
                // 设置半透明背景作为蒙层
                setDimAmount(0.5f)  // 设置背景变暗程度
                // 使用透明背景
                setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                // 消除对话框边距
                decorView.setPadding(0, 0, 0, 0)
            }
        } catch (e: Exception) {
            Timber.e(e, "设置对话框窗口属性失败")
            // 即使设置窗口属性失败，也要尝试显示对话框
        }
        
        // 设置对话框消失监听
        dialog.setOnDismissListener {
            onDismiss()
        }
        
        // 显示对话框
        dialog.show()
        
        // 确保输入框获得焦点并显示键盘
        queryEditText.requestFocus()
    }
    
    // 辅助方法：将dp转换为像素
    private fun dpToPx(dp: Int): Int {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp.toFloat(),
            context.resources.displayMetrics
        ).toInt()
    }
}