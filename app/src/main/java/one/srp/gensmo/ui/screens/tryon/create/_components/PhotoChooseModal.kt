package one.srp.gensmo.ui.screens.tryon.create._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import android.net.Uri
import one.srp.gensmo.ui.components.camera.rememberImagePicker


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhotoChooseModal(
    onDismiss: () -> Unit,
    isLoading: Boolean = false,
    onShowCameraPicker: (Boolean) -> Unit,
    onAlbumChoose: (Uri) -> Unit,
    isVisible: Boolean = true
) {
    if (!isVisible) return
    
    val imagePicker = rememberImagePicker(
        onImageSelected = { uri ->
            onAlbumChoose(uri)
            onDismiss()
        },
        onError = { exception ->
            println("图片选择错误：${exception.message}")
        }
    )
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        sheetState = rememberModalBottomSheetState()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
        ) { 
            Text(
                text = "Tips for best results",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                textAlign = TextAlign.Center,
                color = Color(0xFF333333)
            )
            Text(
                text = "Front-facing, bright lighting, solo, full face.",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp)
            )
            
            Image(
                painter = painterResource(id = R.drawable.tryon_tips),
                contentDescription = "拍摄提示图片",
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp)
                    .padding(bottom = 16.dp),
                alignment = Alignment.Center
            )
            Button(
                onClick = { 
                    onShowCameraPicker(true)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .padding(vertical = 8.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFF5F5F5),
                    contentColor = Color.Black
                ),
                enabled = !isLoading
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_camera),
                        contentDescription = "Camera Icon",
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Take a Photo")
                }
            }

            Button(
                onClick = { 
                    imagePicker.launch() 
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .padding(top = 4.dp, bottom = 16.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFF5F5F5),
                    contentColor = Color.Black
                ),
                enabled = !isLoading
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_album),
                        contentDescription = "Gallery Icon",
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Choose from Gallery")
                }
            }
        }
    }
}
