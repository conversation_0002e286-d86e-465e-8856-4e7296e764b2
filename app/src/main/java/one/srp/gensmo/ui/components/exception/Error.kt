package one.srp.gensmo.ui.components.exception

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R

@Composable
fun BaseError(modifier: Modifier = Modifier, fill: Boolean = true, onClick: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .then(if (fill) Modifier.fillMaxSize() else Modifier)
            .then(modifier),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            Image(painter = painterResource(R.drawable.icon_error_hint), contentDescription = null)

            Text(stringResource(R.string.text_well_that_was_unexpected))

            Button(onClick = onClick, modifier = Modifier.fillMaxWidth(0.5f)) {
                Text(stringResource(R.string.text_try_later))
            }
        }
    }
}
