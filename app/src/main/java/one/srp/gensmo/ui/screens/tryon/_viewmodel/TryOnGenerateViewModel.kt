package one.srp.gensmo.ui.screens.tryon._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.model.TryOnTaskItem
import one.srp.gensmo.data.model.TryOnTaskTryOnItems
import one.srp.gensmo.data.remote.ProductService
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import one.srp.gensmo.utils.integration.trackEventOnSentry
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class TryOnGenerateViewModel @Inject constructor(
    private val asyncTaskManager: AsyncTaskManager,
) : ViewModel() {
    val taskStatus: StateFlow<String?> = asyncTaskManager.replicaTaskStatus
    val progress: StateFlow<AsyncTaskManager.Progress> = asyncTaskManager.progress
    val isTaskLoading: StateFlow<Boolean> = asyncTaskManager.isTaskLoading
    // val previewUrl: StateFlow<String?> = asyncTaskManager.previewUrl

    private val _userModelId = MutableStateFlow<String?>(null)
    val userModelId: StateFlow<String?> = _userModelId.asStateFlow()

    private val _previewUrl = MutableStateFlow<String?>(null)
    val previewUrl: StateFlow<String?> = _previewUrl.asStateFlow()

    private var lastStatus: String? = null

    init {
        observeTaskStatus()
    }

    fun loadUserModelId() {
        viewModelScope.launch {
            val model = UserDataStoreManager.getModelInfo()
            _userModelId.value = model.second
            _previewUrl.value = model.first
        }
    }

    private fun observeTaskStatus() {
        viewModelScope.launch {
            taskStatus.collect { status ->
                if (status == lastStatus) {
                    return@collect
                }
                lastStatus = status

                Timber.d("任务状态: $status")
                when (status) {
                    "completed" -> {
                        Timber.d("任务完成")
                        _userModelId.value = "temp"
                        _previewUrl.value = asyncTaskManager.previewUrl.value
                        viewModelScope.launch {
                            kotlinx.coroutines.delay(1000)
                            loadUserModelId()
                        }
                    }

                    "failure", "failed" -> {
                        Timber.d("任务失败")
                    }

                    else -> {
                        // 不做任何事情
                    }
                }
            }
        }
    }

    private val _queryStatus = MutableStateFlow(TaskStatus.Idle)
    val queryStatus: StateFlow<TaskStatus> = _queryStatus.asStateFlow()

    private val _queryResult = MutableStateFlow<TryOnTaskItem?>(null)
    val queryResult: StateFlow<TryOnTaskItem?> = _queryResult.asStateFlow()

    private var queryJob: Job? = null
    private var queryParams: TryOnParams? = null
    private var startTime: Long = 0

    fun generateTask(params: TryOnParams) {
        queryParams = params
        queryJob = viewModelScope.launch {
            try {
                startTime = System.currentTimeMillis()
                trackEventOnSentry("network_request", mapOf("path" to "/tryon"))

                val res = TryOnService.api.generateTryOnTask(
                    params.copy(
                        isAsync = false,
                        internalImageList = null
                    )
                )
                if (res.isSuccessful) {
                    res.body()?.let {
                        if (it.status == "completed") {
                            _queryResult.emit(it)
                            _queryStatus.emit(TaskStatus.Success)

                            val duration = (System.currentTimeMillis() - startTime) / 1000.0
                            trackEventOnSentry(
                                "network_timing",
                                mapOf("path" to "/tryon", "custom_duration" to duration)
                            )

                            it.moodboardId?.let { moodboardId -> getTryOnProduct(moodboardId) }
                        } else {
                            _queryStatus.emit(TaskStatus.Fail)
                            trackEventOnSentry(
                                "network_failure",
                                mapOf("path" to "/tryon", "error" to "任务未完成: ${it.status}")
                            )
                        }
                    }
                } else {
                    _queryStatus.emit(TaskStatus.Fail)
                    trackEventOnSentry(
                        "network_failure",
                        mapOf("path" to "/tryon", "error" to "请求失败: ${res.code()}")
                    )
                }
            } catch (e: Exception) {
                Timber.e(e)
                _queryStatus.emit(TaskStatus.Fail)
                trackEventOnSentry(
                    "network_failure",
                    mapOf("path" to "/tryon", "error" to (e.message ?: "未知错误"))
                )
            }
        }
    }

    fun regenerateTask() {
        queryParams?.let {
            viewModelScope.launch {
                _queryStatus.emit(TaskStatus.Loading)
                generateTask(
                    it.copy(
                        modelId = UserDataStoreManager.getModelInfo().second ?: it.modelId,
                        isFeed = false
                    )
                )
            }
        }
    }

    fun getTask(taskId: String) {
        queryJob = viewModelScope.launch {
            try {
                val res = TryOnService.api.getTryOnTask(taskId)
                if (res.isSuccessful) {
                    res.body()?.let {
                        _queryResult.emit(it)
                        _queryStatus.emit(TaskStatus.Success)

                        // 使用获取到的任务结果更新queryParams
                        updateQueryParamsFromTask(it)

                        it.moodboardId?.let { moodboardId -> getTryOnProduct(moodboardId) }
                    }
                } else {
                    _queryStatus.emit(TaskStatus.Fail)
                }
            } catch (e: Exception) {
                Timber.e(e)
            }

        }
    }

    private fun updateQueryParamsFromTask(task: TryOnTaskItem) {
        // 根据streetTryOnTaskId判断tryOnTaskId和moodboardId的设置
        val (finalTryOnTaskId, finalMoodboardId) = if (task.streetTryOnTaskId == null) {
            // streetTryOnTaskId为null时，使用moodboardId对应值
            Pair(null, task.moodboardId ?: queryParams?.moodboardId)
        } else {
            // streetTryOnTaskId不为null时，tryOnTaskId使用streetTryOnTaskId的值
            Pair(task.streetTryOnTaskId, null)
        }
        
        queryParams = queryParams?.copy(
            tryOnTaskId = finalTryOnTaskId,
            modelId = task.modelId ?: queryParams?.modelId,
            moodboardId = finalMoodboardId,
            products = task.products ?: queryParams?.products,
            garmentType = task.garmentType ?: queryParams?.garmentType,
            userImage = task.userImageUrl ?: queryParams?.userImage,
            userImageTag = task.userImageTag ?: queryParams?.userImageTag
        ) ?: TryOnParams(
            tryOnTaskId = finalTryOnTaskId,
            modelId = task.modelId,
            moodboardId = finalMoodboardId,
            products = task.products,
            garmentType = task.garmentType,
            userImage = task.userImageUrl,
            userImageTag = task.userImageTag
        )
        Timber.d("queryParams已更新: $queryParams")
    }


    private val _tryOnProductStatus = MutableStateFlow(TaskStatus.Idle)
    val tryOnProductStatus: StateFlow<TaskStatus> = _tryOnProductStatus.asStateFlow()
    private val _tryOnProductResult = MutableStateFlow<TryOnTaskTryOnItems?>(null)
    val tryOnProductResult: StateFlow<TryOnTaskTryOnItems?> = _tryOnProductResult.asStateFlow()

    fun getTryOnProduct(moodboardId: String) {
        viewModelScope.launch {
            try {
                val res = ProductService.api.getTryOnProduct(moodboardId)
                if (res.isSuccessful) {
                    res.body()?.let {
                        _tryOnProductResult.emit(it)
                        _tryOnProductStatus.emit(TaskStatus.Success)
                    }
                } else {
                    _tryOnProductStatus.emit(TaskStatus.Fail)
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    fun clear() {
        viewModelScope.launch {
            _queryStatus.emit(TaskStatus.Idle)
            _queryResult.emit(null)
        }
        queryJob?.cancel()
        queryJob = null
    }
}