package one.srp.gensmo.ui.screens.tryon.closet._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.data.model.GenerateReplicaHistoryItem
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.foundation.BorderStroke
import timber.log.Timber

@Composable
fun AvatarHistory(
    replicaHistory: List<GenerateReplicaHistoryItem>,
    currentModelId: String?,
    previewedModelId: String?,
    onAvatarClick: (String, String) -> Unit = { _, _ -> },
    onLoadMore: () -> Unit = {},
    isLoading: Boolean = false,
    hasMoreReplicaHistory: Boolean = false,
    isTaskLoading: Boolean = false,
    progress: Progress = Progress(current = 0, total = 1),
    onAddAvatarClick: () -> Unit = {},
    onTaskLoadingCardClick: () -> Unit = {}
) {
    val listState = rememberLazyListState()
    
    // 检测是否滚动到最后
    val shouldLoadMore = remember(hasMoreReplicaHistory, isLoading) {
        derivedStateOf {
            val lastVisibleItem = listState.layoutInfo.visibleItemsInfo.lastOrNull()
            val totalItems = listState.layoutInfo.totalItemsCount
            
            // 当最后一个可见项距离列表末尾还有2个或更少项时，就开始加载更多
            val shouldLoad = lastVisibleItem != null && 
                lastVisibleItem.index >= totalItems - 1 && 
                !isLoading && 
                hasMoreReplicaHistory
            shouldLoad
        }
    }
    
    // 当滚动到最后时加载更多
    LaunchedEffect(shouldLoadMore.value) {
        if (shouldLoadMore.value) {
            Timber.d("AvatarHistory - 触发加载更多")
            onLoadMore()
        }
    }

    LazyRow(
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        state = listState,
        modifier = Modifier.fillMaxWidth()
    ) {
        if (isTaskLoading) {
            item {
                TaskLoadingSmallCard(
                    progress = progress,
                    modifier = Modifier.width(72.dp),
                    onClick = onTaskLoadingCardClick
                )
            }
        } else {
            item {
                Card(
                    modifier = Modifier
                        .width(72.dp)
                        .height(96.dp)
                        .drawWithContent {
                            drawContent()
                            // 绘制虚线边框
                            drawRoundRect(
                                color = Color(0xFFD9D9D9),
                                style = Stroke(
                                    width = 1.dp.toPx(),
                                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
                                ),
                                cornerRadius = androidx.compose.ui.geometry.CornerRadius(8f, 8f)
                            )
                        },
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFf5F5F5)
                    ),
                    onClick = onAddAvatarClick
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(3f/4f),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "+",
                            style = MaterialTheme.typography.headlineLarge,
                            color = Color.Gray
                        )
                    }
                }
            }
        }
        
        items(replicaHistory) { replica ->
            replica.replicaList.forEachIndexed { index, replicaItem ->
                if (index > 0) {
                    Spacer(modifier = Modifier.width(12.dp))
                }
                Card(
                    modifier = Modifier
                        .width(72.dp)
                        .height(96.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    border = if (previewedModelId == replicaItem.modelId) {
                        BorderStroke(width = 2.dp, color = Color(0xFF222222))
                    } else null,
                    onClick = { 
                        onAvatarClick(replicaItem.modelUrl, replicaItem.modelId) 
                    }
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(3f/4f)
                    ) {
                        AsyncImage(
                            model = replicaItem.modelUrl,
                            contentDescription = "历史头像",
                            contentScale = ContentScale.FillWidth,
                            modifier = Modifier.fillMaxSize()
                        )
                        
                        // 如果当前模型ID与该历史记录一致，显示current标记
                        if (currentModelId == replicaItem.modelId) {
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomStart)
                                    .background(
                                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.8f),
                                        shape = RoundedCornerShape(topEnd = 4.dp)
                                    )
                                    .padding(horizontal = 4.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = "Current",
                                    style = MaterialTheme.typography.labelSmall,
                                    color = Color.White
                                )
                            }
                        }
                    }
                }
            }
        }
        
        if (hasMoreReplicaHistory) {
            item {
                Card(
                    modifier = Modifier
                        .width(72.dp)
                        .height(96.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Transparent
                    )
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
    }
}
