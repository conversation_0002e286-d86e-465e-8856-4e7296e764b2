package one.srp.gensmo.ui.screens.tryon.create._components

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.net.toUri
import one.srp.gensmo.R
import androidx.compose.ui.text.style.TextDecoration
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import androidx.compose.foundation.background
import androidx.compose.foundation.shape.RoundedCornerShape


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginModal(
    onDismiss: () -> Unit,
    isLoading: Boolean = false,
    onGoogleLogin: () -> Unit,
    onAppleLogin: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        sheetState = rememberModalBottomSheetState()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
        ) { 
            Text(
                text = "Log in to continue",
                style = AppThemeTextStyle.Heading20D,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                textAlign = TextAlign.Center,
                color = Color(0xFF333333)
            )
            Text(
                text = "This helps us keep your data safe as we create your AI Avatar.",
                style = AppThemeTextStyle.Body12H,
                color = Color(0xFF333333),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 24.dp)
            )
            Button(
                onClick = { onGoogleLogin() },
                modifier = Modifier
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFF4924EF).copy(alpha = 0.1f),
                                Color(0xFF8BE6F6).copy(alpha = 0.1f),
                                Color(0xFFF3B0BD).copy(alpha = 0.1f)
                            )
                        ),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .then(
                        Modifier.background(
                            color = Color(0xFFFFF8FA).copy(alpha = 0.5f),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                    ),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    contentColor = Color.Black
                ),
                enabled = !isLoading,
                contentPadding = PaddingValues(start = 24.dp, top = 16.dp, end = 24.dp, bottom = 16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_google),
                        contentDescription = "Google Icon",
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Continue with Google", style = AppThemeTextStyle.Body16H)
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = { onAppleLogin() },
                modifier = Modifier
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFF4924EF).copy(alpha = 0.1f),
                                Color(0xFF8BE6F6).copy(alpha = 0.1f),
                                Color(0xFFF3B0BD).copy(alpha = 0.1f)
                            )
                        ),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .then(
                        Modifier.background(
                            color = Color(0xFFFFF8FA).copy(alpha = 0.5f),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                    ),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    contentColor = Color.Black
                ),
                enabled = !isLoading,
                contentPadding = PaddingValues(start = 24.dp, top = 16.dp, end = 24.dp, bottom = 16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_apple),
                        contentDescription = "Apple Icon",
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Continue with Apple", style = AppThemeTextStyle.Body16H)
                }
            }

            val context = LocalContext.current
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 24.dp, bottom = 24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "By tapping Continue,",
                    style = AppThemeTextStyle.Body11H
                )
                
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "you agree to our ",
                        style = AppThemeTextStyle.Body11H
                    )
                    Text(
                        text = "Terms",
                        style = AppThemeTextStyle.Body11H.copy(
                            color = Color(0xFF333333),
                            textDecoration = TextDecoration.Underline
                        ),
                        modifier = Modifier.clickable {
                            val intent = Intent(Intent.ACTION_VIEW, "https://gensmo.com/about/terms".toUri())
                            context.startActivity(intent)
                        }
                    )
                    Text(
                        text = " and ",
                        style = AppThemeTextStyle.Body11H
                    )
                    Text(
                        text = "Privacy Policy",
                        style = AppThemeTextStyle.Body11H.copy(
                            color = Color(0xFF333333),
                            textDecoration = TextDecoration.Underline
                        ),
                        modifier = Modifier.clickable {
                            val intent = Intent(Intent.ACTION_VIEW, "https://gensmo.com/about/privacy".toUri())
                            context.startActivity(intent)
                        }
                    )
                }
            }
        }
    }
}
