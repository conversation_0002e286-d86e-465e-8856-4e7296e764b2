package one.srp.gensmo.ui.components.collage

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowUpward
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeColors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RemixPanelDrawer(
    open: Boolean = false,
    onClose: () -> Unit,
    content: @Composable () -> Unit,
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    if (open) {
        ModalBottomSheet(
            onDismissRequest = { onClose() },
            sheetState = sheetState,
            dragHandle = null,
            containerColor = Color.White,
        ) {
            content()
        }
    }
}

@Composable
fun ProductRemixPanel(
    initQuery: String = "Style with this",
    initImageUrl: String? = null,
    cameraEnable: Boolean = false,
    onClose: () -> Unit,
    onCommit: (String?, String?) -> Unit,
) {
    var query by remember { mutableStateOf(initQuery) }
    var imageUrl by remember { mutableStateOf<String?>(initImageUrl) }

    Column {
        Box {
            IconButton(onClick = onClose, modifier = Modifier.align(Alignment.CenterStart)) {
                Icon(Icons.Default.Close, null)
            }

            Text(
                text = stringResource(R.string.text_edit_to_continue),
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.Center),
                textAlign = TextAlign.Center,
            )
        }

        RemixInputBox(
            query = query,
            onQueryChange = { query = it },
            imageUrl = imageUrl,
            cameraEnable = cameraEnable,
            onImageUrlChange = {},
            onCommit = { onCommit(query, imageUrl) })
    }
}

@Composable
private fun RemixInputBox(
    query: String,
    onQueryChange: (String) -> Unit = {},
    imageUrl: String? = null,
    cameraEnable: Boolean = false,
    onImageUrlChange: () -> Unit = {},
    onCommit: () -> Unit = {},
) {
    Card(
        modifier = Modifier
            .padding(16.dp)
            .shadow(
                elevation = 4.dp, spotColor = Color(0x14000000), ambientColor = Color(0x14000000)
            )
            .border(
                width = 1.dp, color = AppThemeColors.Black, shape = RoundedCornerShape(size = 12.dp)
            ),
        colors = CardDefaults.cardColors(Color.White),
    ) {
        Column {
            TextField(
                value = query,
                onValueChange = { onQueryChange(it) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(4.dp),
                minLines = 5,
                maxLines = 5,
                shape = RoundedCornerShape(4.dp),
                colors = TextFieldDefaults.colors(
                    focusedContainerColor = Color.Transparent,
                    unfocusedContainerColor = Color.Transparent,
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                    disabledIndicatorColor = Color.Transparent,
                    errorIndicatorColor = Color.Transparent,
                ),
            )

            Row(modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)) {
                if (cameraEnable) {
                    Button(
                        onClick = { onImageUrlChange() },
                        colors = ButtonDefaults.buttonColors(Color.Transparent),
                        shape = RoundedCornerShape(4.dp),
                        contentPadding = PaddingValues(0.dp),
                    ) {
                        imageUrl?.let {
                            AsyncImage(
                                model = it,
                                contentDescription = null,
                                modifier = Modifier.size(40.dp)
                            )
                        } ?: run {
                            Icon(
                                painterResource(R.drawable.icon_camera),
                                null,
                                tint = Color.Unspecified,
                                modifier = Modifier.size(40.dp)
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.weight(1f))

                Button(
                    onClick = {
                        if (!query.isEmpty() || !imageUrl.isNullOrEmpty()) onCommit()
                    },
                    shape = MaterialTheme.shapes.medium,
                    modifier = Modifier.size(40.dp),
                    contentPadding = PaddingValues(0.dp),
                ) {
                    Icon(
                        Icons.Default.ArrowUpward,
                        null,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }

        }
    }
}
