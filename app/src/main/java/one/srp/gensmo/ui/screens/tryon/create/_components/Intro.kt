package one.srp.gensmo.ui.screens.tryon.create._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.ui.layout.layout
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.R

@Composable
fun Intro() {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp, vertical = 4.dp),
        ) {
            Text(
                text = "Let's create your own Avatar",
                color = Color(0xFF000000),
                style = AppThemeTextStyle.Heading24D
            )

            Text(
                text = "For a more accurate try-on experience, upload a selfie. Or, you can always use a default we provide.",
                color = Color(0xFF929292),
                style = AppThemeTextStyle.Body12LightH,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .layout { measurable, constraints ->
                    val placeable = measurable.measure(
                        constraints.copy(
                            maxWidth = constraints.maxWidth + 32.dp.roundToPx()
                        )
                    )
                    layout(placeable.width, placeable.height) {
                        placeable.placeRelative(0, 0)
                    }
                }
        ) {
            var currentImageIndex by remember { mutableIntStateOf(1) }
            
            LaunchedEffect(Unit) {
                while (true) {
                    delay(1000)  // 延迟1秒
                    currentImageIndex = if (currentImageIndex >= 5) 1 else currentImageIndex + 1
                }
            }
            
            Image(
                painter = painterResource(
                    id = when (currentImageIndex) {
                        1 -> R.drawable.tryon_suit1
                        2 -> R.drawable.tryon_suit2
                        3 -> R.drawable.tryon_suit3
                        4 -> R.drawable.tryon_suit4
                        else -> R.drawable.tryon_suit5
                    }
                ),
                contentDescription = "Try-on suit preview",
                modifier = Modifier
                    .fillMaxWidth(),
                contentScale = ContentScale.FillWidth
            )
        }
    }
}
