package one.srp.gensmo.ui.screens.user.account

import android.content.Context
import android.graphics.BitmapFactory
import android.net.Uri
import android.webkit.MimeTypeMap
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.launch
import one.srp.gensmo.R
import one.srp.gensmo.data.model.PostUserProfile
import one.srp.gensmo.data.remote.ImageService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import timber.log.Timber
import java.io.File

@Composable
fun UserAccountEditScreen(navActions: NavActions) {
    Scaffold(topBar = {
        TopBar(onBack = { navActions.back() }, transparent = true) {
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 40.dp)
            ) {
                Text(stringResource(R.string.text_edit_profile))
            }
        }
    }) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            Box(modifier = Modifier.weight(1f)) {
                AccountInfo()
            }
        }
    }
}

@Composable
private fun AccountInfo(viewModel: UserProfileViewModel = hiltViewModel()) {
    LaunchedEffect(Unit) {
        viewModel.getUserInfo()
    }

    val userName = viewModel.userName.collectAsState()
    val userAvatar = viewModel.userAvatar.collectAsState()

    var avatarUrl by remember { mutableStateOf<String?>(null) }

    var loading by remember { mutableStateOf(false) }
    var imageUri by remember { mutableStateOf<Uri?>(null) }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        if (uri == null) {
            loading = false
            return@rememberLauncherForActivityResult
        }

        coroutineScope.launch {
            imageUri = uri
            try {
                val url = uploadImage(context, uri)
                UserService.api.postUserProfile(PostUserProfile(profilePicture = url))
                avatarUrl = url
                viewModel.updateUserAvatar(url)
            } catch (e: Exception) {
                Toast.makeText(context, "Upload image failed", Toast.LENGTH_SHORT).show()
                Timber.e(e)
            }
            loading = false
        }
    }

    fun editAvatar() {
        loading = true
        launcher.launch("image/*")
    }

    Column {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Box(modifier = Modifier.size(96.dp)) {
                AsyncImage(
                    model = when (avatarUrl ?: userAvatar.value) {
                        "icon_random_thumb" -> R.drawable.icon_random_thumb
                        "icon_unregister" -> R.drawable.icon_unregister
                        else -> avatarUrl ?: userAvatar.value
                    },
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    error = painterResource(id = R.drawable.icon_unregister)
                )

                EditAvatarButton(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(24.dp),
                    loading = loading,
                    onClick = { editAvatar() })
            }
        }

        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(stringResource(R.string.text_username), style = AppThemeTextStyle.Body13H)
            TextField(
                modifier = Modifier.fillMaxWidth(),
                value = userName.value,
                onValueChange = {},
                enabled = false,
            )
        }
    }
}

@Composable
private fun EditAvatarButton(
    modifier: Modifier = Modifier,
    loading: Boolean = false,
    onClick: () -> Unit = {},
) {
    Button(
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        onClick = onClick,
        contentPadding = PaddingValues(4.dp),
        colors = ButtonDefaults.buttonColors(
            MaterialTheme.colorScheme.surface,
            MaterialTheme.colorScheme.onSurface
        )
    ) {
        if (loading) {
            BaseLoading(strokeWidth = 2.dp, modifier = Modifier.padding(2.dp))
        } else {
            Icon(Icons.Default.Add, null)
        }
    }
}

suspend fun uploadImage(context: Context, uri: Uri): String {
    try {
        // 处理 content URI，创建临时文件
        val file = when (uri.scheme) {
            "file" -> File(uri.path!!)
            "content" -> {
                // 创建临时文件
                val tempFile = createTempFileFromUri(context, uri)
                    ?: throw Exception("无法从内容URI创建临时文件")
                tempFile
            }

            else -> throw Exception("不支持的URI方案: ${uri.scheme}")
        }

        // 获取图片宽高
        val options = BitmapFactory.Options().apply {
            inJustDecodeBounds = true
        }
        BitmapFactory.decodeFile(file.absolutePath, options)
        val width = options.outWidth
        val height = options.outHeight

        // 获取预签名URL
        val presignedResponse = ImageService.api.getPresignedUrl(
            purpose = "user_upload",
            width = width,
            height = height
        )

        if (!presignedResponse.isSuccessful) {
            throw Exception("获取预签名URL失败: ${presignedResponse.errorBody()?.string()}")
        }

        val presignedData = presignedResponse.body()
            ?: throw Exception("预签名数据为空")

        // 使用预签名URL上传图片
        val uploadSuccess = ImageService.uploadWithPresignedUrl(
            presignedUrl = presignedData.presignedUrl,
            file = file
        )

        // 如果是临时文件，上传完成后删除
        if (uri.scheme == "content" && file.exists()) {
            file.delete()
        }

        if (!uploadSuccess) {
            throw Exception("使用预签名URL上传图片失败")
        }

        // 返回公共URL
        val uploadedUrl = presignedData.publicUrl
        Timber.d("uploadedImageUrl: $uploadedUrl")
        return uploadedUrl
    } catch (e: Exception) {
        Timber.e(e, "上传图片过程中发生错误")
        throw e
    }
}

// 从 URI 创建临时文件的辅助函数
private fun createTempFileFromUri(context: Context, uri: Uri): File? {
    return try {
        val fileExtension = getFileExtension(context, uri)
        val tempFile = File.createTempFile(
            "temp_upload_",
            if (fileExtension.isNotEmpty()) ".$fileExtension" else ".jpg",
            context.cacheDir
        )

        context.contentResolver.openInputStream(uri)?.use { inputStream ->
            tempFile.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
        }

        tempFile
    } catch (e: Exception) {
        Timber.e(e, "创建临时文件失败")
        null
    }
}

// 获取文件扩展名的辅助函数
private fun getFileExtension(context: Context, uri: Uri): String {
    return when (uri.scheme) {
        "content" -> {
            val mime = context.contentResolver.getType(uri)
            MimeTypeMap.getSingleton().getExtensionFromMimeType(mime) ?: ""
        }

        "file" -> {
            MimeTypeMap.getFileExtensionFromUrl(uri.toString()) ?: ""
        }

        else -> ""
    }
}
