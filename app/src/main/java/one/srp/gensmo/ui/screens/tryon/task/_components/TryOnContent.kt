package one.srp.gensmo.ui.screens.tryon.task._components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.delay
import one.srp.gensmo.data.model.TryOnBackgroundRes
import one.srp.gensmo.data.model.TryOnTaskItem
import one.srp.gensmo.data.repository.utils.PollState
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.screens.tryon.task._viewmodel.TryOnBackgroundViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun TryOnContent(
    modifier: Modifier = Modifier,
    item: TryOnTaskItem,
    onIndexChange: (Int) -> Unit = {},
    viewModel: TryOnBackgroundViewModel = hiltViewModel(),
) {
    val bgStateList = viewModel.bgStateMap.collectAsState().value.values.toList()
    val pagerState = rememberPagerState(pageCount = { bgStateList.size + 1 })

    val progress = remember { mutableIntStateOf(1) }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            onIndexChange(page)
        }
    }

    LaunchedEffect(bgStateList.size) {
        if (bgStateList.isNotEmpty()) {
            pagerState.animateScrollToPage(bgStateList.size + 1)
        }
    }

    Box(modifier = Modifier.then(modifier)) {
        if (bgStateList.isEmpty()) {
            TryOnImage(item.tryOnUrl)
        } else {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize(),
                pageSpacing = 16.dp,
                contentPadding = PaddingValues(horizontal = 32.dp)
            ) { index ->
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(0.75f),
                        colors = CardDefaults.cardColors(Color.White),
                        shape = MaterialTheme.shapes.extraSmall,
                        elevation = CardDefaults.cardElevation(2.dp),
                    ) {
                        if (index == 0) {
                            TryOnImage(item.tryOnUrl)
                        } else {
                            val bgState = bgStateList[index - 1]

                            LaunchedEffect(Unit) {
                                while (progress.intValue < 98) {
                                    if (bgState is PollState.Success || bgState is PollState.Timeout || bgState is PollState.Error) {
                                        progress.intValue = 1
                                        break
                                    }
                                    delay(200)
                                    progress.intValue += 1
                                }
                            }

                            BackgroundItem(bgState, progress.intValue)
                        }
                    }
                }
            }
        }

        if (pagerState.pageCount > 1) {
            PageIndicator(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = (-4).dp),
                pagerState = pagerState
            )
        }
    }
}

@Composable
private fun TryOnImage(image: String? = null) {
    Box() {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current).data(image).crossfade(true).build(),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Composable
private fun BackgroundItem(state: PollState<TryOnBackgroundRes>, progress: Int = 1) {
    when (state) {
        is PollState.Error -> {
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                BaseError()
            }
        }

        is PollState.Polling -> {
            BackgroundLoading(progress)
        }

        is PollState.Success -> {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(state.data.changeBackgroundImageUrl).crossfade(true).build(),
                contentDescription = null,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier.fillMaxWidth(),
            )
        }

        is PollState.Timeout -> {
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                BaseError()
            }
        }

        else -> {
            BackgroundLoading(progress)
        }
    }
}

@Composable
private fun BackgroundLoading(progress: Int = 1) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(AppThemeColors.Gray100.copy(alpha = 0.5f))
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "Generating...${progress}%",
                style = AppThemeTextStyle.Body16H
            )
        }
    }


}

@Composable
private fun PageIndicator(modifier: Modifier = Modifier, pagerState: PagerState) {
    Row(
        Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .padding(bottom = 8.dp, top = 16.dp)
            .then(modifier),
        horizontalArrangement = Arrangement.Center,
    ) {
        Card(colors = CardDefaults.cardColors(Color.White.copy(0.7f))) {
            Row(
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(pagerState.pageCount) { iteration ->
                    val color =
                        if (pagerState.currentPage == iteration) AppThemeColors.Black else AppThemeColors.Gray100
                    val size = if (pagerState.currentPage == iteration) 8 else 7

                    Box(
                        modifier = Modifier
                            .padding(2.dp)
                            .clip(CircleShape)
                            .background(color)
                            .size(size.dp)
                    )
                }
            }
        }
    }
}