package one.srp.gensmo.ui.screens.session.view.tryon

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchParams
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnChangebgResMessage
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.model.TryOnQueryMessage
import one.srp.gensmo.data.model.TryOnQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnResMessage
import one.srp.gensmo.data.model.TryOnTaskItem
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.collage.SharePanelDrawer
import one.srp.gensmo.ui.components.collage.TryOnSharePanel
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.product.panel.alternatives.ProductAlternativesDrawer
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.screens.session.view._components.ActionDropdownMenu
import one.srp.gensmo.ui.screens.session.view._components.AlternativesView
import one.srp.gensmo.ui.screens.session.view._components.CarouselContainer
import one.srp.gensmo.ui.screens.session.view._components.DownloadButton
import one.srp.gensmo.ui.screens.session.view._components.ShareButton
import one.srp.gensmo.ui.screens.session.view._components.TryOnHistoryButton
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.ui.screens.tryon.task._viewmodel.TryOnProductAlternativesViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.mime.downloadUrlImage
import one.srp.gensmo.viewmodel.navigation.SharedNavViewModel
import one.srp.gensmo.viewmodel.tryon.CreateViewModel
import java.util.UUID

@Composable
fun TryOnTaskViewScreen(
    navActions: NavActions = NavActions(),
    taskId: String? = null,
    sharedNavViewModel: SharedNavViewModel = hiltViewModel(),
    viewModel: SessionChatViewModel = hiltViewModel(),
) {
    if (taskId == null) {
        return BaseError(onClick = { navActions.back() })
    }

    val chatList by viewModel.chatList.collectAsState()
    val tryOnItems = remember(chatList) {
        chatList.filter { msg -> msg is TryOnResMessage || msg is TryOnChangebgResMessage }
            .mapNotNull { msg ->
                when (msg) {
                    is TryOnResMessage -> msg.value.tryonRes
                    is TryOnChangebgResMessage -> msg.value.tryonRes
                    else -> null
                }
            }
    }
    val initTryOnIndex = remember(taskId, tryOnItems) {
        val curr = tryOnItems.indexOfFirst { it.tryOnTaskId == taskId }
        if (curr == -1) 0 else curr
    }
    var currTryOnIndex by remember { mutableIntStateOf(initTryOnIndex) }
    val currTryOnItem =
        remember(tryOnItems, currTryOnIndex) { tryOnItems.getOrNull(currTryOnIndex) }

    var result by remember { mutableStateOf<TryOnTaskItem?>(null) }

    LaunchedEffect(Unit) {
        val cached = sharedNavViewModel.getTryOnTaskItem(taskId)
        cached?.let {
            result = it
        } ?: run {
            val res = TryOnService.api.getTryOnTask(taskId)
            if (res.isSuccessful) {
                res.body()?.let {
                    result = it
                }
            }
        }
    }

    val metric = rememberMetricHelper(EventRefer.TryOnGen)
    val metricItemTask = remember(currTryOnItem) {
        EventItem(
            itemCategory = EventItemCategory.TryOnTask,
            itemId = currTryOnItem?.tryOnTaskId,
        )
    }

    LaunchedEffect(Unit) {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView,
            )
        )
    }

    LaunchedEffect(currTryOnItem) {
        metric(
            MetricEvent.ViewItemList(
                itemListName = EventItemListName.TryOnResultList,
                items = listOf(metricItemTask),
            )
        )

        metric(
            MetricEvent.ViewItemList(
                itemListName = EventItemListName.TryOnResultEntityList,
                items = listOf(metricItemTask) + (currTryOnItem?.products?.mapIndexed { index, it ->
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = it.globalId,
                        itemName = it.brand,
                        index = index
                    )
                } ?: emptyList()),
            )
        )
    }

    fun clickProduct(product: ProductItem) {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.TryOnResultEntityListEntity,
                method = EventMethod.Click,
                actionType = EventActionType.EnterProductDetail,
                items = listOf(
                    metricItemTask, EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = product.globalId,
                        itemName = product.brand,
                    )
                ),
            )
        )
    }

    fun tryOnProduct(product: ProductItem) {
        val message = TryOnQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = TryOnQueryMessageWrapper(
                tryonQuery = TryOnParams(
                    modelId = runBlocking { UserDataStoreManager.getModelInfo().second ?: "" },
                    products = listOf(product),
                    internalImageList = listOfNotNull(product.mainImage?.link)
                )
            )
        )
        viewModel.sendMessage(message)
        navActions.back()
    }

    fun remixProduct(product: ProductItem) {
        val message = SearchQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = SearchQueryMessageWrapper(
                searchQuery = SearchParams(
                    debugLevel = 0,
                    query = "Complete the look",
                    budget = "",
                    isAsync = true,
                    route = "",
                    isPresetQuery = false,
                    moodboardVersion = "v2",
                    imageUrl = product.mainImage?.link ?: "",
                    useOnlineImageSeg = false,
                    specifiedProduct = product,
                )
            )
        )
        viewModel.sendMessage(message)
        navActions.back()
    }

    fun saveTryOn(targetState: Boolean) {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.SaveBtn,
                method = EventMethod.Click,
                actionType = if (targetState) EventActionType.Save else EventActionType.Unsave,
                items = listOf(metricItemTask),
            )
        )
    }

    var alternativesOpen by remember { mutableStateOf(false) }
    fun onMoreLook() {
        alternativesOpen = true

        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.MoreLooksBtn,
                method = EventMethod.Click,
                actionType = EventActionType.TryOnSelect,
                items = listOf(metricItemTask),
            )
        )
    }

    fun tryOnWithParam(param: TryOnParams) {
        val message = TryOnQueryMessage(
            sessionId = "default",
            messageId = UUID.randomUUID().toString(),
            role = ChatMessageRole.User.value,
            visible = true,
            value = TryOnQueryMessageWrapper(tryonQuery = param)
        )
        viewModel.sendMessage(message)
        navActions.back()
    }

    fun postFeed() {
        currTryOnItem?.let {
            navActions.navigateToPostEditor(it.tryOnTaskId, "tryon")
        }
    }

    Scaffold(topBar = {
        TopBar(immersive = true, transparent = true, onBack = { navActions.back() }, action = {
            ActionMenu(navActions = navActions, item = tryOnItems.getOrNull(currTryOnIndex))
        })
    }) { paddingValues ->
        Column(
            modifier = Modifier.padding(
                top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding(),
                bottom = paddingValues.calculateBottomPadding()
            )
        ) {
            currTryOnItem?.let { item ->
                TryOnCarousel(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    items = tryOnItems,
                    initialPage = initTryOnIndex,
                    onIndexChange = { currTryOnIndex = it },
                    onTryOn = { tryOnProduct(it) },
                    onRemix = { remixProduct(it) },
                    onItemClick = { clickProduct(it) }
                )

                BottomAction(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(bottom = 16.dp),
                    item = item,
                    onSave = { saveTryOn(it) },
                    moreLookEnable = item.moodboardId != null,
                    onMoreLook = { onMoreLook() },
                    onPost = { postFeed() }
                )

                AlternativesView(
                    open = alternativesOpen,
                    onClose = { alternativesOpen = false },
                    moodboardId = item.moodboardId,
                    onCommit = { tryOnWithParam(it) }
                )
            } ?: run {
                BaseLoading(modifier = Modifier.fillMaxSize())
            }
        }
    }
}

@Composable
private fun ActionMenu(navActions: NavActions = NavActions(), item: TryOnTaskItem?) {
    var shareOpen by remember { mutableStateOf(false) }

    val gotoTryOnHistory = {
        navActions.navigateToUserLibrary(1)
    }

    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val metric = rememberMetricHelper(EventRefer.TryOnGen)
    val downloadTryOn = {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.DownloadBtn,
                method = EventMethod.Click,
                actionType = EventActionType.Download,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.TryOnTask,
                        itemId = item?.tryOnTaskId,
                    )
                ),
            )
        )

        coroutineScope.launch {
            if (downloadUrlImage(context, item?.tryOnUrl)) {
                Toast.makeText(context, "Look saved.", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(context, "Save failed.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    ActionDropdownMenu(menu = {
        DownloadButton(
            text = stringResource(R.string.text_download_look), onClick = { downloadTryOn() })
        ShareButton(onClick = { shareOpen = true })
        TryOnHistoryButton(onClick = { gotoTryOnHistory() })
    }) {
        item?.let {
            SharePanelDrawer(open = shareOpen, onClose = { shareOpen = !shareOpen }) {
                TryOnSharePanel(item)
            }
        }
    }
}

@Composable
private fun TryOnCarousel(
    modifier: Modifier = Modifier,
    items: List<TryOnTaskItem>,
    initialPage: Int = 0,
    onIndexChange: (Int) -> Unit = {},
    onTryOn: (ProductItem) -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
    onItemClick: (ProductItem) -> Unit = {},
) {
    CarouselContainer(
        modifier = modifier,
        items = items,
        indicator = true,
        initialPage = initialPage,
        onIndexChange = { onIndexChange(it) }
    ) { item ->
        Box {
            AsyncImage(
                modifier = Modifier.fillMaxSize(),
                model = ImageRequest.Builder(LocalContext.current).data(item.tryOnUrl)
                    .crossfade(true)
                    .build(),
                contentDescription = null,
                contentScale = ContentScale.Crop
            )

            item.products?.let { products ->
                ProductView(
                    items = products,
                    moodboardId = item.moodboardId,
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(horizontal = 16.dp, vertical = 32.dp),
                    onTryOn = { onTryOn(it) },
                    onRemix = { onRemix(it) },
                    onItemClick = { onItemClick(it) }
                )
            }
        }
    }
}

@Composable
private fun ProductView(
    modifier: Modifier = Modifier,
    moodboardId: String? = null,
    items: List<ProductItem>,
    onTryOn: (ProductItem) -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
    onItemClick: (ProductItem) -> Unit = {},
    viewModel: TryOnProductAlternativesViewModel = hiltViewModel(),
) {
    var productPanelOpen by remember { mutableStateOf(false) }
    var selectedProduct by remember { mutableStateOf<ProductItem?>(null) }

    val productAlternatives by viewModel.queryResult.collectAsState()
    LaunchedEffect(selectedProduct, productPanelOpen) {
        if (productPanelOpen) {
            selectedProduct?.let {
                if (moodboardId?.isNotEmpty() == true) {
                    viewModel.getTryOnProductAlternatives(moodboardId, it.tags?.cateTag ?: "")
                }
            }
        }
    }

    fun onProductClick(product: ProductItem) {
        productPanelOpen = true
        selectedProduct = product
        onItemClick(product)
    }

    fun onProductClose() {
        productPanelOpen = false
        selectedProduct = null
    }

    Column(verticalArrangement = Arrangement.spacedBy(8.dp), modifier = modifier) {
        items.map { product ->
            Card(
                modifier = Modifier
                    .size(60.dp)
                    .clickable { onProductClick(product) },
                colors = CardDefaults.cardColors(
                    MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
                ),
                shape = MaterialTheme.shapes.medium
            ) {
                product.mainImage?.link?.let {
                    AsyncImage(it, null, modifier = Modifier.fillMaxSize())
                }
            }
        }
    }

    ProductAlternativesDrawer(
        open = productPanelOpen,
        onClose = { onProductClose() },
        onTryOn = {
            onTryOn(it)
            onProductClose()
        },
        onRemix = {
            onRemix(it)
            onProductClose()
        },
        selected = selectedProduct,
        products = productAlternatives,
        replaceable = false,
    )
}

@Composable
private fun BottomAction(
    modifier: Modifier = Modifier,
    item: TryOnTaskItem?,
    onSave: (Boolean) -> Unit = {},
    moreLookEnable: Boolean = false,
    onMoreLook: () -> Unit = {},
    onPost: () -> Unit = {},
) {
    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "TryOnTaskView")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickPost() {
        if (createViewModel.isUserLoggedIn()) {
            onPost()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    Row(horizontalArrangement = Arrangement.spacedBy(16.dp), modifier = modifier) {
        item?.let { item ->
            SaveActionContainer(
                type = CollectionType.TryOn,
                id = item.tryOnTaskId ?: "",
                initialState = item.isFavorited == true,
                onPreAction = { onSave(it) },
                onAction = { item.isFavorited = it }
            ) { state, onClick ->
                Button(
                    onClick = { onClick() },
                    colors = ButtonDefaults.buttonColors(
                        MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
                    ),
                    shape = MaterialTheme.shapes.medium,
                    contentPadding = PaddingValues(4.dp),
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Image(
                            painterResource(if (state) R.drawable.icon_saved_black else R.drawable.icon_saved_line),
                            null,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }

        Button(
            onClick = onMoreLook,
            enabled = moreLookEnable,
            modifier = Modifier.weight(2f),
            colors = ButtonDefaults.buttonColors(
                MaterialTheme.colorScheme.surface, MaterialTheme.colorScheme.onSurface
            ),
            shape = MaterialTheme.shapes.medium,
        ) {
            Text(stringResource(R.string.text_try_more), style = AppThemeTextStyle.Body16H)
        }

        Button(
            onClick = { clickPost() },
            modifier = Modifier.weight(2f),
            shape = MaterialTheme.shapes.medium,
        ) {
            Text(stringResource(R.string.text_post), style = AppThemeTextStyle.Body16H)
        }
    }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        onPost()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        onPost()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}
