package one.srp.gensmo.ui.screens.session.chat._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import kotlinx.coroutines.runBlocking
import one.srp.gensmo.R
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatHeader(viewModel: SessionChatViewModel, onBack: () -> Unit = {}) {
    val chatMeta = viewModel.chatMeta.collectAsState().value

    var userAvatar by remember { mutableStateOf<String?>(null) }
    LaunchedEffect(Unit) {
        userAvatar = UserDataStoreManager.getUserInfo()?.avatar
    }

    Row(modifier = Modifier.padding(8.dp), verticalAlignment = Alignment.CenterVertically) {
        Box(modifier = Modifier.size(48.dp)) {
            IconButton(onClick = onBack) {
                Box(modifier = Modifier.padding(8.dp)) {
                    Icon(Icons.Default.ArrowBackIosNew, null)
                }
            }
        }

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                chatMeta.title ?: "Chat",
                style = AppThemeTextStyle.Body13H,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )
        }

        Box(
            modifier = Modifier
                .size(48.dp)
                .padding(8.dp)
                .clip(MaterialTheme.shapes.medium),
            contentAlignment = Alignment.Center,
        ) {
            AsyncImage(
                model = userAvatar?.let {
                    when (userAvatar) {
                        "icon_random_thumb" -> R.drawable.icon_random_thumb
                        "icon_unregister" -> R.drawable.icon_unregister
                        else -> userAvatar
                    }
                } ?: {
                    if (runBlocking { UserDataStoreManager.isUserLoggedIn() }){
                        R.drawable.icon_random_thumb
                    } else {
                        R.drawable.icon_unregister
                    }
                },
                contentDescription = null,
                alignment = Alignment.Center
            )
        }
    }
}
