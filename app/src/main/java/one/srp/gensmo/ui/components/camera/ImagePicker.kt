package one.srp.gensmo.ui.components.camera

import android.net.Uri
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import androidx.activity.compose.rememberLauncherForActivityResult
/**
 * 创建并返回图片选择器的Composable函数
 */
@Composable
fun rememberImagePicker(
    onImageSelected: (Uri) -> Unit,
    onError: (Exception) -> Unit = {}
): ImagePickerLauncher {
    val context = LocalContext.current
    
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let {
            try {
                // 生成唯一文件名
                val photoFile = File(
                    context.getExternalFilesDir(null),
                    SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.CHINA)
                        .format(System.currentTimeMillis()) + ".jpg"
                )
                
                // 将选中图片复制到应用私有目录
                context.contentResolver.openInputStream(uri)?.use { input ->
                    photoFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }
                
                // 回调返回新文件URI
                val fileUri = Uri.fromFile(photoFile)
                onImageSelected(fileUri)
            } catch (e: Exception) {
                onError(e)
                e.printStackTrace()
            }
        }
    }
    
    return remember(launcher) {
        ImagePickerLauncher(launcher)
    }
}

class ImagePickerLauncher(private val launcher: ActivityResultLauncher<String>) {
    fun launch() {
        launcher.launch("image/*")
    }
}
