package one.srp.gensmo.ui.screens.tryon.closet._components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.clickable
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.preview.ZoomDialog

@Composable
fun AvatarCard(
    imageUrl: String,
    onDeleteClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                ZoomDialog {
                    AsyncImage(
                        model = imageUrl,
                        contentDescription = "模特图片",
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(3f/4f)
                            .shadow(
                                elevation = 3.dp,
                                shape = MaterialTheme.shapes.medium
                            ),
                        contentScale = ContentScale.FillWidth
                    )
                }
            }
            
            Surface(
                modifier = Modifier
                    .size(36.dp)
                    .align(Alignment.BottomEnd)
                    .padding(4.dp)
                    .clickable(onClick = onDeleteClick),
                shape = RoundedCornerShape(4.dp),
                color = Color.Transparent
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_delete),
                    contentDescription = "删除图标",
                    tint = Color.Unspecified,
                    modifier = Modifier.size(36.dp)
                )
            }
        }
    }
} 