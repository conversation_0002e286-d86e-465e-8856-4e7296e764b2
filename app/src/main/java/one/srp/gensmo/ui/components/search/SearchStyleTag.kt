package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.data.model.HomePageInfoStyle
import androidx.compose.foundation.layout.Arrangement
import one.srp.gensmo.ui.theme.AppThemeTextStyle
@Composable
fun SearchStyleTag(
    style: HomePageInfoStyle,
    onDelete: (HomePageInfoStyle) -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.padding(vertical = 4.dp),
        shape = RoundedCornerShape(16.dp),
        color = Color(0xFFEFEFEF),
        contentColor = Color(0xFF1E1E1E)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            if (!style.image.isNullOrEmpty()) {
                AsyncImage(
                    model = style.image,
                    contentDescription = style.title,
                    modifier = Modifier
                        .size(16.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    contentScale = ContentScale.Crop
                )
            }
            Text(
                text = style.title ?: "",
                style = AppThemeTextStyle.Body12H
            )
            IconButton(
                onClick = { onDelete(style) },
                modifier = Modifier.size(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "删除样式",
                    tint = Color(0xFF666666),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}
