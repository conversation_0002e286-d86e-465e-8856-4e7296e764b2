package one.srp.gensmo.ui.components.notification

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.draw.shadow
import androidx.compose.foundation.layout.wrapContentHeight

enum class ToastType {
    SUCCESS,
    ERROR,
    WARNING,
    INFO,
    IMAGE,
    ICON
}

object ToastManager {
    private var _showToast by mutableStateOf(false)
    private var _toastText by mutableStateOf("")
    private var _toastSubText by mutableStateOf("")
    private var _toastType by mutableStateOf(ToastType.INFO)
    private var _toastDuration by mutableLongStateOf(3000L)
    private var _imageUrl by mutableStateOf("")
    private var _iconType by mutableStateOf("")
    private var _onClick by mutableStateOf<(() -> Unit)?>(null)

    fun show(
        text: String,
        type: ToastType = ToastType.INFO,
        duration: Long = 3000L,
        imageUrl: String = "",
        subText: String = "",
        iconType: String = "",
        onClick: (() -> Unit)? = null
    ) {
        _toastText = text
        _toastSubText = subText
        _toastType = type
        _toastDuration = if (type == ToastType.IMAGE || type == ToastType.ICON) 5000L else duration
        _imageUrl = imageUrl
        _iconType = iconType
        _onClick = onClick
        _showToast = true
    }
    
    fun dismiss() {
        _showToast = false
    }

    @Composable
    fun Toast() {
        var visible by remember { mutableStateOf(_showToast) }

        LaunchedEffect(_showToast) {
            if (_showToast) {
                visible = true
                delay(_toastDuration)
                visible = false
                _showToast = false
            } else {
                visible = false
            }
        }

        when (_toastType) {
            ToastType.IMAGE -> {
                ToastWithImage(
                    text = _toastText,
                    imageUrl = _imageUrl,
                    onClick = _onClick,
                    visible = visible,
                    onDismiss = { dismiss() }
                )
            }
            ToastType.ICON -> {
                ToastWithIcon(
                    text = _toastText,
                    subText = _toastSubText,
                    iconType = _iconType,
                    onClick = _onClick,
                    visible = visible,
                    onDismiss = { dismiss() }
                )
            }
            else -> {
                if (visible) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 32.dp),
                        contentAlignment = Alignment.TopCenter
                    ) {
                        AnimatedVisibility(
                            visible = visible,
                            enter = fadeIn(animationSpec = tween(300)),
                            exit = fadeOut(animationSpec = tween(300))
                        ) {
                            Surface(
                                modifier = Modifier
                                    .shadow(elevation = 10.dp, spotColor = Color(0x33000000), ambientColor = Color(0x33000000))
                                    .shadow(elevation = 30.dp, spotColor = Color(0x1F000000), ambientColor = Color(0x1F000000))
                                    .shadow(elevation = 24.dp, spotColor = Color(0x24000000), ambientColor = Color(0x24000000))
                                    .width(335.dp)
                                    .background(color = Color(0xFF242C32), shape = RoundedCornerShape(size = 8.dp))
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                color = Color.Transparent
                            ) {
                                Row(
                                    modifier = Modifier.wrapContentHeight(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = when (_toastType) {
                                            ToastType.SUCCESS -> Icons.Default.CheckCircle
                                            ToastType.ERROR -> Icons.Default.Error
                                            ToastType.WARNING -> Icons.Default.Warning
                                            ToastType.INFO -> Icons.Default.Info
                                            ToastType.IMAGE -> Icons.Default.Info
                                            ToastType.ICON -> Icons.Default.Info
                                        },
                                        contentDescription = null,
                                        tint = when (_toastType) {
                                            ToastType.SUCCESS -> Color(0xFF4CAF50)
                                            ToastType.ERROR -> Color(0xFFF44336)
                                            ToastType.WARNING -> Color(0xFFFFC107)
                                            ToastType.INFO -> Color(0xFF2196F3)
                                            ToastType.IMAGE -> Color.White
                                            ToastType.ICON -> Color.White
                                        },
                                        modifier = Modifier.size(24.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = _toastText,
                                        color = Color.White,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
