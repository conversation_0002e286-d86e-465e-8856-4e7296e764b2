package one.srp.gensmo.ui.screens.user.profile

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._components.UserDeleteDialog
import one.srp.gensmo.viewmodel.user.ProfileViewModel

@Composable
fun UserProfileLegacyScreen(navActions: NavActions, viewModel: ProfileViewModel = hiltViewModel()) {
    val isLoggedIn = viewModel.isLoggedIn.collectAsState()
    val userAvatar = viewModel.userAvatar.collectAsState()
    val userName = viewModel.userName.collectAsState()
    val showDeleteDialog = remember { mutableStateOf(false) }

    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.checkLoginStatus()
        viewModel.getUserInfo()
    }

    Scaffold { paddingValues ->
        Column(
            Modifier
                .padding(paddingValues)
                .padding(horizontal = 20.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(24.dp))
            
            // 用户信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    AsyncImage(
                        model = when (userAvatar.value) {
                            "icon_random_thumb" -> R.drawable.icon_random_thumb
                            "icon_unregister" -> R.drawable.icon_unregister
                            else -> userAvatar.value
                        },
                        contentDescription = "用户头像",
                        modifier = Modifier
                            .size(56.dp)
                            .clip(CircleShape)
                            .border(1.5.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.5f), CircleShape)
                            .background(MaterialTheme.colorScheme.surfaceVariant),
                        contentScale = ContentScale.Crop,
                        error = painterResource(id = R.drawable.icon_unregister)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = userName.value,
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 0.5.sp
                        ),
                        color = MaterialTheme.colorScheme.onBackground,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.width(200.dp)
                    )
                }
                
                IconButton(
                    onClick = { navActions.back() },
                    modifier = Modifier.offset(x = 8.dp)
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                        contentDescription = "返回",
                        tint = MaterialTheme.colorScheme.onBackground,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))  // 增加间距
            
            // 功能区标题
            Text(
                text = "Features",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.align(Alignment.Start)
            )
            
            Spacer(modifier = Modifier.height(12.dp))

            // 功能按钮网格
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(0.dp)
            ) {
                FeatureItem(
                    icon = R.drawable.icon_history,
                    text = "History",
                    onClick = { 
                        if (isLoggedIn.value) {
                            navActions.navigateToHistory()
                        } else {
                            navActions.navigateToLogin()
                        }
                    }
                )
                FeatureItem(
                    icon = R.drawable.icon_saved_black,
                    text = "Saved",
                    onClick = { 
                        if (isLoggedIn.value) {
                            navActions.navigateToSaved()
                        } else {
                             navActions.navigateToLogin()
                        }
                    }
                )
                FeatureItem(
                    icon = R.drawable.icon_closet,
                    text = "AI Closet",
                    onClick = { 
                        if (isLoggedIn.value) {
                            navActions.navigateToCloset()
                        } else {
                             navActions.navigateToTryOnCreate()
                        }
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))  // 增加间距
            
            // 社交媒体部分
            Text(
                text = "Social",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.align(Alignment.Start)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 社交图标行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                SocialIcon(
                    icon = R.drawable.icon_share,
                    contentDescription = "Share",
                    onClick = { 
                        val shareIntent = Intent().apply {
                            action = Intent.ACTION_SEND
                            type = "text/plain"
                            putExtra(Intent.EXTRA_TEXT, "https://gensmo.com")
                        }
                        context.startActivity(Intent.createChooser(shareIntent, "Share Gensmo"))
                    }
                )
                SocialIcon(
                    icon = R.drawable.icon_x,
                    contentDescription = "X",
                    onClick = { 
                        context.openUrl("https://x.com/Gensmo_official")
                    }
                )
                SocialIcon(
                    icon = R.drawable.icon_tiktok,
                    contentDescription = "TikTok",
                    onClick = { 
                        context.openUrl("https://www.tiktok.com/@gensmo")
                    }
                )
                SocialIcon(
                    icon = R.drawable.icon_ins,
                    contentDescription = "Instagram",
                    onClick = { 
                        context.openUrl("https://www.instagram.com/gensmo_ig/")
                    }
                )
                SocialIcon(
                    icon = R.drawable.icon_discord,
                    contentDescription = "Discord",
                    onClick = { 
                        context.openUrl("https://discord.gg/9VjmFRKCqY")
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))

            // Legal 部分
            Text(
                text = "Legal",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.align(Alignment.Start)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(0.dp)
            ) {
                FunctionButton(
                    icon = R.drawable.icon_about,
                    text = "About Gensmo",
                    onClick = { context.openUrl("https://gensmo.com/about/") }
                )
                FunctionButton(
                    icon = R.drawable.icon_terms,
                    text = "Terms of Service",
                    onClick = { context.openUrl("https://gensmo.com/about/terms/") }
                )
                FunctionButton(
                    icon = R.drawable.icon_privacy,
                    text = "Privacy Policy",
                    onClick = { context.openUrl("https://gensmo.com/about/privacy/") }
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Account 部分移到 Legal 下面
            if (isLoggedIn.value) {
                Text(
                    text = "Account",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.onBackground,
                    modifier = Modifier.align(Alignment.Start)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Button(
                    onClick = { showDeleteDialog.value = true },
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = MaterialTheme.colorScheme.error
                    ),
                    shape = MaterialTheme.shapes.medium,
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    Text(
                        text = "Delete my account",
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontSize = 12.sp
                        ),
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Start
                    )
                }
                
                Spacer(modifier = Modifier.height(32.dp))
            }
            
            Spacer(modifier = Modifier.weight(0.7f))
            
            // 登录/登出按钮
            if (!isLoggedIn.value) {
                Button(
                    onClick = { navActions.navigateToLogin() },
                    modifier = Modifier
                        .fillMaxWidth(0.67f)
                        .height(48.dp)
                        .border(
                            width = 1.5.dp,
                            color = Color(0xFF6302FC),
                            shape = RoundedCornerShape(percent = 50)
                        ),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = Color(0xFF6302FC)
                    ),
                    shape = RoundedCornerShape(percent = 50),
                    contentPadding = PaddingValues(vertical = 8.dp)
                ) {
                    Text(
                        text = "Log in",
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            } else {
                Button(
                    onClick = { viewModel.logout() },
                    modifier = Modifier
                        .fillMaxWidth(0.67f)
                        .height(48.dp)
                        .border(
                            width = 1.5.dp,
                            color = Color(0xFF6302FC),
                            shape = RoundedCornerShape(percent = 50)
                        ),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.Transparent,
                        contentColor = Color(0xFF6302FC)
                    ),
                    shape = RoundedCornerShape(percent = 50),
                    contentPadding = PaddingValues(vertical = 8.dp)
                ) {
                    Text(
                        text = "Log out",
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 版本号显示
            val versionName = try {
                val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
                "Version ${packageInfo.versionName}"
            } catch (e: PackageManager.NameNotFoundException) {
                "Version Unknown"
            }
            
            Text(
                text = versionName,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),  // 使用半透明颜色
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
            
            Spacer(modifier = Modifier.height(24.dp))
        }
    }

    if (showDeleteDialog.value) {
        UserDeleteDialog(
            onDismiss = { showDeleteDialog.value = false },
            onConfirm = {
                showDeleteDialog.value = false
                viewModel.deleteAccount()  // 需要在 ViewModel 中实现
            }
        )
    }
}

@Composable
fun SocialIcon(
    @DrawableRes icon: Int,
    contentDescription: String,
    onClick: () -> Unit
) {
    IconButton(
        onClick = onClick,
        modifier = Modifier
            .size(48.dp)
            .clip(CircleShape)
    ) {
        Icon(
            painter = painterResource(id = icon),
            contentDescription = contentDescription,
            tint = Color.Unspecified,
            modifier = Modifier.size(24.dp)
        )
    }
}

// 新增功能按钮组件
@Composable
fun FunctionButton(
    @DrawableRes icon: Int,
    text: String,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
            contentColor = MaterialTheme.colorScheme.onBackground
        ),
        shape = MaterialTheme.shapes.medium,
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = icon),
                contentDescription = text,
                tint = Color.Unspecified,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = text,
                color = MaterialTheme.colorScheme.onBackground
            )
        }
    }
}

// 新增 FeatureItem 组件
@Composable
fun FeatureItem(
    @DrawableRes icon: Int,
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
            contentColor = MaterialTheme.colorScheme.onBackground
        ),
        shape = MaterialTheme.shapes.medium,
        contentPadding = PaddingValues(0.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = icon),
                contentDescription = text,
                tint = Color.Unspecified,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(16.dp))
            Text(
                text = text,
                color = MaterialTheme.colorScheme.onBackground
            )
        }
    }
}

private fun Context.openUrl(url: String) {
    val intent = Intent(Intent.ACTION_VIEW, url.toUri())
    try {
        startActivity(intent)
    } catch (e: ActivityNotFoundException) {
        // 处理无法打开URL的情况
    }
}

