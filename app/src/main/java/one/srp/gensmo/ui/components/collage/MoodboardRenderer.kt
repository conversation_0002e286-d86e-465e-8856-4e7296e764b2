package one.srp.gensmo.ui.components.collage

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.core.graphics.toColorInt
import coil3.compose.AsyncImage
import coil3.compose.AsyncImagePainter
import coil3.request.ImageRequest
import coil3.request.allowHardware
import coil3.request.crossfade
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.MoodboardContentBlock
import one.srp.gensmo.ui.theme.AppThemeFontFamily

@Composable
fun MoodboardRenderer(
    modifier: Modifier = Modifier,
    item: MoodboardContent? = null,
    onItemClick: (MoodboardContentBlock) -> Unit = {},
    offscreen: Boolean = false,
    animate: Boolean = false,
    interactive: Boolean = false,
    boundary: String = "w",
) {
    BoxWithConstraints(modifier = Modifier.wrapContentSize()) {
        item?.let {
            val contentWidth = item.width
            val contentHeight = item.height ?: (contentWidth * 1.5f)

            val containerWidth = this.maxWidth.value
            val containerHeight = this.maxHeight.value
            val zoom =
                if (boundary == "h") containerHeight / contentHeight else containerWidth / contentWidth
            val fontZoom =
                if (boundary == "h") this.maxHeight / LocalConfiguration.current.screenHeightDp.dp else this.maxWidth / LocalConfiguration.current.screenWidthDp.dp

            val sortedBlocks = item.blocks.sortedBy { it.position.z }
            val backgroundIsImage = item.background != null && !item.background.startsWith("#")
            val backgroundColor = if (backgroundIsImage) Color.Transparent else Color(
                (item.background?.trim() ?: "#ffffff").toColorInt()
            )

            Box(
                modifier = Modifier
                    .aspectRatio(contentWidth / contentHeight)
                    .background(backgroundColor)
                    .then(modifier)
            ) {
                item.background?.let {
                    if (backgroundIsImage) {
                        BackgroundBlock(url = item.background, offscreen = offscreen)
                    }
                }

                sortedBlocks.forEach { block ->
                    when (block.content.type) {
                        "image" -> ImageBlock(
                            block = block,
                            zoom = zoom,
                            onItemClick = onItemClick,
                            offscreen = offscreen,
                            animate = animate,
                            interactive = interactive,
                        )

                        "text" -> TextBlock(block = block, zoom = zoom, fontZoom = fontZoom)

                        else -> {}
                    }
                }
            }
        }
    }
}

@Composable
fun BackgroundBlock(url: String, offscreen: Boolean = false) {
    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current).data(url).crossfade(true)
            .allowHardware(offscreen).build(),
        contentDescription = null,
        contentScale = ContentScale.FillBounds,
        modifier = Modifier.fillMaxSize(),
    )
}

@Composable
fun ImageBlock(
    block: MoodboardContentBlock,
    zoom: Float = 1f,
    onItemClick: (MoodboardContentBlock) -> Unit = {},
    offscreen: Boolean = false,
    animate: Boolean = false,
    interactive: Boolean = false,
) {
    val coroutineScope = rememberCoroutineScope()

    val offsetX = (block.position.x ?: 0f) * zoom
    val offsetY = (block.position.y ?: 1f) * zoom
    val blockWidth = (block.size.width ?: 1f) * zoom
    val blockHeight = (block.size.height ?: 1f) * zoom

    var success by remember { mutableStateOf(false) }
    val scale = remember { Animatable(1f) }

    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current).data(block.content.content)
            .crossfade(animate).allowHardware(!offscreen).build(),
        contentDescription = null,
        contentScale = ContentScale.Fit,
        modifier = Modifier
            .size(blockWidth.dp, blockHeight.dp)
            .offset(x = offsetX.dp, y = offsetY.dp)
            .zIndex((block.position.z ?: 0f))
            .then(if (!animate) Modifier else Modifier.scale(scale.value))
            .then(if (offscreen || !interactive) Modifier else Modifier.clickable {
                onItemClick(block)
            }),
        onState = { state ->
            if (animate && state is AsyncImagePainter.State.Success && !success) {
                success = true
                coroutineScope.launch {
                    scale.animateTo(
                        targetValue = 0.85f, animationSpec = tween(durationMillis = 400)
                    )
                    scale.animateTo(
                        targetValue = 1f, animationSpec = tween(durationMillis = 250)
                    )
                }
            }
        },
    )
}

@Composable
fun TextBlock(
    block: MoodboardContentBlock,
    zoom: Float = 1f,
    fontZoom: Float = 1f,
) {
    val offsetX = (block.position.x ?: 0f) * zoom
    val offsetY = (block.position.y ?: 1f) * zoom
    val blockWidth = (block.size.width ?: 1f) * zoom
    val blockHeight = (block.size.height ?: 1f) * zoom

    val fontSize = (block.content.style?.fontSize?.toInt() ?: 24) * fontZoom

    Text(
        block.content.content,
        modifier = Modifier
            .size(blockWidth.dp, blockHeight.dp)
            .offset(x = offsetX.dp, y = offsetY.dp)
            .zIndex((block.position.z ?: 0f)),
        textAlign = TextAlign.Center,
        overflow = TextOverflow.Visible,
        fontSize = fontSize.sp,
        lineHeight = (fontSize * 1.1f).sp,
        fontFamily = getFontFamily(block.content.style?.fontFamily),
        color = Color((block.content.style?.color?.trim() ?: "#000000").toColorInt()),
    )
}

fun getFontFamily(fontName: String? = null): FontFamily {
    return when (fontName) {
        "IBM Plex Mono" -> AppThemeFontFamily.IbmPlexMono
        "DM Serif Display" -> AppThemeFontFamily.DmSerifDisplay
        "DM Serif Text" -> AppThemeFontFamily.DmSerifText
        "Outfit" -> AppThemeFontFamily.Outfit
        "Sonsie One" -> AppThemeFontFamily.SonsieOne
        "Lobster" -> AppThemeFontFamily.Lobster
        "Inter" -> AppThemeFontFamily.Inter
        else -> AppThemeFontFamily.Inter
    }
}
