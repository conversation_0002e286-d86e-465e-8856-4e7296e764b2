package one.srp.gensmo.ui.components.collage

import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.MoodboardTryOnGarmentItem
import one.srp.gensmo.data.model.MoodboardTryOnItems
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import kotlin.math.abs


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TryOnPanelDrawer(
    open: Boolean = false,
    onClose: () -> Unit,
    content: @Composable () -> Unit,
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    if (open) {
        ModalBottomSheet(
            onDismissRequest = { onClose() },
            sheetState = sheetState,
            dragHandle = null,
            containerColor = Color.White,
        ) {
            content()
        }
    }
}

@Composable
fun MoodboardTryOnPanel(
    items: MoodboardTryOnItems,
    onClose: () -> Unit = {},
    onTryOn: (List<MoodboardTryOnGarmentItem>) -> Unit = {},
) {
    val fullBody = items.box1?.firstOrNull { it.fullBody != null }?.fullBody
        ?: items.box2?.firstOrNull { it.fullBody != null }?.fullBody
    val top = items.box1?.firstOrNull { it.top != null }?.top
        ?: items.box2?.firstOrNull { it.top != null }?.top
    val bottom = items.box1?.firstOrNull { it.bottom != null }?.bottom
        ?: items.box2?.firstOrNull { it.bottom != null }?.bottom

    var selectedFullBody by remember { mutableStateOf<MoodboardTryOnGarmentItem?>(fullBody?.firstOrNull()) }
    var selectedTop by remember { mutableStateOf<MoodboardTryOnGarmentItem?>(if (fullBody == null) top?.firstOrNull() else null) }
    var selectedBottom by remember { mutableStateOf<MoodboardTryOnGarmentItem?>(if (fullBody == null && top == null) bottom?.firstOrNull() else null) }

    val selectFullBody = { it: MoodboardTryOnGarmentItem? ->
        it?.let {
            selectedFullBody = it
            selectedTop = null
            selectedBottom = null
        }
    }
    val selectTop = { it: MoodboardTryOnGarmentItem? ->
        it?.let {
            selectedFullBody = null

            if (selectedTop == it) {
                if (selectedBottom != null) {
                    selectedTop = null
                }
            } else {
                selectedTop = it
            }
        }
    }
    val selectBottom = { it: MoodboardTryOnGarmentItem? ->
        it?.let {
            selectedFullBody = null

            if (selectedBottom == it) {
                if (selectedTop != null) {
                    selectedBottom = null
                }
            } else {
                selectedBottom = it
            }
        }
    }

    val metric = rememberMetricHelper(EventRefer.TryOnSelectPanel)
    LaunchedEffect(Unit) {
        // 页面曝光埋点
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView
            )
        )
    }
    val confirmTryOn = {
        val selectedProducts = listOfNotNull(selectedFullBody, selectedTop, selectedBottom)
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.ConfirmBtn,
                method = EventMethod.Click,
                actionType = EventActionType.TryOn,
                items = selectedProducts.map {
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = it.itemId,
                    )
                }
            )
        )
        onTryOn(selectedProducts)
    }

    Column {
        PanelHeader(
            onClose = onClose,
            title = stringResource(R.string.text_let_s_get_you_styled),
            description = stringResource(R.string.text_select_a_full_body_outfit_or_a_top_and_bottom)
        )

        if (selectedFullBody == null && selectedTop == null && selectedBottom == null) {
            BaseError(
                modifier = Modifier
                    .fillMaxHeight(0.5f)
                    .fillMaxWidth(),
                fill = false,
                onClick = { onClose() })
        } else {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                fullBody?.let {
                    TryOnGroup(
                        label = "Full-body", fullBody, selectedFullBody, onSelect = {
                            selectFullBody(it)
                        })

                    (top ?: bottom)?.let { OrDivider() }
                }

                top?.let {
                    TryOnGroup(
                        label = "Top", top, selectedTop, onSelect = {
                            selectTop(it)
                        })
                }
                bottom?.let {
                    TryOnGroup(
                        label = "Bottom", bottom, selectedBottom, onSelect = {
                            selectBottom(it)
                        })
                }
            }

            Button(
                onClick = { confirmTryOn() },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = MaterialTheme.shapes.extraSmall
            ) {
                Text(
                    "CONFIRM",
                    style = AppThemeTextStyle.Body16H.copy(fontWeight = FontWeight.W500)
                )
            }
        }
    }
}

@Composable
private fun PanelHeader(
    title: String? = null,
    description: String? = null,
    onClose: () -> Unit = {},
) {
    Box() {
        IconButton(onClick = onClose, modifier = Modifier.align(Alignment.TopStart)) {
            Icon(Icons.Default.Close, null)
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            title?.let {
                Text(
                    title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }

            description?.let { Text(description, style = MaterialTheme.typography.labelSmall) }
        }
    }
}

@Composable
private fun TryOnGroup(
    label: String,
    items: List<MoodboardTryOnGarmentItem>,
    selected: MoodboardTryOnGarmentItem? = null,
    onSelect: (MoodboardTryOnGarmentItem?) -> Unit = {},
) {
    if (items.isEmpty()) return

    val scrollState = rememberScrollState()

    Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
        Text(label, style = MaterialTheme.typography.titleMedium)

        Box(modifier = Modifier.pointerInput(Unit) {
            awaitPointerEventScope {
                while (true) {
                    val event = awaitPointerEvent()
                    val dragEvent = event.changes.firstOrNull()

                    if (dragEvent != null
                        && abs(dragEvent.positionChange().y) > abs(dragEvent.positionChange().x / 3)
                        && abs(dragEvent.positionChange().y) > 1
                    ) {
                        event.changes.forEach { it.consume() }
                    }
                }
            }
        }) {
            Row(
                modifier = Modifier.horizontalScroll(scrollState),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items.map {
                    val isSelected = it == selected
                    Card(
                        modifier = Modifier
                            .size(100.dp)
                            .then(
                                if (isSelected) Modifier.border(
                                    2.dp, Color.Black, CardDefaults.shape
                                ) else Modifier.shadow(
                                    2.dp, CardDefaults.shape
                                )
                            ),
                        colors = CardDefaults.cardColors(Color.White),
                        onClick = { onSelect(it) }) {
                        AsyncImage(
                            model = it.imageUrl ?: it.mainImage?.link,
                            contentDescription = null,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun OrDivider(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.surfaceVariant,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        HorizontalDivider(
            modifier = Modifier.weight(1f), color = color
        )
        Text(
            text = "or",
            modifier = Modifier.padding(horizontal = 16.dp),
            style = MaterialTheme.typography.bodyMedium,
            color = color
        )
        HorizontalDivider(
            modifier = Modifier.weight(1f), color = color
        )
    }
}
