package one.srp.gensmo.ui.screens.user.profile._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.PostUserProfile
import one.srp.gensmo.data.remote.CommunityService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.ui.components.action.FollowStatus
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PublicProfileViewModel @Inject constructor() : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _userProfile = MutableStateFlow<PostUserProfile?>(null)
    val userProfile: StateFlow<PostUserProfile?> = _userProfile.asStateFlow()

    private val _userName = MutableStateFlow("User")
    val userName: StateFlow<String> = _userName.asStateFlow()

    private val _userAvatar = MutableStateFlow<String?>(null)
    val userAvatar: StateFlow<String?> = _userAvatar.asStateFlow()

    private val _isFollowing = MutableStateFlow(false)
    val isFollowing: StateFlow<Boolean> = _isFollowing.asStateFlow()

    private val _currentUserId = MutableStateFlow<String?>(null)
    val currentUserId: StateFlow<String?> = _currentUserId.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    fun loadUserProfile(userId: String) {
        _currentUserId.value = userId
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                // Load user profile
                val profileResponse = UserService.api.getUserProfile(userId)
                if (profileResponse.isSuccessful) {
                    val profile = profileResponse.body()
                    _userProfile.value = profile

                    // Extract user info from profile
                    profile?.let {
                        _userAvatar.value = it.profilePicture
                        // Note: The API doesn't seem to return username, 
                        // we might need to get it from another source or use userId
                        _userName.value = it.userName ?: "User" // Placeholder
                    }
                } else {
                    _error.value = "Failed to load user profile"
                    Timber.e("Failed to load user profile: ${profileResponse.code()}")
                }

                // Check follow status
                checkFollowStatus(userId)

            } catch (e: Exception) {
                _error.value = "Error loading profile: ${e.message}"
                Timber.e(e, "Error loading user profile")
            } finally {
                _isLoading.value = false
            }
        }
    }

    private suspend fun checkFollowStatus(userId: String) {
        try {
            val followResponse = CommunityService.api.checkFollowStatus(userId)
            if (followResponse.isSuccessful) {
                val followRes = followResponse.body()
                // Parse follow status from response
                _isFollowing.value =
                    followRes?.status == FollowStatus.Followed.value || followRes?.status == FollowStatus.Mutual.value
            }
        } catch (e: Exception) {
            Timber.e(e, "Error checking follow status")
        }
    }

    fun toggleFollow() {
        val userId = _currentUserId.value ?: return

        viewModelScope.launch {
            try {
                if (_isFollowing.value) {
                    // Unfollow
                    val response = CommunityService.api.unfollowUser(userId)
                    if (response.isSuccessful) {
                        _isFollowing.value = false
                    }
                } else {
                    // Follow
                    val response = CommunityService.api.followUser(userId)
                    if (response.isSuccessful) {
                        _isFollowing.value = true
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error toggling follow status")
            }
        }
    }

    fun clearError() {
        _error.value = null
    }
}
