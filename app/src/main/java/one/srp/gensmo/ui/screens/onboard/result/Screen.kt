package one.srp.gensmo.ui.screens.onboard.result

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import one.srp.gensmo.ui.navigation.NavActions

@Composable
fun OnboardResultScreen(navActions: NavActions = NavActions()) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text("TODO: Onboard Result Screen")
    }
}
