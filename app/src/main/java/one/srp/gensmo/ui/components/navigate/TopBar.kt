package one.srp.gensmo.ui.components.navigate

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TopBar(
    modifier: Modifier = Modifier,
    immersive: Boolean = false,
    transparent: Boolean = false,
    navIconEnable: Boolean = true,
    onBack: () -> Unit = {},
    action: (@Composable () -> Unit)? = null,
    content: @Composable (() -> Unit) = {},
) {
    TopAppBar(
        modifier = Modifier
            .zIndex(10f)
            .then(modifier),
        colors = TopAppBarDefaults.topAppBarColors(if (immersive || transparent) Color.Transparent else Color.Unspecified),
        navigationIcon = {
            if (navIconEnable) {
                ActionContainer(immersive, transparent) {
                    IconButton(onClick = { onBack() }) {
                        Icon(
                            modifier = Modifier.size(36.dp),
                            imageVector = Icons.Default.ChevronLeft,
                            contentDescription = "back"
                        )
                    }
                }
            }
        },
        actions = { action?.let { ActionContainer(immersive, transparent) { it() } } },
        title = content
    )
}

@Composable
private fun ActionContainer(
    immersive: Boolean = false,
    transparent: Boolean = false,
    content: @Composable () -> Unit,
) {
    if (!immersive || transparent) return content()

    Card(
        modifier = Modifier
            .size(56.dp)
            .padding(8.dp),
        shape = CircleShape,
        colors = CardDefaults.cardColors(Color.White),
    ) {
        content()
    }
}
