package one.srp.gensmo.ui.screens.user.library._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.background
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.data.model.TryOnHistoryItem
import one.srp.gensmo.utils.mime.resizeImage
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.window.PopupProperties
import androidx.compose.ui.unit.DpOffset
import androidx.compose.foundation.layout.Row
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import androidx.compose.ui.draw.shadow
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape

@Composable
fun TryonHistory(
    tryOnHistory: List<TryOnHistoryItem>,
    onTryOn: (String) -> Unit,
    onLoadMore: () -> Unit,
    onDelete: (String) -> Unit = {}
) {
    if (tryOnHistory.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_fitting),
                    contentDescription = "空白试衣图标",
                    modifier = Modifier.size(117.dp),
                    tint = Color.Unspecified
                )
                Text(
                    text = "Outfits you've tried on will appear here.",
                    color = Color(0xFF767676),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        textAlign = TextAlign.Center,
                    )
                )
            }
        }
    } else {
        LazyVerticalGrid(
            columns = GridCells.Fixed(3),
            contentPadding = PaddingValues(0.dp),
            horizontalArrangement = Arrangement.spacedBy(1.dp),
            verticalArrangement = Arrangement.spacedBy(1.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(tryOnHistory) { historyItem ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(0.75f)
                        .clip(RectangleShape),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RectangleShape,
                    onClick = {
                        onTryOn(historyItem.tryOnTaskId)
                    }
                ) {
                    Box(modifier = Modifier.fillMaxSize()) {
                        AsyncImage(
                            model = resizeImage(historyItem.tryOnUrl, 400),
                            contentDescription = "试衣历史图片",
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.Crop
                        )
                        
                        var showMenu by remember { mutableStateOf(false) }
                        
                        Box(
                            modifier = Modifier
                                .padding(8.dp)
                                .align(Alignment.TopEnd)
                                .size(24.dp)
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = null
                                ) {
                                    showMenu = true
                                }
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.icon_more_actions),
                                contentDescription = "更多操作",
                                modifier = Modifier.fillMaxSize(),
                                tint = Color.Black
                            )
                            
                            DropdownMenu(
                                expanded = showMenu,
                                onDismissRequest = { showMenu = false },
                                properties = PopupProperties(
                                    focusable = true,
                                    usePlatformDefaultWidth = false
                                ),
                                offset = DpOffset(-80.dp, 8.dp),
                                modifier = Modifier
                                    .width(120.dp)
                                    .shadow(elevation = 12.dp, spotColor = Color(0x1A2F536D), ambientColor = Color(0x1A2F536D))
                                    .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 4.dp))
                            ) {
                                DropdownMenuItem(
                                    text = {
                                        Row(
                                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Icon(
                                                painter = painterResource(id = R.drawable.icon_trashbin),
                                                contentDescription = "删除图标",
                                                modifier = Modifier.size(20.dp)
                                            )
                                            Text("Delete", style = AppThemeTextStyle.Body14LightH)
                                        }
                                    },
                                    onClick = {
                                        onDelete(historyItem.tryOnTaskId)
                                        showMenu = false
                                    },
                                    contentPadding = PaddingValues(horizontal = 12.dp, vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
            
            item(span = { GridItemSpan(3) }) {
                LaunchedEffect(Unit) {
                    onLoadMore()
                }
            }
        }
    }
}
