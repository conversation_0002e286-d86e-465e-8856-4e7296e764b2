package one.srp.gensmo.ui.screens.user.account

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.launch
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun UserAccountScreen(navActions: NavActions) {
    Scaffold(topBar = {
        TopBar(onBack = { navActions.back() }, transparent = true) {
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 40.dp)
            ) {
                Text(stringResource(R.string.text_account))
            }
        }
    }) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues)) {
            Box(modifier = Modifier.weight(1f)) {
                AccountInfo()
            }

            AccountAction(onPostDelete = { navActions.navigateToFeedRecommend() })
        }
    }
}

@Composable
private fun AccountInfo(viewModel: UserProfileViewModel = hiltViewModel()) {
    LaunchedEffect(Unit) {
        viewModel.getUserInfo()
    }

    val userName = viewModel.userName.collectAsState()
    val userAvatar = viewModel.userAvatar.collectAsState()

    Column {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            Box(
                modifier = Modifier
                    .size(96.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                AsyncImage(
                    model = when (userAvatar.value) {
                        "icon_random_thumb" -> R.drawable.icon_random_thumb
                        "icon_unregister" -> R.drawable.icon_unregister
                        else -> userAvatar.value
                    },
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                    error = painterResource(id = R.drawable.icon_unregister)
                )
            }
        }

        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(stringResource(R.string.text_username), style = AppThemeTextStyle.Body13H)
            TextField(
                modifier = Modifier.fillMaxWidth(),
                value = userName.value,
                onValueChange = {},
                enabled = false,
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AccountAction(
    viewModel: UserProfileViewModel = hiltViewModel(),
    onPostDelete: () -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    fun deleteAccount() {
        viewModel.deleteAccount()

        coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
            if (!sheetState.isVisible) {
                showDeleteConfirmation = false
                onPostDelete()
            }
        }
    }

    Column {
        TextButton(
            onClick = { showDeleteConfirmation = true },
            shape = MaterialTheme.shapes.small,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Image(painterResource(R.drawable.icon_trashbin), null)
                Text(
                    stringResource(R.string.text_delete_my_account),
                    style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray700),
                )
            }
        }
    }

    if (showDeleteConfirmation) {
        ModalBottomSheet(
            onDismissRequest = { showDeleteConfirmation = false },
            sheetState = sheetState,
            dragHandle = null,
        ) {
            Column(modifier = Modifier.background(MaterialTheme.colorScheme.surface)) {
                Column(
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.background)
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    Text(
                        stringResource(R.string.text_are_you_sure_you_want_to_delete_your_account),
                        style = AppThemeTextStyle.Body11H.copy(
                            AppThemeColors.Gray700
                        )
                    )

                    TextButton(
                        modifier = Modifier.fillMaxWidth(), onClick = {
                            deleteAccount()
                        }) {
                        Text(
                            stringResource(R.string.text_delete_account),
                            style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Red500)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Column(
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.background)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    TextButton(
                        modifier = Modifier.fillMaxWidth(), onClick = {
                            coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                                if (!sheetState.isVisible) {
                                    showDeleteConfirmation = false
                                }
                            }
                        }) {
                        Text(
                            stringResource(R.string.text_cancel),
                            style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray700)
                        )
                    }
                }
            }
        }
    }
}
