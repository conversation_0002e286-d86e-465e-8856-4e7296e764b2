package one.srp.gensmo.ui.screens.tryon.create._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import one.srp.gensmo.ui.theme.AppThemeTextStyle

enum class BodyTypeOption(val displayName: String) {
    SLIM("Slim"),
    FIT("Fit"),
    AVERAGE("Average"),
    CURVY("Curvy")
}

@Composable
fun BodyTypeSelector(
    selectedBodyType: BodyTypeOption,
    onBodyTypeSelected: (BodyTypeOption) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Body size",
            style = AppThemeTextStyle.Heading18D.copy(
                color = Color(0xFF000000),
                fontWeight = FontWeight(700)
            ),
            modifier = Modifier.padding(bottom = 8.dp).align(Alignment.Start)
        )
        Text(
            text = "Choose the size that best matches your body.",
            style = AppThemeTextStyle.Body12LightH.copy(
                fontWeight = FontWeight(400),
                color = Color(0xFF495057)
            ),
            modifier = Modifier.padding(bottom = 16.dp).align(Alignment.Start)
        )
        
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .selectableGroup(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 第一行：Slim 和 Fit
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                BodyTypeCard(
                    bodyType = BodyTypeOption.SLIM,
                    selected = BodyTypeOption.SLIM == selectedBodyType,
                    onSelect = { onBodyTypeSelected(BodyTypeOption.SLIM) },
                    modifier = Modifier.weight(1f, fill = false)
                )
                BodyTypeCard(
                    bodyType = BodyTypeOption.FIT,
                    selected = BodyTypeOption.FIT == selectedBodyType,
                    onSelect = { onBodyTypeSelected(BodyTypeOption.FIT) },
                    modifier = Modifier.weight(1f, fill = false)
                )
            }
            
            // 第二行：Average 和 Curvy
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                BodyTypeCard(
                    bodyType = BodyTypeOption.AVERAGE,
                    selected = BodyTypeOption.AVERAGE == selectedBodyType,
                    onSelect = { onBodyTypeSelected(BodyTypeOption.AVERAGE) },
                    modifier = Modifier.weight(1f, fill = false)
                )
                BodyTypeCard(
                    bodyType = BodyTypeOption.CURVY,
                    selected = BodyTypeOption.CURVY == selectedBodyType,
                    onSelect = { onBodyTypeSelected(BodyTypeOption.CURVY) },
                    modifier = Modifier.weight(1f, fill = false)
                )
            }
        }
    }
}

@Composable
fun BodyTypeCard(
    bodyType: BodyTypeOption,
    selected: Boolean,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = Color.White
    
    Card(
        modifier = modifier
            .selectable(
                selected = selected,
                onClick = onSelect,
            ),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 8.dp, vertical = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val resourceId = when (bodyType) {
                BodyTypeOption.SLIM -> one.srp.gensmo.R.drawable.card_slim
                BodyTypeOption.FIT -> one.srp.gensmo.R.drawable.card_fit
                BodyTypeOption.AVERAGE -> one.srp.gensmo.R.drawable.card_average
                BodyTypeOption.CURVY -> one.srp.gensmo.R.drawable.card_curvy
            }
            
            Image(
                painter = painterResource(id = resourceId),
                contentDescription = bodyType.displayName,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
                    .then(
                        if (selected) {
                            Modifier.border(
                                width = 2.dp,
                                color = Color(0xFF2C2C2C),
                                shape = RoundedCornerShape(16.dp)
                            )
                        } else {
                            Modifier
                        }
                    )
            )
            
            Text(
                text = bodyType.displayName,
                style = AppThemeTextStyle.Body12H.copy(
                    fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal
                ),
                color = if (selected) Color(0xFF2C2C2C) else Color(0xFF9C9C9C)
            )
        }
    }
}
