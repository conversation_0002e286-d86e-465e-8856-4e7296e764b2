package one.srp.gensmo.ui.screens.tryon.closet._components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun TaskLoadingCard(
    progress: Progress,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(3f/4f),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier.padding(16.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .drawBehind {
                            drawCircle(
                                color = Color.Gray.copy(alpha = 0.3f),
                                style = Stroke(width = 4.dp.toPx())
                            )

                            val rawProgress =
                                if (progress.total > 0) {
                                    progress.current.toFloat() / progress.total.toFloat()
                                } else 0f

                            drawArc(
                                color = Color(0xFF4924EF),
                                startAngle = -90f,
                                sweepAngle = 360f * rawProgress,
                                useCenter = false,
                                style = Stroke(
                                    width = 4.dp.toPx(),
                                    cap = StrokeCap.Round
                                )
                            )
                        },
                    contentAlignment = Alignment.Center
                ) {
                    val animatedProgress = animateFloatAsState(
                        targetValue = if (progress.total > 0) {
                            progress.current.toFloat() / progress.total.toFloat()
                        } else 0f,
                        animationSpec = tween(durationMillis = 500),
                        label = "进度动画"
                    ).value

                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .drawBehind {
                                drawArc(
                                    color = Color(0xFF4924EF),
                                    startAngle = -90f,
                                    sweepAngle = 360f * animatedProgress,
                                    useCenter = false,
                                    style = Stroke(
                                        width = 4.dp.toPx(),
                                        cap = StrokeCap.Round
                                    )
                                )
                            }
                    )

                    Text(
                        text = "${progress.total - progress.current} min left",
                        color = Color(0xFF4924EF),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500)
                        )
                    )
                }
                Text(
                    text = "Generating your AI Avatar...",
                    color = Color(0xFF0224FF),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        textAlign = TextAlign.Center,
                    )
                )
            }
        }
    }
} 