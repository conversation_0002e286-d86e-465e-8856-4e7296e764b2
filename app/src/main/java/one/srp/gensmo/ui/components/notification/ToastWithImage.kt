package one.srp.gensmo.ui.components.notification

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.rememberAsyncImagePainter
import coil3.request.ImageRequest
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.TransformOrigin

@Composable
fun ToastWithImage(
    text: String,
    imageUrl: String,
    visible: Boolean,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    onDismiss: (() -> Unit)? = null
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 32.dp),
        contentAlignment = Alignment.TopCenter
    ) {
        AnimatedVisibility(
            visible = visible,
            enter = fadeIn(animationSpec = tween(300)),
            exit = fadeOut(animationSpec = tween(300))
        ) {
            Surface(
                modifier = Modifier
                    .shadow(elevation = 0.dp, spotColor = Color(0x0D383838), ambientColor = Color(0x0D383838))
                    .shadow(elevation = 7.dp, spotColor = Color(0x0D383838), ambientColor = Color(0x0D383838))
                    .shadow(elevation = 13.dp, spotColor = Color(0x0A383838), ambientColor = Color(0x0A383838))
                    .shadow(elevation = 17.dp, spotColor = Color(0x08383838), ambientColor = Color(0x08383838))
                    .shadow(elevation = 20.dp, spotColor = Color(0x03383838), ambientColor = Color(0x03383838))
                    .shadow(elevation = 22.dp, spotColor = Color(0x00383838), ambientColor = Color(0x00383838))
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp)
                    .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 16.dp))
                    .padding(horizontal = 8.dp, vertical = 6.dp),
                color = Color.Transparent
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .graphicsLayer {
                                scaleX = 3.0f
                                scaleY = 3.0f
                                transformOrigin = TransformOrigin(0.5f, 0.0f)
                            }
                    ) {
                        Image(
                            painter = rememberAsyncImagePainter(
                                ImageRequest.Builder(LocalContext.current)
                                    .data(imageUrl)
                                    .build()
                            ),
                            contentDescription = null,
                            modifier = Modifier
                                .size(40.dp),
                            contentScale = ContentScale.Crop,
                            alignment = Alignment.TopCenter,
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = text,
                        color = Color(0xFF333333),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                        ),
                        modifier = Modifier.weight(1f)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    TextButton(
                        onClick = { 
                            onClick?.invoke()
                            onDismiss?.invoke()
                        },
                        modifier = Modifier.padding(0.dp),
                        colors = ButtonDefaults.textButtonColors(
                            containerColor = Color(0xFF191A1A)
                        ),
                        shape = RoundedCornerShape(4.dp)
                    ) {
                        Text(
                            text = "View",
                            color = Color.White,
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight(500),
                            ),
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                    }
                }
            }
        }
    }
}
