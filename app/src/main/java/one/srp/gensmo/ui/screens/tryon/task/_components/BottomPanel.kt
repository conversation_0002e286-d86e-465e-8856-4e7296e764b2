package one.srp.gensmo.ui.screens.tryon.task._components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedIconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R

@Composable
fun BottomPanel(
    saved: Boolean = false,
    showTryOnButton: Boolean = true,
    onChangeBg: () -> Unit = {},
    onRemix: () -> Unit = {},
    onTryOn: () -> Unit = {},
    onSave: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        ChangeBgButton(onClick = onChangeBg)
        RemixButton(onClick = onRemix)
        if (showTryOnButton) {
            TryOnProductButton(onClick = onTryOn)
        }

        Spacer(modifier = Modifier.weight(1f))

        SaveButton(saved, onClick = onSave)
    }
}

@Composable
fun ChangeBgButton(onClick: () -> Unit = {}) {
    IconButton(onClick = onClick) {
        Image(painterResource(R.drawable.icon_star), null, modifier = Modifier.size(24.dp))
    }
}

@Composable
private fun RemixButton(onClick: () -> Unit = {}) {
    IconButton(onClick = onClick) {
        Image(painterResource(R.drawable.icon_tryon_search), null, modifier = Modifier.size(24.dp))
    }
}

@Composable
fun TryOnProductButton(onClick: () -> Unit = {}) {
    IconButton(onClick = onClick) {
        Image(painterResource(R.drawable.icon_tryon_product), null, modifier = Modifier.size(24.dp))
    }
}

@Composable
fun SaveButton(saved: Boolean = false, onClick: () -> Unit = {}) {
    OutlinedIconButton(
        onClick = onClick,
        border = BorderStroke(1.dp, color = MaterialTheme.colorScheme.onTertiary)
    ) {
        Image(
            painterResource(if (saved) R.drawable.icon_saved_black else R.drawable.icon_saved_line),
            null,
            modifier = Modifier.size(24.dp),
        )
    }
}

