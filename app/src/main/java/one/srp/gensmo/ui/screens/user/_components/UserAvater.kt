package one.srp.gensmo.ui.screens.user._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun UserAvatar(
    modifier: Modifier = Modifier,
    editable: Boolean = false,
    onEdit: () -> Unit = {},
    usernameVisible: Boolean = true,
    viewModel: UserProfileViewModel = hiltViewModel(),
) {
    val userName = viewModel.userName.collectAsState()
    val userAvatar = viewModel.userAvatar.collectAsState()

    Column(
        modifier = Modifier.then(modifier),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box {
            Box(
                modifier = Modifier
                    .size(90.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                AsyncImage(
                    model = when (userAvatar.value) {
                        "icon_random_thumb" -> R.drawable.icon_random_thumb
                        "icon_unregister" -> R.drawable.icon_unregister
                        else -> userAvatar.value
                    },
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                    error = painterResource(id = R.drawable.icon_unregister)
                )
            }

            if (editable) {
                Button(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(24.dp),
                    shape = MaterialTheme.shapes.medium,
                    onClick = { onEdit() },
                    contentPadding = PaddingValues(4.dp),
                    colors = ButtonDefaults.buttonColors(
                        MaterialTheme.colorScheme.surface,
                        MaterialTheme.colorScheme.onSurface
                    )
                ) {
                    Image(painterResource(R.drawable.icon_edit), null)
                }
            }
        }

        if (usernameVisible) {
            Text("@${userName.value}", style = AppThemeTextStyle.Heading18D)
        }
    }
}