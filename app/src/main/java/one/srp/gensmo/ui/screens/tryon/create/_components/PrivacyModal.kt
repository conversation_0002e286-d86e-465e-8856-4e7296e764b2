package one.srp.gensmo.ui.screens.tryon.create._components

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import one.srp.gensmo.R

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacyModal(
    onDismiss: () -> Unit
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        sheetState = rememberModalBottomSheetState()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) { 
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Image(
                    painter = painterResource(id = R.drawable.icon_shield),
                    contentDescription = "Privacy Shield",
                    modifier = Modifier
                        .padding(end = 12.dp)
                        .size(24.dp)
                )
                
                val context = LocalContext.current
                Text(
                    text = buildAnnotatedString {
                        append("Your privacy matters. We're committed to keeping your data secure. ")
                        withStyle(
                            style = SpanStyle(
                                color = Color(0xFF929292),
                                textDecoration = TextDecoration.Underline
                            )
                        ) {
                            append("Learn more")
                        }
                    },
                    style = TextStyle(
                        fontSize = 16.sp,
                        color = Color(0xFF929292)
                    ),
                    modifier = Modifier.clickable {
                        val intent = Intent(Intent.ACTION_VIEW, "https://gensmo.com/about/privacy".toUri())
                        // 检查是否有应用可以处理这个 Intent
                        if (intent.resolveActivity(context.packageManager) != null) {
                            context.startActivity(intent)
                        }
                    }
                )
            }
        }
    }
}
