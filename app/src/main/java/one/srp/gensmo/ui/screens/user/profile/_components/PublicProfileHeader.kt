package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.button.ProfileFollowButton
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.PublicProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun PublicProfileHeader(
    modifier: Modifier = Modifier,
    navActions: NavActions = NavActions(),
    viewModel: PublicProfileViewModel,
) {
    val userName = viewModel.userName.collectAsState()
    val userAvatar = viewModel.userAvatar.collectAsState()
    val isFollowing = viewModel.isFollowing.collectAsState()
    val isLoading = viewModel.isLoading.collectAsState()

    Column(
        modifier = Modifier
            .height(300.dp)
            .then(modifier)
    ) {
        Row(modifier = Modifier.padding(vertical = 8.dp)) {
            IconButton(onClick = { navActions.back() }) {
                Icon(Icons.Default.ChevronLeft, null, modifier = Modifier.size(36.dp))
            }
        }

        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(), contentAlignment = Alignment.Center
        ) {
            PublicProfileAvatar(
                userName = userName.value,
                userAvatar = userAvatar.value,
                isFollowing = isFollowing.value,
                isLoading = isLoading.value,
                onFollowClick = { viewModel.toggleFollow() })
        }
    }
}

@Composable
private fun PublicProfileAvatar(
    userName: String,
    userAvatar: String?,
    isFollowing: Boolean,
    isLoading: Boolean,
    onFollowClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier.fillMaxHeight(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Avatar
        Box(
            modifier = Modifier
                .size(96.dp)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.surfaceVariant)
        ) {
            AsyncImage(
                model = when (userAvatar) {
                    "icon_random_thumb" -> R.drawable.icon_random_thumb
                    "icon_unregister" -> R.drawable.icon_unregister
                    else -> userAvatar
                },
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                error = painterResource(id = R.drawable.icon_unregister)
            )
        }

        // Username
        Text("@$userName", style = AppThemeTextStyle.Heading18D)

        // Follow Button
        ProfileFollowButton(
            onClick = onFollowClick,
            isFollowing = isFollowing,
        )
    }
}
