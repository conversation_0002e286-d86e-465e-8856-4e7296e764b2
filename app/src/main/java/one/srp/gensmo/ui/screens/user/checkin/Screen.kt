package one.srp.gensmo.ui.screens.user.checkin

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user._components.UserAvatar
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.integration.LaunchAction
import one.srp.gensmo.utils.integration.LaunchActionDetector

@Composable
fun UserCheckInScreen(navActions: NavActions = NavActions()) {
    val context = LocalContext.current
    var isFirstLaunch by remember { mutableStateOf(false) }
    LaunchedEffect(Unit) {
        isFirstLaunch = LaunchActionDetector.checkFirstLaunch(context, LaunchAction.USER_CHECK_IN)
    }

    val coroutineScope = rememberCoroutineScope()
    fun checkIn() {
        isFirstLaunch = false
        coroutineScope.launch {
            LaunchActionDetector.recordFirstLaunch(context, LaunchAction.USER_CHECK_IN)
        }
    }

    Scaffold(topBar = {
        TopBar(transparent = true, onBack = { navActions.back() }) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(stringResource(R.string.text_check__in), style = AppThemeTextStyle.Heading18D)
                Spacer(modifier = Modifier.size(40.dp))
            }
        }
    }) { paddingValues ->
        Box(modifier = Modifier.padding(paddingValues)) {
            Image(
                painterResource(R.drawable.image_user_check_in_card_bg),
                null,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                contentScale = ContentScale.FillWidth
            )

            Column(
                modifier = Modifier
                    .matchParentSize()
                    .padding(horizontal = 20.dp, vertical = 40.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Column(
                    modifier = Modifier
                        .weight(34f)
                        .padding(4.dp),
                    verticalArrangement = Arrangement.Center
                ) {
                    UserAvatar(usernameVisible = true)
                }

                Column(
                    verticalArrangement = Arrangement.Bottom,
                    modifier = Modifier.weight(26f)
                ) {
                    Image(
                        painterResource(if (isFirstLaunch) R.drawable.image_user_check_in_box_close else R.drawable.image_user_check_in_box_open),
                        null,
                        modifier = Modifier,
                    )
                }

                Spacer(modifier = Modifier.weight(8f))

                Column(modifier = Modifier.weight(32f)) {
                    Button(
                        enabled = isFirstLaunch,
                        onClick = { checkIn() },
                        shape = MaterialTheme.shapes.medium,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(54.dp)
                            .padding(horizontal = 32.dp)
                    ) {
                        Text(
                            stringResource(if (isFirstLaunch) R.string.text_check__in else R.string.text_checked__in),
                            style = AppThemeTextStyle.Body16H
                        )
                    }
                }
            }
        }
    }
}