package one.srp.gensmo.ui.screens.tryon.task._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.TryOnBackgroundParams
import one.srp.gensmo.data.model.TryOnBackgroundRes
import one.srp.gensmo.data.repository.TryOnBackgroundRepository
import one.srp.gensmo.data.repository.utils.PollState
import javax.inject.Inject

@HiltViewModel
class TryOnBackgroundViewModel @Inject constructor(
    private val repository: TryOnBackgroundRepository,
) : ViewModel() {
    private val placeholderKey = "_placeholder"

    private val _polling = MutableStateFlow<Boolean>(false)
    val polling = _polling.asStateFlow()

    private val _bgJobList = mutableListOf<Job>()
    private val _bgStateMap =
        MutableStateFlow<Map<String, PollState<TryOnBackgroundRes>>>(emptyMap())
    val bgStateMap = _bgStateMap.asStateFlow()

    fun submitBgChange(params: TryOnBackgroundParams): Boolean {
        if (_polling.value == true) return false

        viewModelScope.launch {
            _polling.update { true }
            _bgStateMap.update {
                it.toMutableMap().apply {
                    this[placeholderKey] = PollState.Idle
                }
            }
            repository.submitChangeBackground(params).collect {
                if (it.isSuccess) {
                    it.getOrNull()?.let { res ->
                        _bgStateMap.update {
                            it.toMutableMap().apply {
                                remove(placeholderKey)
                                this[res.generateUuid] = PollState.Idle
                            }
                        }
                        pollBgChange(res.generateUuid)
                    }
                }
            }
        }
        return true
    }

    fun pollBgChange(generateUuid: String) {
        val job = viewModelScope.launch {
            repository.pollChangeBackground(generateUuid).collect { state ->
                _bgStateMap.update {
                    it.toMutableMap().apply {
                        this[generateUuid] = state
                    }
                }
            }
        }
        job.invokeOnCompletion { _polling.update { false } }
        _bgJobList.add(job)
    }

    override fun onCleared() {
        _bgJobList.map {
            it.cancel()
        }
        _bgJobList.clear()
    }
}