package one.srp.gensmo.ui.screens.feed._components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.AccountCircle
import androidx.compose.material.icons.outlined.KeyboardArrowUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import one.srp.gensmo.viewmodel.feed.InputBoxViewModel
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.navigation.NavRoutes
import timber.log.Timber

@Composable
fun InputBox(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "Describe the style or occasion you want to explore...",
    onCameraClick: () -> Unit = {},
    onSparkleClick: () -> Unit = {},
    viewModel: InputBoxViewModel = hiltViewModel(),
    navActions: NavActions
) {
    val isNavigating by viewModel.isNavigating

    LaunchedEffect(navActions.currentRoute) {
        if (navActions.currentRoute == NavRoutes.Feed.Recommend.route) {
            Timber.d("resetNavigatingState: ${viewModel.getNavigatingState()}")
            viewModel.resetNavigatingState()
        }
    }

    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        shape = RoundedCornerShape(24.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Camera Icon Button
            IconButton(
                onClick = {
                    if (!isNavigating) {
                        viewModel.navigateToCamera()
                        onCameraClick()
                    }
                },
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = Color(0xFFF5F0FF),
                        shape = CircleShape
                    ),
                enabled = !isNavigating
            ) {
                Icon(
                    imageVector = Icons.Outlined.AccountCircle,
                    contentDescription = "Camera",
                    tint = Color(0xFF8B5CF6)
                )
            }

            // Sparkle Icon Button
            IconButton(
                onClick = onSparkleClick,
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = Color(0xFFF5F0FF),
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Outlined.KeyboardArrowUp,
                    contentDescription = "Sparkle",
                    tint = Color(0xFF8B5CF6)
                )
            }

            // Text Input Field
            BasicTextField(
                value = value,
                onValueChange = onValueChange,
                modifier = Modifier
                    .weight(1f)
                    .padding(horizontal = 8.dp),
                textStyle = TextStyle(
                    fontSize = 16.sp,
                    color = Color.Black
                ),
                decorationBox = { innerTextField ->
                    Box {
                        if (value.isEmpty()) {
                            Text(
                                text = placeholder,
                                color = Color.Gray,
                                fontSize = 16.sp
                            )
                        }
                        innerTextField()
                    }
                }
            )

            // Upload Arrow Button
            IconButton(
                onClick = { /* Handle upload */ },
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = Color(0xFFF5F0FF),
                        shape = CircleShape
                    )
            ) {
                Icon(
                    imageVector = Icons.Outlined.KeyboardArrowUp,
                    contentDescription = "Upload",
                    tint = Color(0xFF8B5CF6)
                )
            }
        }
    }
}

// Preview
@Preview(showBackground = true)
@Composable
fun InputBoxPreview() {
    var text by remember { mutableStateOf("") }
    InputBox(
        value = text,
        onValueChange = { text = it },
        navActions = NavActions()
    )
}
