package one.srp.gensmo.ui.screens.session._components

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Shape
import one.srp.gensmo.ui.theme.AppThemeColors

@Composable
fun MessageBox(
    modifier: Modifier = Modifier,
    shape: Shape = MaterialTheme.shapes.extraLarge,
    content: @Composable () -> Unit,
) {
    Card(
        modifier = Modifier.then(modifier),
        shape = shape,
        colors = CardDefaults.cardColors(AppThemeColors.Gray50)
    ) {
        Box(modifier = Modifier.clip(shape)) {
            content()
        }
    }
}