package one.srp.gensmo.ui.screens.user.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import one.srp.gensmo.R
import one.srp.gensmo.viewmodel.user.LoginUiState
import one.srp.gensmo.viewmodel.user.LoginViewModel
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.foundation.clickable
import one.srp.gensmo.ui.navigation.NavActions
/**
 * Demonstrate Firebase Authentication using a Google ID Token.
 */
@Composable
fun LoginScreen(
    viewModel: LoginViewModel = hiltViewModel(),
    onLoginSuccess: () -> Unit,
    navActions: NavActions
) {
    // 设置登录成功回调
    viewModel.setOnLoginSuccess(onLoginSuccess)

    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    val backgroundImage = ImageBitmap.imageResource(id = R.drawable.home_bg)

    Column(
        modifier = Modifier
            .fillMaxSize()
            .drawBehind {
                // 绘制整个屏幕的背景图片
                drawImage(
                    image = backgroundImage,
                    dstSize = IntSize(size.width.toInt(), size.height.toInt())
                )
            }
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 32.dp, end = 16.dp)
        ) {
            Text(
                text = "Skip",
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = 16.dp)
                    .clickable { navActions.back() },
                color = Color.Black
            )
        }

        Spacer(modifier = Modifier.height(44.dp))

        Image(
            painter = painterResource(id = R.drawable.gensmo_logo_vertical),
            contentDescription = "Gensmo Logo",
            modifier = Modifier
                .width(120.dp)
                .align(Alignment.CenterHorizontally)
                .weight(1f)
        )

        // 第二块：当前内容
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .padding(16.dp)
        ) {
            when (val state = uiState) {
                is LoginUiState.LoggedIn -> {
                    // 登录成功后直接触发回调，不需要显示内容
                    onLoginSuccess()
                }

                LoginUiState.LoggedOut -> {
                    Button(
                        onClick = { viewModel.startLogin(context) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                            .height(56.dp),
                        shape = RoundedCornerShape(28.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.White.copy(alpha = 0.5f))
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                painter = painterResource(id = R.drawable.icon_google),
                                contentDescription = "Google Icon",
                                modifier = Modifier.size(24.dp),
                                tint = Color.Unspecified
                            )
                            Text(
                                text = "Continue with Google",
                                modifier = Modifier.padding(start = 8.dp),
                                color = Color.Black
                            )
                        }
                    }

                    Button(
                        onClick = { viewModel.startAppleLogin(context) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                            .height(56.dp),
                        shape = RoundedCornerShape(28.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.White.copy(alpha = 0.5f))
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Icon(
                                painter = painterResource(id = R.drawable.icon_apple),
                                contentDescription = "Apple Icon",
                                modifier = Modifier.size(24.dp),
                                tint = Color.Unspecified
                            )
                            Text(
                                text = "Continue with Apple",
                                modifier = Modifier.padding(start = 8.dp),
                                color = Color.Black
                            )
                        }
                    }
                }

                is LoginUiState.Error -> {
                    Text(
                        text = state.message,
                        modifier = Modifier.padding(bottom = 8.dp),
                        color = Color.Black
                    )
                    Button(
                        onClick = { navActions.back() },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        shape = RoundedCornerShape(28.dp),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.White.copy(alpha = 0.5f))
                    ) {
                        Text(
                            text = "Go Back",
                            color = Color.Black
                        )
                    }
                }

                LoginUiState.Initial -> {
                    CircularProgressIndicator()
                }

                LoginUiState.Loading -> {
                    CircularProgressIndicator()
                }
            }
        }

        // 第三块：空白区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1.6f)
        ) {
            Image(
                painter = painterResource(id = R.drawable.start_illustration),
                contentDescription = "Start Illustration",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
        }
    }
}

// 用户信息数据类
data class UserInfo(
    val uid: String,
    val displayName: String?,
    val email: String?,
    val phoneNumber: String?,
    val photoUrl: String?,
    val isEmailVerified: Boolean,
    val providerId: String,
)