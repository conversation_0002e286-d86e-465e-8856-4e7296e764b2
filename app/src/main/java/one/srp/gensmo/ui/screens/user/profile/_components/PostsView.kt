package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.paging.compose.collectAsLazyPagingItems
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.ui.screens.feed._components.FeedCard
import one.srp.gensmo.ui.screens.feed._components.FeedList
import one.srp.gensmo.ui.screens.user.profile._viewmodel.PostsViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun PostsView(
    modifier: Modifier = Modifier,
    public: Boolean = false,
    targetUserId: String? = null,
    onItemClick: (FeedItem) -> Unit = {},
    onPostClick: () -> Unit = {},
    viewModel: PostsViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val metric = rememberMetricHelper(EventRefer.Profile)

    if (targetUserId == null) return EmptyPosts(public, onPostClick)

    val itemList = viewModel.getPublishHistoryFlow(targetUserId).collectAsLazyPagingItems()

    Box(modifier = modifier) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            if (itemList.itemCount == 0) {
                EmptyPosts(public = public, onPostClick = { onPostClick() })
            } else {
                FeedList(modifier = modifier, items = itemList) {
                    FeedCard(item = it, onItemClick = { onItemClick(it) })
                }
            }
        }
    }
}

@Composable
private fun EmptyPosts(public: Boolean = false, onPostClick: () -> Unit = {}) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(30.dp)
    ) {
        if (public) {
            // 公共资料页面：显示空状态图片和文案
            Image(painterResource(R.drawable.image_empty_post), null)

            Text(
                text = "No post yet",
                style = AppThemeTextStyle.Body14H.copy(AppThemeColors.Gray500),
                textAlign = TextAlign.Center,
            )
//        } else {
//            // 个人资料页面：显示收藏占位图片和引导文案
//            Image(painterResource(R.drawable.image_collects_placeholder), null)
//
//            Text(
//                text = stringResource(R.string.text_ready_to_share_a_look),
//                style = AppThemeTextStyle.Heading20D,
//                modifier = Modifier.width(222.dp),
//                maxLines = 2,
//                overflow = TextOverflow.Visible,
//                textAlign = TextAlign.Center,
//            )
//
//            Button(onClick = { onPostClick() }, shape = MaterialTheme.shapes.medium) {
//                Text(stringResource(R.string.text_get_started), style = AppThemeTextStyle.Body16H)
//            }
//        }
        } else {
            Image(painterResource(R.drawable.image_empty_post), null)

            Text(
                text = "You haven't collected any note yet",
                style = AppThemeTextStyle.Body14H.copy(AppThemeColors.Gray500),
                textAlign = TextAlign.Center,
            )
        }
    }
}
