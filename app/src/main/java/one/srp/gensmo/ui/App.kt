package one.srp.gensmo.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.compose.rememberNavController
import one.srp.gensmo.ui.components.notification.ToastManager
import one.srp.gensmo.ui.navigation.NavGraph
import one.srp.gensmo.ui.theme.AppTheme
import one.srp.gensmo.utils.integration.NavManager

@Composable
fun App() {
    val navController = rememberNavController()
    
    LaunchedEffect(navController) {
        // 统一使用NavManager管理导航
        NavManager.setNavController(navController)
    }

    AppTheme {
        NavGraph(navController = navController)
    }

    ToastManager.Toast()
}