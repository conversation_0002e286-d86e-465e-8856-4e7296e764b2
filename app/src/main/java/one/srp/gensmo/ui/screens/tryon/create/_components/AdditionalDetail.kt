package one.srp.gensmo.ui.screens.tryon.create._components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun AdditionalDetail(
    additionalInfo: String,
    onAdditionalInfoChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Additional details",
             style = AppThemeTextStyle.Heading18D.copy(
                color = Color(0xFF000000),
                fontWeight = FontWeight(700)
            ),
            modifier = Modifier.padding(bottom = 8.dp).align(Alignment.Start)
        )
        Text(
            text = "Add any extra measurements, preferences, or requests to further personalize your Avatar.",
            style = AppThemeTextStyle.Body12LightH.copy(
                fontWeight = FontWeight(400),
                color = Color(0xFF495057)
            ),
            modifier = Modifier.padding(bottom = 16.dp).align(Alignment.Start)
        )
        
        OutlinedTextField(
            value = additionalInfo,
            onValueChange = onAdditionalInfoChanged,
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp),
            placeholder = { Text("e.g. 175 cm / 70 kg", color = Color(0xFFB3B3B3)) },
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF1A1A1A),
                unfocusedBorderColor = Color(0xFFD9D9D9)
            ),
            textStyle = AppThemeTextStyle.Body14H.copy(
                fontWeight = FontWeight(400),
                color = Color(0xFF495057)
            )
        )
    }
}
