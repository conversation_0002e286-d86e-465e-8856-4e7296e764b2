package one.srp.gensmo.ui.screens.session.chat._components

import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import one.srp.gensmo.data.model.ChatMessage
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.TryOnQueryMessage
import one.srp.gensmo.data.model.UnexpectErrorMessage
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun ChatContainer(
    viewModel: SessionChatViewModel,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    val chatList = viewModel.chatList.collectAsState().value
    val visibleList = remember(chatList) { chatList.filter { it.visible == true } }
    val chatLock = viewModel.chatLock.collectAsState().value

    val lazyListState = rememberLazyListState()
    val nestedScrollConn = remember(visibleList) {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                val atBottom =
                    lazyListState.layoutInfo.visibleItemsInfo.lastOrNull()?.index == visibleList.lastIndex + 2

                return if (available.y < 0 && atBottom) {
                    Offset(x = 0f, y = available.y * 0.5f)
                } else {
                    Offset.Zero
                }
            }
        }
    }

    val context = LocalContext.current
    LaunchedEffect(chatList) {
        val last = visibleList.lastOrNull()
        last?.let { last ->
            when (last) {
                is UnexpectErrorMessage -> {
                    Toast.makeText(
                        context, "Well, that was unexpected! One more time?", Toast.LENGTH_SHORT
                    ).show()
                }

                else -> {}
            }
        }
    }

    val density = LocalDensity.current
    LaunchedEffect(visibleList) {
        if (visibleList.isNotEmpty()) {
            delay(200)

            val last = visibleList.last()
            if (last is SearchQueryMessage || last is TryOnQueryMessage) {
                lazyListState.animateScrollToItem(visibleList.size - 1)
            } else {
                // TODO: future scroll
                lazyListState.animateScrollToItem(
                    visibleList.size - 1, -(with(density) { 42.dp.toPx().toInt() })
                )
            }
        }
    }

    BoxWithConstraints {
        val maxHeight = this.maxHeight

        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(nestedScrollConn)
                .padding(bottom = 16.dp),
            state = lazyListState,
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(bottom = maxHeight - 120.dp),
        ) {
            items(visibleList.size) { index ->
                ChatGroup(
                    item = visibleList[index], onMessage = onMessage, onNavigate = onNavigate
                )
            }

            item {
                Box {
                    if (chatLock.input) {
//                        LaunchedEffect(Unit) {
//                            lazyListState.animateScrollToItem(
//                                lazyListState.layoutInfo.totalItemsCount - 1,
//                                -lazyListState.layoutInfo.viewportEndOffset + (with(density) {
//                                    32.dp.toPx().toInt()
//                                })
//                            )
//                        }

                        Row(
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                            horizontalArrangement = Arrangement.spacedBy(6.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            BaseLoading(modifier = Modifier.size(12.dp), strokeWidth = 1.5.dp)
                            Text(mockHintList.random(), style = AppThemeTextStyle.Body16H)
                        }
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

private val mockHintList = listOf(
    "Planning ahead…",
    "Thinking through your style…",
    "Analyzing your preferences…",
    "Setting the direction…"
)

@Composable
private fun ChatGroup(
    item: ChatMessage,
    onMessage: (ChatMessage) -> Unit = {},
    onNavigate: (SessionNavigation, ChatMessage, Int?) -> Unit = { _, _, _ -> },
) {
    when (item.role) {
        ChatMessageRole.User.value -> UserCard { ChatComponent(item, onMessage, onNavigate) }
        ChatMessageRole.Assistant.value -> AssistantCard {
            ChatComponent(
                item, onMessage, onNavigate
            )
        }

        else -> {}
    }
}

@Composable
private fun UserCard(
    modifier: Modifier = Modifier, content: @Composable () -> Unit,
) {
    Row(
        horizontalArrangement = Arrangement.End,
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 40.dp, end = 16.dp)
    ) {
        Box(
            modifier = Modifier.then(modifier)
        ) {
            content()
        }
    }
}

@Composable
private fun AssistantCard(
    modifier: Modifier = Modifier, content: @Composable () -> Unit,
) {
    Row(
        horizontalArrangement = Arrangement.Start, modifier = Modifier.fillMaxWidth()
    ) {
        Box(
            modifier = Modifier.then(modifier)
        ) {
            content()
        }
    }
}
