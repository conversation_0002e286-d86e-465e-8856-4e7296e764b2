package one.srp.gensmo.ui.screens.detail.collage

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalUriHandler
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.graphics.toColorInt
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ChatMessageRole
import one.srp.gensmo.data.model.CollectionType
import one.srp.gensmo.data.model.FeedCoverItem
import one.srp.gensmo.data.model.FeedCoverItemType
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.MoodboardContentBlock
import one.srp.gensmo.data.model.MoodboardTryOnItems
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchParams
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.data.model.SearchQueryMessageWrapper
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.remote.RemixService
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.data.utils.JSON
import one.srp.gensmo.ui.components.action.FollowAction
import one.srp.gensmo.ui.components.action.FollowStatus
import one.srp.gensmo.ui.components.button.FeedFollowButton
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.collage.CollageSharePanel
import one.srp.gensmo.ui.components.collage.MoodboardRenderer
import one.srp.gensmo.ui.components.collage.MoodboardTryOnPanel
import one.srp.gensmo.ui.components.collage.SharePanelDrawer
import one.srp.gensmo.ui.components.collage.TryOnPanelDrawer
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.detail._components.DetailDesc
import one.srp.gensmo.ui.screens.detail._components.ProductAction
import one.srp.gensmo.ui.screens.detail._components.ProductView
import one.srp.gensmo.ui.screens.session.chat._components.SaveActionContainer
import one.srp.gensmo.ui.screens.session.view._components.ActionDropdownMenu
import one.srp.gensmo.ui.screens.session.view._components.CarouselContainer
import one.srp.gensmo.ui.screens.session.view._components.DeletePostButton
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel
import timber.log.Timber
import java.util.UUID
import kotlin.math.max

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedDetailScreen(
    navActions: NavActions = NavActions(),
    feedItem: FeedItem? = null,
    moodboardId: String? = null,
    feedType: String? = null,
    openTryOn: (TryOnParams) -> Unit = {},
    onClear: () -> Unit = {},
    viewModel: FeedDetailViewModel = hiltViewModel(),
    onUpdateRemixProduct: (ProductItem?) -> Unit = {},
    createSession: (SearchQueryMessage) -> Unit = {},
    refer: EventRefer = EventRefer.FeedDetail,
) {
    val metric = rememberMetricHelper(refer)
    LaunchedEffect(Unit) {
        MetricData.logEventAF("af_feed_detail_view")
        metric(MetricEvent.SelectItem(EventItemListName.Screen, EventMethod.PageView))
    }

    DisposableEffect(Unit) {
        onDispose {
            onClear()
        }
    }

    // 拦截系统返回按钮，确保使用我们的安全返回逻辑
    BackHandler {
        navActions.back()
    }

    val coroutineScope = rememberCoroutineScope()
    val scrollState = rememberScrollState()
    val urlHandler = LocalUriHandler.current

    // 删除确认状态
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    val sheetState = rememberModalBottomSheetState()

    val result by viewModel.searchResult.collectAsState()
    val item = feedItem ?: result

    LaunchedEffect(moodboardId) {
        if (feedItem == null) {
            moodboardId?.let { viewModel.getCollageDetail(it) }
        }
    }

    // 删除帖子函数
    fun deletePost() {
        item?.let { feedItem ->
            val documentId = feedItem.moodboards?.id ?: return
            val title = ""
            val description = ""
            val type = "collage"

            viewModel.deletePost(
                feedType = type,
                documentId = documentId,
                title = title,
                description = description,
                onSuccess = {
                    coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                        if (!sheetState.isVisible) {
                            showDeleteConfirmation = false
                            navActions.back()
                        }
                    }
                },
                onFail = { error ->
                    // 可以在这里显示错误提示
                    coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                        if (!sheetState.isVisible) {
                            showDeleteConfirmation = false
                        }
                    }
                }
            )
        }
    }

    val jsonContent = remember(item) {
        item?.moodboards?.let {
            it.parsedContent ?: JSON.decodeFromString<MoodboardContent>(it.content)
        } ?: run { null }
    }

    var selectedBlock by remember { mutableStateOf<MoodboardContentBlock?>(null) }
    // Click handler for moodboard blocks: report metric and select block
    val onBlockClick: (MoodboardContentBlock) -> Unit = { block ->
        // 将 block 转换为对应的 ProductItem，以使用 product.globalId 作为埋点参数
        val product = item?.moodboards?.products?.firstOrNull { it.id == block.id }
        val eventItem = EventItem(
            itemCategory = EventItemCategory.Product,
            itemId = product?.globalId ?: block.id.orEmpty(),
            itemName = product?.title ?: block.content.content,
            index = -1
        )
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.CollageEntityListEntity,
                method = EventMethod.Click,
                actionType = EventActionType.Default,
                items = listOf(eventItem)
            )
        )
        selectedBlock = block
    }

    var shareOpen by remember { mutableStateOf(false) }
    var tryOnOpen by remember { mutableStateOf(false) }
    var tryOnItems by remember { mutableStateOf<MoodboardTryOnItems?>(null) }

    var searchPanelVisible by remember { mutableStateOf(false) }
    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf("") }

    val reportRemix = { id: String ->
        coroutineScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    RemixService.api.reportFeedRemix(id)
                }
            } catch (e: Exception) {
                // 如果发生401错误或其他异常，静默处理
                // 因为这是一个统计接口，失败不影响主要功能
                Timber.e(e)
            }
        }
    }
    val remixFeed = {
        item?.let {
            metric(
                MetricEvent.SelectItem(
                    itemListName = EventItemListName.RemixBottomBtn,
                    method = EventMethod.Click,
                    actionType = EventActionType.InitializeSearch
                )
            )
            reportRemix(it.moodboardId)
            capturedImageUri = it.imageUrl
            capturedSearchText = it.query ?: "Style with this"
            searchPanelVisible = true
        }
    }
    val onSearch = { q: String, img: String, stylesList: String?, budget: String? ->
        if (q.trim().isNotEmpty()) {
            Timber.d("onSearch: $q, $img, $stylesList, $budget")
            createSession(
                SearchQueryMessage(
                    sessionId = "default",
                    messageId = UUID.randomUUID().toString(),
                    role = ChatMessageRole.User.value,
                    visible = true,
                    value = SearchQueryMessageWrapper(
                        searchQuery = SearchParams(
                            query = q,
                            imageUrl = img,
                            debugLevel = 0,
                            budget = budget ?: "",
                            isAsync = true,
                            route = "",
                            isPresetQuery = false,
                            moodboardVersion = "v2",
                            inspoLabel = (stylesList?.split(",")?.map { it.trim() }
                                ?.filter { it.isNotEmpty() } ?: emptyList())))))
        }
    }
    val remixProduct = { product: ProductItem ->
        item?.let {
            reportRemix(it.moodboardId)
            onUpdateRemixProduct(product)

            onSearch("Complete the look", product.mainImage?.link ?: "", null, null)
        }
    }

    val openProductLink = { product: ProductItem ->
        product.link?.let { link ->
            val uri = link.toUri()
            val linkValid = uri.scheme != null && uri.host != null
            if (linkValid) {
                try {
                    metric(
                        MetricEvent.SelectItem(
                            method = EventMethod.Click,
                            itemListName = EventItemListName.ProductListProduct,
                            actionType = EventActionType.ProductExternalJump,
                            items = listOf(
                                EventItem(
                                    itemId = product.id,
                                    itemName = product.title,
                                    itemCategory = EventItemCategory.Product,
                                )
                            )
                        )
                    )
                    MetricData.logEventAF("af_product_external_jump")
                    urlHandler.openUri(link)
                } catch (err: Exception) {
                    Timber.e(err)
                }
            }
        }
    }

    val tryOnProduct = { product: ProductItem, shouldShowTryOnPanel: Boolean ->
        item?.let {
            coroutineScope.launch {
                val model = UserDataStoreManager.getModelInfo()
                metric(
                    MetricEvent.SelectItem(
                        itemListName = EventItemListName.TryOnBtn,
                        method = EventMethod.Click,
                        actionType = EventActionType.TryOn,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.Product,
                                itemId = product.globalId,
                                itemName = product.title
                            )
                        )
                    )
                )
                // 检查是否有可用的试穿选项
                tryOnItems = item.moodboards?.allTryOnItems

                if (shouldShowTryOnPanel && (tryOnItems?.box1 != null || tryOnItems?.box2 != null)) {
                    tryOnOpen = true
                } else {
                    val isFeedValue = feedType == "feed"
                    val params = TryOnParams(
                        modelId = (model.second ?: ""),
                        products = listOf(product),
                        moodboardId = item.moodboardId,
                        taskId = item.taskId,
                        internalImageList = product.mainImage?.link?.let { listOf(it) },
                        isFeed = isFeedValue,
                    )

                    openTryOn(params)
                }
            }
        }
    }

    var liked by remember(item) { mutableStateOf(item?.isLiked == true) }
    fun clickLike() {
        coroutineScope.launch {
            item?.moodboardId?.let { moodboardId ->
                val newLike = !liked
                liked = newLike
                item.isLiked = newLike
                item.likedCount = (item.likedCount ?: 0).let { count ->
                    if (newLike) count + 1 else max(count - 1, 0)
                }
                // 埋点：帖子详情页点赞按键点击
                metric(
                    MetricEvent.SelectItem(
                        itemListName = EventItemListName.LikeBtn,
                        method = EventMethod.Click,
                        actionType = if (newLike) EventActionType.Like else EventActionType.CancelLike,
                        items = listOf(
                            EventItem(
                                itemCategory = EventItemCategory.GeneralCollage,
                                itemId = moodboardId,
                                itemName = item.detailTitle
                            )
                        )
                    )
                )
                try {
                    if (newLike) {
                        UserService.api.likePost(moodboardId, CollectionType.Collage)
                    } else {
                        UserService.api.unlikePost(moodboardId, CollectionType.Collage)
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                    liked = !newLike
                    item.isLiked = !newLike
                    item.likedCount = (item.likedCount ?: 0).let { count ->
                        if (newLike) max(count - 1, 0) else count + 1
                    }
                }
            }
        }
    }

    var sharedCount by remember(item) { mutableIntStateOf(item?.sharedCount ?: 0) }
    fun clickShare() {
        // 埋点：帖子详情页分享按键点击，带上帖子的 ID
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.ShareBtn,
                method = EventMethod.Click,
                actionType = EventActionType.Default,
                items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.GeneralCollage,
                        itemId = item?.moodboardId.orEmpty(),
                        itemName = item?.detailTitle.orEmpty()
                    )
                )
            )
        )
        shareOpen = true

        coroutineScope.launch {
            item?.let { item ->
                item.moodboardId.let {
                    try {
                        UserService.api.sharePost(it, CollectionType.Collage)
                    } catch (e: Exception) {
                        Timber.e(e)
                    }
                }

                item.sharedCount = (item.sharedCount ?: 0) + 1
                sharedCount = (item.sharedCount ?: 0) + 1
            }
        }
    }

    var saved by remember(item) { mutableStateOf(item?.isFavorited == true) }
    fun clickSave(newSaved: Boolean) {
        item?.moodboardId?.let { moodboardId ->
            // 埋点：帖子详情页保存按键点击，带上帖子的 ID
            metric(
                MetricEvent.SelectItem(
                    itemListName = EventItemListName.SaveBtn,
                    method = EventMethod.Click,
                    actionType = if (newSaved) EventActionType.Save else EventActionType.Unsave,
                    items = listOf(
                        EventItem(
                            itemCategory = EventItemCategory.GeneralCollage,
                            itemId = moodboardId,
                            itemName = item.detailTitle.orEmpty()
                        )
                    )
                )
            )
        }
    }

    var productSaveTrigger by remember { mutableIntStateOf(0) }

    val carouselItemList = remember(item) {
        val list =
            item?.coverImageList?.filter { it.imageType == FeedCoverItemType.COLLAGE.value || (it.hiddenInFront == false && !it.coverUrl.isNullOrBlank()) }
        if (!list.isNullOrEmpty()) {
            list
        } else {
            listOf(FeedCoverItem(imageType = FeedCoverItemType.COLLAGE.value))
        }
    }

    val uid = item?.createdUserId ?: item?.userId
    fun gotoPublicProfile(userId: String? = null) {
        userId?.let {
            navActions.navigateToUserPublicProfile(userId)
        }
    }

    Scaffold(
        topBar = {
            TopBar(
                immersive = true,
                onBack = { navActions.back() },
                modifier = Modifier.background(Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(end = 16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Row(
                        modifier = Modifier.clickable { gotoPublicProfile(uid) },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        item?.userInfo?.userImageUrl?.let { url ->
                            AsyncImage(
                                model = url,
                                contentDescription = "user avatar",
                                modifier = Modifier
                                    .size(32.dp)
                                    .clip(CircleShape),
                                contentScale = ContentScale.Crop
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        item?.userInfo?.userName?.let { name ->
                            Text(
                                text = name, style = AppThemeTextStyle.Body11H
                            )
                        }
                    }

                    Spacer(modifier = Modifier.weight(1f))

                    item?.userInfo?.let { userInfo ->
                        uid?.let { uid ->
                            val selfId = runBlocking { UserDataStoreManager.getUserId() }

                            if (uid == selfId) {
                                // 删除按钮 dropdown menu
                                ActionDropdownMenu(
                                    menu = {
                                        DeletePostButton(
                                            onClick = { showDeleteConfirmation = true }
                                        )
                                    }
                                )
                            } else {
                                val initState = when (userInfo.followStatus) {
                                    FollowStatus.Followed.value, FollowStatus.Mutual.value -> true
                                    else -> false
                                }
                                FollowAction(
                                    initialState = initState,
                                    userId = uid,
                                    onFollow = {
                                        item.userInfo?.followStatus = FollowStatus.Followed.value
                                    },
                                    refer = refer,
                                ) { isFollowing, clickFollow ->
                                    FeedFollowButton(
                                        modifier = Modifier
                                            .height(36.dp)
                                            .width(100.dp),
                                        onClick = { clickFollow() },
                                        isFollowing = isFollowing,
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
    ) { paddingValues ->
        item?.let {
            val containerBg = jsonContent?.background?.let { bg ->
                if (bg.startsWith("#")) Color(bg.trim().toColorInt()) else Color.White
            } ?: Color.White

            Column(
                modifier = Modifier
                    .verticalScroll(scrollState)
                    .padding(
                        top = paddingValues.calculateTopPadding(),
                        bottom = paddingValues.calculateBottomPadding()
                    )
            ) {
                Box {
                    CarouselContainer(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(0.7f)
                            .clipToBounds(),
                        containerModifier = Modifier.aspectRatio(0.75f),
                        items = carouselItemList,
                        indicator = true,
                    ) { it ->
                        when (it.imageType) {
                            FeedCoverItemType.COLLAGE.value -> {
                                Box(
                                    contentAlignment = Alignment.Center,
                                    modifier = Modifier
                                        .background(containerBg)
                                        .fillMaxSize()
                                ) {
                                    item.moodboards?.let {
                                        MoodboardRenderer(
                                            modifier = Modifier.fillMaxHeight(),
                                            item = jsonContent,
                                            onItemClick = { onBlockClick(it) },
                                            animate = true,
                                            interactive = true,
                                            boundary = "h",
                                        )
                                    }
                                }
                            }

                            else -> {
                                Box(
                                    contentAlignment = Alignment.Center,
                                    modifier = Modifier.fillMaxSize(),
                                ) {
                                    AsyncImage(
                                        it.coverUrl,
                                        null,
                                        contentScale = ContentScale.FillHeight,
                                        modifier = Modifier.fillMaxHeight(),
                                    )
                                }

                            }
                        }
                    }

                    if (item.isTryOn == true) {
                        Row(
                            modifier = Modifier
                                .padding(vertical = 32.dp)
                                .align(Alignment.BottomCenter),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Button(
                                onClick = {
                                    item.moodboards?.products?.firstOrNull()?.let { product ->
                                        tryOnProduct(product, true)
                                    }
                                },
                                modifier = Modifier.height(40.dp),
                                shape = MaterialTheme.shapes.large,
                                colors = ButtonDefaults.outlinedButtonColors(
                                    containerColor = AppThemeColors.White,
                                    contentColor = AppThemeColors.Black
                                ),
                                contentPadding = PaddingValues(vertical = 2.dp, horizontal = 12.dp),
                                elevation = ButtonDefaults.buttonElevation(1.dp),
                            ) {
                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Image(painterResource(R.drawable.icon_try_on_star), null)

                                    Text(
                                        text = stringResource(R.string.text_try_on),
                                        style = AppThemeTextStyle.Body16H,
                                        color = Color.Black
                                    )
                                }
                            }
                        }
                    }
                }

                SaveActionContainer(
                    type = CollectionType.Collage,
                    id = item.moodboardId,
                    initialState = saved,
                    onPreAction = {
                        clickSave(it)
                    },
                    onAction = {
                        item.isFavorited = it
                    }
                ) { state, onClick ->
                    DetailDesc(
                        item = item,
                        modifier = Modifier.padding(bottom = 16.dp),
                        liked = liked,
                        onLikeClick = { clickLike() },
                        sharedCount = sharedCount,
                        onShareClick = { clickShare() },
                        saved = state,
                        onSaveClick = { onClick() },
                        navActions = navActions
                    )
                }


                item.moodboards?.products?.let { products ->
                    LaunchedEffect(Unit) {
                        metric(
                            MetricEvent.ViewItemList(
                                itemListName = EventItemListName.CollageEntityList,
                                items = products.mapIndexed { index, it ->
                                    EventItem(
                                        itemId = it.id,
                                        itemName = it.brand,
                                        itemCategory = EventItemCategory.Product,
                                        index = index
                                    )
                                })
                        )
                    }
                    ProductView(
                        refer = refer,
                        renderTrigger = productSaveTrigger,
                        products = products,
                        onProductClick = { product -> openProductLink(product) }
                    )
                }

                SharePanelDrawer(
                    open = shareOpen,
                    onClose = { shareOpen = false },
                ) {
                    CollageSharePanel(item = item.moodboards)
                }

                item.moodboards?.let {
                    ProductAction(
                        selectedBlock,
                        moodboard = it,
                        onClose = { selectedBlock = null },
                        onRemix = { product -> remixProduct(product) },
                        onTryOn = { product -> tryOnProduct(product, false) },
                    )
                }

                // ProductInfoDrawer removed - products now open external links directly
            }
        } ?: run {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = paddingValues.calculateBottomPadding()),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        }
    }

    // 添加 TryOnPanelDrawer
    tryOnItems?.let {
        TryOnPanelDrawer(open = tryOnOpen, onClose = { tryOnOpen = false }) {
            MoodboardTryOnPanel(
                items = it,
                onClose = { tryOnOpen = false },
                onTryOn = { selectedItems ->
                    tryOnOpen = false
                    item?.let { item ->
                        coroutineScope.launch {
                            val model = UserDataStoreManager.getModelInfo()
                            val selectedProducts = selectedItems.mapNotNull { selected ->
                                item.moodboards?.products?.find { product -> product.globalId == selected.itemId }
                            }

                            val imageType = selectedItems.filter { it.itemType == "user_image" }

                            val isFeedValue = feedType == "feed"

                            val params = TryOnParams(
                                modelId = model.second ?: "",
                                moodboardId = item.moodboardId,
                                taskId = item.taskId,
                                internalImageList = selectedItems.mapNotNull { p -> p.imageUrl },
                                products = selectedProducts,
                                userImage = imageType.firstOrNull()?.imageUrl,
                                userImageTag = imageType.firstOrNull()?.garmentType,
                                isFeed = isFeedValue,
                            )

                            openTryOn(params)
                        }
                    }
                })
        }
    }

    // 添加SearchPanel组件
    SearchPanel(
        isVisible = searchPanelVisible && !showCameraPicker,
        onDismiss = {
            searchPanelVisible = false
            capturedImageUri = null
            capturedSearchText = ""
        },
        viewModel = hiltViewModel(),
        imageUrl = capturedImageUri,
        searchQueryText = capturedSearchText,
        onCameraRequest = {
            showCameraPicker = true
        },
        onSearch = { q, img, stylesList, budget ->
            onSearch(q, img, stylesList, budget)
        },
        onUpdateQuery = { query ->
            capturedSearchText = query
        },
        navActions = navActions,
        placeholderText = stringResource(R.string.text_describe_an_occasion_vibe_or_something_you_want_help_with)
    )

    // 添加CameraPicker组件
    if (showCameraPicker) {
        CameraPicker(onPhotoTaken = { uri, searchQueryWords ->
            capturedImageUri = uri.toString()
            capturedSearchText += searchQueryWords
            showCameraPicker = false
        }, onMiss = {
            showCameraPicker = false
            capturedImageUri = null
        })
    }

    // 删除确认弹窗
    if (showDeleteConfirmation) {
        ModalBottomSheet(
            onDismissRequest = { showDeleteConfirmation = false },
            sheetState = sheetState,
            dragHandle = null,
        ) {
            Column(modifier = Modifier.background(MaterialTheme.colorScheme.surface)) {
                Column(
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.background)
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                ) {
                    Text(
                        stringResource(R.string.text_are_you_sure_you_want_to_delete_this_post),
                        style = AppThemeTextStyle.Body11H.copy(
                            AppThemeColors.Gray700
                        )
                    )

                    TextButton(
                        modifier = Modifier.fillMaxWidth(), onClick = {
                            deletePost()
                        }) {
                        Text(
                            stringResource(R.string.text_delete_post),
                            style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Red500)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Column(
                    modifier = Modifier
                        .background(MaterialTheme.colorScheme.background)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    TextButton(
                        modifier = Modifier.fillMaxWidth(), onClick = {
                            coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                                if (!sheetState.isVisible) {
                                    showDeleteConfirmation = false
                                }
                            }
                        }) {
                        Text(
                            stringResource(R.string.text_cancel),
                            style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray700)
                        )
                    }
                }
            }
        }
    }
}

