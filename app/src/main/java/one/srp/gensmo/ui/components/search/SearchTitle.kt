package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun SearchTitle(
    imageUrl: String?,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(
            modifier = Modifier
                .weight(0.2f)
                .padding(end = 60.dp),
        ) {
            Text(
                text = "Look for every wear",
                style = AppThemeTextStyle.Heading24D.copy(lineHeight = 36.sp),
                modifier = Modifier.padding(bottom = 4.dp)
            )
        }

        if (imageUrl != null) {
            AsyncImage(
                model = coil3.request.ImageRequest.Builder(LocalContext.current)
                    .data(imageUrl)
                    .size(coil3.size.Size.ORIGINAL)
                    .build(),
                contentDescription = "Replica Avatar",
                contentScale = ContentScale.Crop,
                alignment = Alignment.TopCenter,
                modifier = Modifier
                    .border(
                        width = 1.dp,
                        color = Color(0xFFFFFFFF),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .width(44.dp)
                    .height(44.dp)
                    .clip(RoundedCornerShape(size = 4.dp))
                    .graphicsLayer {
                        scaleX = 3f
                        scaleY = 3f
                        transformOrigin = TransformOrigin(0.5f, 0.0f)
                    }
            )
        } else {
            Image(
                painter = painterResource(id = R.drawable.profile_default),
                contentDescription = "Default Replica Avatar",
                modifier = Modifier
                    .border(
                        width = 1.dp,
                        color = Color(0xFFFFFFFF),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .width(44.dp)
                    .height(44.dp)
                    .clip(RoundedCornerShape(size = 4.dp))
            )
        }
    }
}
