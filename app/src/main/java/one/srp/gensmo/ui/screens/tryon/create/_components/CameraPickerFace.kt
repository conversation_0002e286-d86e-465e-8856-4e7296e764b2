package one.srp.gensmo.ui.screens.tryon.create._components

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.Uri
import android.view.Surface
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Cameraswitch
import androidx.compose.material.icons.filled.FlashOff
import androidx.compose.material.icons.filled.FlashOn
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import one.srp.gensmo.R
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import one.srp.gensmo.ui.components.camera.rememberImagePicker
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.zIndex
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun CameraPickerFace(
    onPhotoTaken: (String) -> Unit,
    onMiss: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    var lensFacing by remember { mutableIntStateOf(CameraSelector.LENS_FACING_FRONT) }
    var preview by remember { mutableStateOf<Preview?>(null) }
    var imageCapture by remember { mutableStateOf<ImageCapture?>(null) }
    var camera by remember { mutableStateOf<Camera?>(null) }
    var shouldRebindCamera by remember { mutableStateOf(true) }
    var isCapturing by remember { mutableStateOf(false) }
    var previewEnabled by remember { mutableStateOf(true) }
    var flashEnabled by remember { mutableStateOf(false) }
    var hasFlash by remember { mutableStateOf(false) }
    var showTips by remember { mutableStateOf(true) }

    // 添加权限状态
    var hasCameraPermission by remember {
        mutableStateOf(
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED
        )
    }

    // 权限请求启动器
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        hasCameraPermission = isGranted
    }

    // 请求权限
    LaunchedEffect(Unit) {
        if (!hasCameraPermission) {
            permissionLauncher.launch(Manifest.permission.CAMERA)
        }
    }

    // 使用 ImagePicker 组件
    val imagePicker = rememberImagePicker(
        onImageSelected = { uri ->
            onPhotoTaken(uri.toString())
        },
        onError = { e ->
            e.printStackTrace()
        }
    )

    LaunchedEffect(lensFacing, shouldRebindCamera) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            val cameraProvider = cameraProviderFuture.get()

            preview = Preview.Builder()
                .setTargetRotation(Surface.ROTATION_0)
                .build()

            imageCapture = ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                .setTargetRotation(Surface.ROTATION_0)
                .setFlashMode(if (flashEnabled) ImageCapture.FLASH_MODE_ON else ImageCapture.FLASH_MODE_OFF)
                .build()

            val cameraSelector = CameraSelector.Builder()
                .requireLensFacing(lensFacing)
                .build()

            try {
                cameraProvider.unbindAll()
                camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageCapture
                )
                hasFlash = camera?.cameraInfo?.hasFlashUnit() == true
                camera?.cameraControl?.enableTorch(flashEnabled)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }, ContextCompat.getMainExecutor(context))
    }

    // 添加一个函数来释放相机资源
    fun releaseCamera() {
        preview?.surfaceProvider = null
        preview = null
        imageCapture = null
        camera = null
        ProcessCameraProvider.getInstance(context).get().unbindAll()
    }

    if (hasCameraPermission) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                val iconSize = 68

                // 添加状态栏高度的空间
                Spacer(modifier = Modifier.height(24.dp))

                // 顶部按钮栏
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    IconButton(
                        onClick = { 
                            releaseCamera()
                            onMiss() 
                        }
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.icon_close_round),
                            contentDescription = "返回",
                            tint = Color.White
                        )
                    }

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        IconButton(
                            onClick = {
                                if (!isCapturing && hasFlash) {
                                    flashEnabled = !flashEnabled
                                    camera?.cameraControl?.enableTorch(flashEnabled)
                                }
                            },
                            enabled = !isCapturing && lensFacing == CameraSelector.LENS_FACING_BACK && hasFlash
                        ) {
                            Icon(
                                imageVector = if (flashEnabled) Icons.Default.FlashOn else Icons.Default.FlashOff,
                                contentDescription = if (flashEnabled) "关闭闪光灯" else "开启闪光灯",
                                tint = Color.White.copy(
                                    alpha = if (!isCapturing && lensFacing == CameraSelector.LENS_FACING_BACK && hasFlash) 1f else 0.5f
                                )
                            )
                        }

                        IconButton(
                            onClick = {
                                if (!isCapturing) {
                                    lensFacing = if (lensFacing == CameraSelector.LENS_FACING_BACK) {
                                        CameraSelector.LENS_FACING_FRONT
                                    } else {
                                        CameraSelector.LENS_FACING_BACK
                                    }
                                    shouldRebindCamera = !shouldRebindCamera
                                }
                            },
                            enabled = !isCapturing
                        ) {
                            Icon(
                                imageVector = Icons.Default.Cameraswitch,
                                contentDescription = "切换相机",
                                tint = Color.White.copy(
                                    alpha = if (isCapturing) 0.5f else 1f
                                )
                            )
                        }
                    }
                }

                // 相机预览区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(3f / 4f)
                ) {
                    AndroidView(
                        factory = { context ->
                            PreviewView(context).apply {
                                implementationMode = PreviewView.ImplementationMode.COMPATIBLE
                                scaleType = PreviewView.ScaleType.FILL_CENTER
                            }
                        },
                        modifier = Modifier.fillMaxSize()
                    ) { previewView ->
                        if (previewEnabled) {
                            preview?.surfaceProvider = previewView.surfaceProvider
                        } else {
                            preview?.surfaceProvider = null
                        }
                    }

                    // 拍照提示文字
                    Text(
                        text = "Look ahead with head in the outline.",
                        style = TextStyle(
                            fontSize = 16.sp,
                            lineHeight = 28.sp,
                            fontWeight = FontWeight(600),
                            fontStyle = FontStyle.Italic,
                            color = Color.White,
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(PaddingValues(horizontal = 16.dp, vertical = 16.dp))
                    )

                    // 遮罩图标
                    Icon(
                        painter = painterResource(id = R.drawable.mask_face),
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .fillMaxWidth(),
                        tint = Color.White
                    )
                }

                // 底部控制区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.Black.copy(alpha = 0.7f))
                        .padding(top = 32.dp, bottom = 32.dp)
                ) {
                    // 相册选择按钮（左下角）
                    IconButton(
                        onClick = { imagePicker.launch() },
                        enabled = !isCapturing,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(start = 32.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.btn_upload_image),
                            contentDescription = "从相册选择",
                            tint = Color.White.copy(
                                alpha = if (isCapturing) 0.5f else 1f
                            )
                        )
                    }

                    // 拍照按钮
                    IconButton(
                        onClick = {
                            if (!isCapturing && imageCapture != null) {
                                isCapturing = true
                                previewEnabled = false
                                takePhoto(
                                    context = context,
                                    imageCapture = imageCapture,
                                    onPhotoCaptured = { uri ->
                                        isCapturing = false
                                        previewEnabled = true
                                        releaseCamera()
                                        onPhotoTaken(uri.toString())
                                    },
                                    onError = {
                                        isCapturing = false
                                        previewEnabled = true
                                    }
                                )
                            }
                        },
                        enabled = !isCapturing,
                        modifier = Modifier
                            .size(iconSize.dp)
                            .align(Alignment.Center)
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.btn_shutter),
                            contentDescription = "拍照",
                            modifier = Modifier.size(iconSize.dp),
                            alpha = if (isCapturing) 0.5f else 1f
                        )
                    }

                     // 提示按钮
                    IconButton(
                        onClick = { showTips = !showTips },
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(end = 32.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.icon_info_outline),
                            contentDescription = "提示",
                            tint = Color.White.copy(
                                alpha = 1f
                            )
                        )
                    }
                }
            }

            // 提示框
            if (showTips) {
                Box(
                    modifier = Modifier
                        .width(360.dp)
                        .align(Alignment.TopCenter)
                        .offset(y = 460.dp)
                        .background(Color.Black.copy(alpha = 0.8f))
                        .padding(8.dp)
                        .zIndex(1f)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Row(
                            modifier = Modifier.fillMaxWidth().padding(horizontal = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Front-facing, bright lighting, solo, full face.",
                                style = AppThemeTextStyle.Body13H,
                                color = Color.White,
                                modifier = Modifier.weight(1f)
                            )
                            IconButton(
                                onClick = { showTips = false },
                                modifier = Modifier.size(24.dp)
                            ) {
                                Icon(
                                    painter = painterResource(id = R.drawable.icon_close_round),
                                    contentDescription = "关闭提示",
                                    tint = Color.White
                                )
                            }
                        }
                        Image(
                            painter = painterResource(id = R.drawable.tryon_tips),
                            contentDescription = "提示图片",
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 8.dp, bottom = 0.dp),
                            contentScale = ContentScale.FillWidth
                        )
                    }
                }
            }
        }

        // 加载指示器
        if (isCapturing) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color.White
                )
            }
        }
    } else {
        // 显示无权限提示
        Box(
            modifier = Modifier.fillMaxSize().background(Color.White),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "Permission required to use this feature",
                    color = Color.Black,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                IconButton(
                    onClick = { onMiss() }
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_close_round),
                        contentDescription = "返回",
                        tint = Color.Black
                    )
                }
            }
        }
    }
}

private fun takePhoto(
    context: Context,
    imageCapture: ImageCapture?,
    onPhotoCaptured: (Uri) -> Unit,
    onError: () -> Unit,
) {
    imageCapture?.let {
        val photoFile = File(
            context.getExternalFilesDir(null),
            SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.CHINA)
                .format(System.currentTimeMillis()) + ".jpg"
        )

        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()

        it.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(outputFileResults: ImageCapture.OutputFileResults) {
                    val savedUri = Uri.fromFile(photoFile)
                    onPhotoCaptured(savedUri)
                }

                override fun onError(exception: ImageCaptureException) {
                    exception.printStackTrace()
                    onError()
                }
            }
        )
    } ?: onError()
}
