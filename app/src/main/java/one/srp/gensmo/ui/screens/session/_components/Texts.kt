package one.srp.gensmo.ui.screens.session._components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import one.srp.gensmo.ui.theme.AppThemeFontFamily
import one.srp.gensmo.ui.theme.AppThemeTextStyle

data class BracketContent(
    val ori: String,
    val before: String,
    val inside: String,
    val after: String,
)

@Composable
fun TryOnSpanText(
    text: String,
    style: TextStyle = AppThemeTextStyle.Body16H,
    onClick: () -> Unit = {},
) {
    val parts = extractContentParts(text)
    val fullText = parts.ori.replace("<${parts.inside}>", parts.inside)
    val startIndex = fullText.indexOf(parts.inside)
    val endIndex = startIndex + parts.inside.length

    val annotatedString = buildAnnotatedString {
        append(fullText)
        addStyle(
            style = SpanStyle(
                color = Color(0xFF000000),
                fontFamily = AppThemeFontFamily.BodyDefault,
                fontSize = style.fontSize,
                fontWeight = FontWeight(600),
                textDecoration = TextDecoration.Underline
            ), start = startIndex, end = endIndex
        )
        addStringAnnotation(
            tag = "URL", annotation = parts.inside, start = startIndex, end = endIndex
        )
    }

    Text(
        text = annotatedString,
        style = style,
        maxLines = 2,
        modifier = Modifier.clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = null,
            onClick = onClick
        )
    )
}

fun extractContentParts(text: String? = null): BracketContent {
    if (text == null) return BracketContent(text ?: "", "", "", "")

    val pattern = "(.+)<(.+)>(.+)".toRegex()
    return pattern.find(text)?.let { match ->
        BracketContent(
            text,
            before = match.groupValues[1].trim(),
            inside = match.groupValues[2].trim(),
            after = match.groupValues[3].trim()
        )
    } ?: BracketContent(text, "", "", "")
}
