package one.srp.gensmo.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.em
import androidx.compose.ui.unit.sp
import one.srp.gensmo.R

object AppThemeFontFamily {
    val IbmPlexMono = FontFamily(Font(resId = R.font.ibm_plex_mono))
    val DmSerifDisplay = FontFamily(Font(resId = R.font.dm_serif_display))
    val DmSerifText = FontFamily(Font(resId = R.font.dm_serif_text))
    val Outfit = FontFamily(Font(resId = R.font.outfit))
    val SonsieOne = FontFamily(Font(resId = R.font.sonsie_one))
    val Lobster = FontFamily(Font(resId = R.font.lobster))
    val Inter = FontFamily(Font(resId = R.font.inter))

    // TODO: Add fonts
//    val Helvetica = FontFamily(Font(resId = R.font.helvetica))
//    val Didot = FontFamily(Font(resId = R.font.didot))

    val LibreCaslonText = FontFamily(Font(resId = R.font.libre_caslon_text))

    val TitleDefault = LibreCaslonText
    val BodyDefault = FontFamily.Default
}

object AppThemeTextStyle {
    val Heading32D = TextStyle(
        fontFamily = AppThemeFontFamily.TitleDefault,
        fontWeight = FontWeight.W700,
        fontSize = 32.sp,
        lineHeight = 32.sp,
        letterSpacing = (-0.01).em
    )
    val Heading24D = TextStyle(
        fontFamily = AppThemeFontFamily.TitleDefault,
        fontWeight = FontWeight.W700,
        fontSize = 24.sp,
        lineHeight = 24.sp,
        letterSpacing = (-0.01).em
    )
    val Heading20D = TextStyle(
        fontFamily = AppThemeFontFamily.TitleDefault,
        fontWeight = FontWeight.W700,
        fontSize = 20.sp,
        lineHeight = 20.sp,
        letterSpacing = (-0.01).em
    )
    val Heading18D = TextStyle(
        fontFamily = AppThemeFontFamily.TitleDefault,
        fontWeight = FontWeight.W700,
        fontSize = 18.sp,
        lineHeight = 18.sp,
        letterSpacing = (-0.02).em
    )
    val Heading16D = TextStyle(
        fontFamily = AppThemeFontFamily.TitleDefault,
        fontWeight = FontWeight.W700,
        fontSize = 16.sp,
        lineHeight = 16.sp,
        letterSpacing = (-0.02).em
    )
    val Heading16H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W700,
        fontSize = 16.sp,
        lineHeight = 16.sp,
        letterSpacing = (-0.02).em
    )
    val Heading14H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W700,
        fontSize = 14.sp,
        lineHeight = 14.sp,
        letterSpacing = 0.em
    )
    val Heading12H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W700,
        fontSize = 12.sp,
        lineHeight = 12.sp,
        letterSpacing = 0.em
    )
    val Body16H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W400,
        fontSize = 16.sp,
        lineHeight = 20.sp,
        letterSpacing = (-0.02).em
    )
    val Body16LightH = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W300,
        fontSize = 16.sp,
        lineHeight = 20.sp,
        letterSpacing = (-0.02).em
    )
    val Body14H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W400,
        fontSize = 14.sp,
        lineHeight = 17.5.sp,
        letterSpacing = 0.em
    )
    val Body14LightH = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W300,
        fontSize = 14.sp,
        lineHeight = 17.5.sp,
        letterSpacing = 0.em
    )
    val Body13H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W400,
        fontSize = 13.sp,
        lineHeight = 16.5.sp,
        letterSpacing = 0.em
    )
    val Body13LightH = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W300,
        fontSize = 13.sp,
        lineHeight = 16.5.sp,
        letterSpacing = 0.em
    )
    val Body12H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W400,
        fontSize = 12.sp,
        lineHeight = 15.sp,
        letterSpacing = 0.sp
    )
    val Body12LightH = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W300,
        fontSize = 12.sp,
        lineHeight = 15.sp,
        letterSpacing = 0.em
    )
    val Body11H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W400,
        fontSize = 11.sp,
        lineHeight = 13.5.sp,
        letterSpacing = 0.sp
    )
    val Body11LightH = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W300,
        fontSize = 11.sp,
        lineHeight = 13.5.sp,
        letterSpacing = 0.sp
    )
    val Body10H = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W400,
        fontSize = 10.sp,
        lineHeight = 12.5.sp,
        letterSpacing = 0.sp
    )
    val Body10LightH = TextStyle(
        fontFamily = AppThemeFontFamily.BodyDefault,
        fontWeight = FontWeight.W300,
        fontSize = 10.sp,
        lineHeight = 12.5.sp,
        letterSpacing = 0.sp
    )
}

val appThemeTypography = AppThemeTextStyle.run {
    Typography(
        displayLarge = Heading32D,
        displayMedium = Heading24D,
        displaySmall = Heading20D,

        headlineLarge = Heading32D,
        headlineMedium = Heading24D,
        headlineSmall = Heading20D,

        titleLarge = Heading20D,
        titleMedium = Heading16D,
        titleSmall = Heading14H,

        bodyLarge = Body16H,
        bodyMedium = Body14H,
        bodySmall = Body12H,

        labelLarge = Body14LightH,
        labelMedium = Body12LightH,
        labelSmall = Body10LightH
    )
}