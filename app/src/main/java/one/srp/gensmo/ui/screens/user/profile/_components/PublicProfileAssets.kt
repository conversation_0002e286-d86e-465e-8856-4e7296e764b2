package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Icon
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.PublicProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun PublicProfileAssets(
    modifier: Modifier = Modifier,
    navActions: NavActions = NavActions(),
    onClearFeed: () -> Unit = {},
    viewModel: PublicProfileViewModel,
) {
    val tabs = listOf("Posts", "Saved")
    var selectedTab by remember { mutableIntStateOf(0) }
    val pagerState = rememberPagerState(pageCount = { tabs.size })
    val coroutineScope = rememberCoroutineScope()

    // 使用 by 委托来正确处理状态变化
    val currentUserId by viewModel.currentUserId.collectAsState()

    // Sync tab selection with pager
    LaunchedEffect(pagerState.currentPage) {
        selectedTab = pagerState.currentPage
    }

    Column(modifier = modifier.fillMaxSize()) {
        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            modifier = Modifier.fillMaxWidth(),
            containerColor = Color.Transparent,
            divider = {},
            indicator = { tabPositions ->
                if (selectedTab < tabPositions.size) {
                    BoxWithConstraints(contentAlignment = Alignment.BottomCenter) {
                        val paddingH = (this.maxWidth - 110.dp) / tabPositions.size / 2
                        TabRowDefaults.SecondaryIndicator(
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTab])
                                .padding(horizontal = paddingH),
                        )
                    }
                }
            },
        ) {
            tabs.forEachIndexed { index, tabLabel ->
                Tab(
                    selected = selectedTab == index,
                    onClick = {},
                    text = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            if (selectedTab == index) {
                                Text(
                                    text = tabLabel,
                                    style = AppThemeTextStyle.Body16H
                                )
                            } else {
                                Text(
                                    text = tabLabel,
                                    style = AppThemeTextStyle.Body16LightH.copy(AppThemeColors.Gray700)
                                )
                            }

                            if (tabLabel == "Saved") {
                                Icon(
                                    painterResource(R.drawable.icon_lock),
                                    null,
                                    modifier = Modifier.size(16.dp)
                                )
                            }
                        }
                    }
                )
            }
        }

        // Content Pager
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            when (page) {
                0 -> PostsView(
                    public = true,
                    targetUserId = currentUserId,
                    onItemClick = { item ->
                        onClearFeed()

                        item.tryOnTaskId?.let { id ->
                            navActions.navigateToTryOnDetail(id, "feed")
                        } ?: run {
                            if (item.moodboardId.isNotBlank()) {
                                navActions.navigateToFeedDetail(item.moodboardId, "feed")
                            }
                        }
                    },
                    onPostClick = {
                        navActions.navigateToPostEditor(type = "collage")
                    }
                )

                1 -> {}
            }
        }
    }
}
