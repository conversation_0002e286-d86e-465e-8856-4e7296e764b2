package one.srp.gensmo.ui.screens.collage.task

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.data.model.MoodboardContentBlock
import one.srp.gensmo.data.model.MoodboardEntity
import one.srp.gensmo.data.model.MoodboardTryOnGarmentItem
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.SearchInspoItem
import one.srp.gensmo.data.model.SearchItem
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.remote.RemixService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.camera.CameraPicker
import one.srp.gensmo.ui.components.collage.CollageSharePanel
import one.srp.gensmo.ui.components.collage.MoodboardRenderer
import one.srp.gensmo.ui.components.collage.MoodboardTryOnPanel
import one.srp.gensmo.ui.components.collage.SharePanelDrawer
import one.srp.gensmo.ui.components.collage.ShoppingPanel
import one.srp.gensmo.ui.components.collage.TryOnPanelDrawer
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.BottomBar
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.components.search.SearchPanel
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.product.panel.alternatives.ProductAlternativesDrawer
import one.srp.gensmo.utils.render.renderComposeToBitmap
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel
import one.srp.gensmo.viewmodel.search.SearchExtensionViewModel
import timber.log.Timber

@Composable
fun CollageTaskScreen(
    navActions: NavActions = NavActions(),
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
    taskId: String? = null,
    openTryOn: (TryOnParams) -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    val metric = rememberMetricHelper(EventRefer.CollageGen)

    val query by collageSearchViewModel.query.collectAsState()
    val imageURI by collageSearchViewModel.imageURI.collectAsState()
    val result by collageSearchViewModel.searchResult.collectAsState()

    // 添加SearchPanel相关状态
    var searchPanelVisible by remember { mutableStateOf(false) }
    var showCameraPicker by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<String?>(null) }
    var capturedSearchText by remember { mutableStateOf<String?>(null) }

    // 获取正确类型的ViewModel
    val searchExtensionViewModel: SearchExtensionViewModel = hiltViewModel()

    LaunchedEffect(taskId) {
        if (result == null) {
            taskId?.let { collageSearchViewModel.pollTask(it) }
        }
    }

    val moodboardContentList = remember(result?.moodboards) {
        result?.moodboards?.mapNotNull { it.parsedContent }
    }
    var moodboardIndex by remember { mutableIntStateOf(0) }
    val moodboard =
        remember(result?.moodboards, moodboardIndex) { result?.moodboards?.get(moodboardIndex) }

    var selectedBlock by remember { mutableStateOf<MoodboardContentBlock?>(null) }
    val selectedProduct = remember(selectedBlock) {
        selectedBlock?.let { block -> moodboard?.products?.find { it.globalId == block.globalId } }
    }

    val remixProduct = { p: ProductItem ->
        moodboard?.let {
            coroutineScope.launch {
                try {
                    // 先报告，再导航
                    RemixService.api.reportFeedRemix(it.id)
                    // 报告成功后执行导航
                    navActions.navigateToCollageSearch(
                        query = "Style with this", imageUrl = p.mainImage?.link
                    )
                } catch (e: Exception) {
                    // 如果报告失败，记录错误但继续导航
                    Timber.e(e, "Failed to report remix for id: ${it.id}")
                    navActions.navigateToCollageSearch(
                        query = "Style with this", imageUrl = p.mainImage?.link
                    )
                }
            }
        }
    }
    val clickInspo = { index: Int, inspo: SearchInspoItem ->
        metric(
            MetricEvent.SelectItem(
                EventItemListName.InspoListInspo,
                EventMethod.Click,
                EventActionType.CollageGen,
                items = listOf(
                    EventItem(
                        itemName = inspo.showQuery,
                        itemCategory = EventItemCategory.RecoInspo,
                        index = index
                    )
                )
            )
        )

        collageSearchViewModel.updateInspo(listOf(inspo))
        navActions.navigateToCollageSearch(
            query = inspo.searchQuery ?: "",
            imageUrl = imageURI,
            stylesList = inspo.showQuery
        )
    }

    val tryOnMoodboard = { selects: List<MoodboardTryOnGarmentItem> ->
        val selectIds = selects.map { it.itemId }
        result?.let { result ->
            moodboard?.let { moodboard ->
                coroutineScope.launch {
                    val model = UserDataStoreManager.getModelInfo()
                    metric(
                        MetricEvent.SelectItem(
                            EventItemListName.TryOnBtn,
                            EventMethod.Click,
                            EventActionType.TryOn
                        )
                    )

                    val products = moodboard.products.filter { it.globalId in selectIds }

                    val imageType = selects.filter { it.itemType == "user_image" }

                    val params = if (!model.second.isNullOrEmpty()) {
                        TryOnParams(
                            modelId = model.second,
                            moodboardId = moodboard.id,
                            taskId = result.taskId,
                            products = products,
                            internalImageList = selects.mapNotNull { p -> p.imageUrl },
                            userImage = imageType.firstOrNull()?.imageUrl,
                            userImageTag = imageType.firstOrNull()?.garmentType,
                        )
                    } else {
                        TryOnParams(
                            moodboardId = moodboard.id,
                            taskId = result.taskId,
                            products = products,
                            internalImageList = selects.mapNotNull { p -> p.imageUrl },
                            userImage = imageType.firstOrNull()?.imageUrl,
                            userImageTag = imageType.firstOrNull()?.garmentType,
                        )
                    }
                    openTryOn(params)
                }
            }
        }
    }
    val tryOnProduct = { product: ProductItem ->
        result?.let { result ->
            moodboard?.let { item ->
                coroutineScope.launch {
                    val model = UserDataStoreManager.getModelInfo()

                    metric(
                        MetricEvent.SelectItem(
                            EventItemListName.TryOnBtn,
                            EventMethod.Click,
                            EventActionType.TryOn,
                            items = listOf(
                                EventItem(
                                    itemCategory = EventItemCategory.Product,
                                    itemId = product.globalId,
                                    itemName = product.title,
                                )
                            )
                        )
                    )
                    val params = if (!model.second.isNullOrEmpty()) {
                        TryOnParams(
                            modelId = model.second,
                            products = listOf(product),
                            moodboardId = item.id,
                            taskId = result.taskId,
                            internalImageList = product.mainImage?.link?.let { listOf(it) },
                        )
                    } else {
                        TryOnParams(
                            products = listOf(product),
                            moodboardId = item.id,
                            taskId = result.taskId,
                            internalImageList = product.mainImage?.link?.let { listOf(it) },
                        )
                    }
                    openTryOn(params)
                }
            }
        }
    }

    result?.let { result ->
        Scaffold(topBar = {
            TopBar(immersive = true, onBack = { navActions.back() }, action = {
                ActionMenu(
                    moodboard, result, collageSearchViewModel = collageSearchViewModel
                )
            }) {
                QueryCard(query, imageURI, onQueryChange = { query, imageURI ->
                    metric(
                        MetricEvent.SelectItem(
                            EventItemListName.InputBox, EventMethod.Click,
                        )
                    )
                    capturedSearchText = query
                    capturedImageUri = imageURI
                    searchPanelVisible = true
                })
            }
        }, bottomBar = {
            BottomBar(immersive = true) {
                ExtensionPanel(
                    query = query,
                    imageUrl = imageURI,
                    moodboard = moodboard,
                    onInspoClick = { index, it -> clickInspo(index, it) },
                    onPreferenceClick = {
                        metric(
                            MetricEvent.SelectItem(
                                EventItemListName.PreferencesBtn,
                                EventMethod.Click
                            )
                        )
                        navActions.navigateToPreference("collage")
                    },
                    onTryOnClick = { tryOnMoodboard(it) },
                    collageSearchViewModel = collageSearchViewModel,
                    result = result,
                )
            }
        }) { paddingValues ->
            Box {
                val metric = rememberMetricHelper(EventRefer.CollageGen)
                LaunchedEffect(result) {
                    result.moodboards?.let {
                        metric(
                            MetricEvent.ViewItemList(
                                EventItemListName.CollageList,
                                result.moodboards.mapIndexed { index, it ->
                                    EventItem(
                                        itemId = it.id,
                                        itemName = result.reasoning,
                                        itemCategory = EventItemCategory.GeneralCollage,
                                        index = index
                                    )
                                })
                        )
                    }
                }

                MoodboardCarousel(
                    moodboardContentList,
                    onIndexChange = {
                        result.moodboards?.let { moodboards ->
                            metric(
                                MetricEvent.SelectItem(
                                    method = EventMethod.Swipe,
                                    itemListName = EventItemListName.CollageList,
                                    items = listOf(
                                        EventItem(
                                            itemId = moodboards[it].id,
                                            itemName = result.reasoning,
                                            itemCategory = EventItemCategory.GeneralCollage,
                                            index = it
                                        )
                                    )
                                )
                            )
                        }
                        moodboardIndex = it
                    },
                    onItemClick = {
                        selectedBlock = it
                        metric(
                            MetricEvent.SelectItem(
                                EventItemListName.CollageListEntityListEntity,
                                EventMethod.Click,
                                items = listOf(
                                    it.globalId?.let {
                                        val p = moodboard?.products?.find { p -> p.globalId == it }
                                        EventItem(
                                            itemId = p?.globalId,
                                            itemName = p?.title,
                                            itemCategory = EventItemCategory.Product
                                        )
                                    } ?: run {
                                        EventItem(
                                            EventItemCategory.InputItem,
                                            null,
                                            it.content.content
                                        )
                                    }
                                )
                            )
                        )
                    },
                    paddingValues = paddingValues,
                    result = result,
                )

                moodboard?.let {
                    ProductAction(
                        selectedProduct,
                        productMap = result.searchProductList,
                        moodboard = it,
                        onClose = { selectedBlock = null },
                        onRemix = { p -> remixProduct(p) },
                        onTryOn = { p -> tryOnProduct(p) },
                        collageSearchViewModel = collageSearchViewModel
                    )
                }
            }
        }
    } ?: run {
        BaseLoading(modifier = Modifier.fillMaxSize())
    }

    // 显示SearchPanel
    SearchPanel(
        placeholderText = query,
        isVisible = searchPanelVisible && !showCameraPicker,
        onDismiss = {
            searchPanelVisible = false
            capturedImageUri = null
            capturedSearchText = null
        },
        viewModel = searchExtensionViewModel,
        imageUrl = capturedImageUri,
        searchQueryText = capturedSearchText ?: "",
        onCameraRequest = {
            showCameraPicker = true
        },
        onUpdateQuery = { query ->
            capturedSearchText = query
        },
        onSearch = { searchText, displayImageUrl, stylesList, budget ->
            if (searchText.trim().isNotEmpty()) {
                navActions.navigateToCollageSearch(
                    query = searchText,
                    imageUrl = displayImageUrl,
                    stylesList = stylesList,
                    budget = budget
                )
            }
        },
        navActions = navActions
    )

    // 处理CameraPicker
    if (showCameraPicker) {
        CameraPicker(
            onPhotoTaken = { uri, searchQueryWords ->
                capturedImageUri = uri.toString()
                capturedSearchText += searchQueryWords
                showCameraPicker = false
            },
            onMiss = {
                showCameraPicker = false
                capturedImageUri = null
                // capturedSearchText = null
            }
        )
    }
}

@Composable
fun QueryCard(
    text: String? = null,
    imageURI: String? = null,
    onQueryChange: (String?, String?) -> Unit = { _, _ -> },
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .padding(vertical = 4.dp),
        shape = CircleShape,
        elevation = CardDefaults.cardElevation(2.dp),
        colors = CardDefaults.cardColors(Color.White),
        onClick = { onQueryChange(text, imageURI) }) {
        Row(
            modifier = Modifier.fillMaxSize(), verticalAlignment = Alignment.CenterVertically
        ) {
            if (!imageURI.isNullOrEmpty()) {
                AsyncImage(
                    model = imageURI,
                    contentDescription = null,
                    modifier = Modifier
                        .size(32.dp)
                        .padding(start = 12.dp)
                        .clip(CircleShape)
                )
            }

            Text(
                text = text ?: "",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(horizontal = 12.dp),
                style = MaterialTheme.typography.bodyLarge,
            )
        }
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ActionMenu(
    item: MoodboardEntity? = null,
    result: SearchItem? = null,
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    val metric = rememberMetricHelper(EventRefer.CollageGen)

    var expanded by remember { mutableStateOf(false) }

    var shareOpen by remember { mutableStateOf(false) }

    var shoppingOpen by remember { mutableStateOf(false) }
    val shoppingSheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val saved = collageSearchViewModel.moodboardSaveStateMap.getOrElse(item?.id ?: "") { false }
    val saveCollection = {
        val targetState = !saved

        item?.let {
            if (targetState == false) {
                metric(
                    MetricEvent.SelectItem(
                        method = EventMethod.Click,
                        itemListName = EventItemListName.SavedBtn,
                        actionType = EventActionType.Unsave,
                        items = listOf(
                            EventItem(
                                itemId = item.id,
                                itemName = result?.reasoning,
                                itemCategory = EventItemCategory.GeneralCollage,
                            )
                        )
                    )
                )
            } else {
                metric(
                    MetricEvent.SelectItem(
                        method = EventMethod.Click,
                        itemListName = EventItemListName.SavedBtn,
                        actionType = EventActionType.Save,
                        items = listOf(
                            EventItem(
                                itemId = item.id,
                                itemName = result?.reasoning,
                                itemCategory = EventItemCategory.GeneralCollage,
                            )
                        )
                    )
                )
            }

            collageSearchViewModel.setSaveState(item.id, targetState)
            coroutineScope.launch {
                delay(700)
                expanded = false
            }

            coroutineScope.launch {
                try {
                    if (result != null && targetState) {
                        item.parsedContent?.let {
                            val bitmap = renderComposeToBitmap(
                                context, it.width.toInt(), (it.height ?: (it.width / 0.7)).toInt()
                            ) {
                                MoodboardRenderer(
                                    modifier = Modifier.fillMaxWidth(),
                                    item = it,
                                    offscreen = true
                                )
                            }
                            collageSearchViewModel.updatePreviewFromBitmap(item, result, bitmap)
                        }
                    }
                } catch (err: Exception) {
                    Timber.e(err)
                }
            }
        }
    }

    Box {
        IconButton(
            onClick = {
                metric(MetricEvent.SelectItem(EventItemListName.MenuBtn, EventMethod.Click))
                expanded = !expanded
            },
            colors = IconButtonDefaults.iconButtonColors(
                MaterialTheme.colorScheme.background,
                MaterialTheme.colorScheme.onBackground
            ),
        ) {
            Icon(
                imageVector = Icons.Default.MoreHoriz,
                contentDescription = "more",
                modifier = Modifier
                    .aspectRatio(1f)
                    .padding(4.dp)
            )
        }


        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            containerColor = Color.White,
        ) {
            ShareButton(onClick = {
                shareOpen = true
                expanded = false
                metric(MetricEvent.SelectItem(EventItemListName.ShareBtn, EventMethod.Click))
            })
            ShoppingButton(onClick = {
                shoppingOpen = true
                expanded = false
                metric(MetricEvent.SelectItem(EventItemListName.ShopBagBtn, EventMethod.Click))
            })
            SaveButton(
                onClick = { saveCollection() }, enable = saved
            )
        }


        SharePanelDrawer(open = shareOpen, onClose = { shareOpen = false }) {
            CollageSharePanel(item, result)
        }

        item?.let {
            if (shoppingOpen) {
                ModalBottomSheet(
                    onDismissRequest = { shoppingOpen = false },
                    sheetState = shoppingSheetState,
                    containerColor = Color.White
                ) {
                    ShoppingPanel(item)
                }
            }
        }
    }
}

@Composable
private fun ShareButton(onClick: () -> Unit = {}) {
    DropdownMenuItem(
        text = { Text(stringResource(R.string.text_share)) },
        onClick = onClick,
        leadingIcon = {
            Image(
                painter = painterResource(R.drawable.icon_share_line),
                contentDescription = null,
                modifier = Modifier.size(24.dp),
            )
        })
}

@Composable
private fun ShoppingButton(onClick: () -> Unit = {}) {
    DropdownMenuItem(
        text = { Text(stringResource(R.string.text_shopping)) },
        onClick = onClick,
        leadingIcon = {
            Image(
                painter = painterResource(R.drawable.icon_bag_line),
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .padding(1.dp)
            )
        })
}

@Composable
private fun SaveButton(
    onClick: () -> Unit = {},
    enable: Boolean = false,
) {
    Box {
        DropdownMenuItem(
            text = { Text(stringResource(R.string.text_save)) },
            onClick = onClick,
            leadingIcon = {
                Image(
                    painter = painterResource(
                        if (enable) R.drawable.icon_bookmark_color else R.drawable.icon_bookmark_line
                    ),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                )
            })
    }
}

@Composable
private fun MoodboardCarousel(
    moodboards: List<MoodboardContent>? = null,
    onIndexChange: (Int) -> Unit = {},
    onItemClick: (MoodboardContentBlock) -> Unit = {},
    paddingValues: PaddingValues = PaddingValues(0.dp),
    result: SearchItem? = null,
) {
    val pagerState = rememberPagerState(pageCount = {
        moodboards?.size ?: 0
    })

    LaunchedEffect(moodboards?.size) {
        if (pagerState.currentPage >= (moodboards?.size ?: 0)) {
            pagerState.scrollToPage(0)
        }
    }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            onIndexChange(page)
        }
    }

    Box {
        HorizontalPager(
            state = pagerState, modifier = Modifier.fillMaxSize()
        ) { index ->
            moodboards?.get(index)?.let {
                val scrollState = rememberScrollState()
                val containerBg = it.background?.let { bg ->
                    if (bg.startsWith("#")) Color(bg.trim().toColorInt()) else Color.White
                } ?: Color.White

                Column(
                    modifier = Modifier
                        .background(containerBg)
                        .fillMaxSize()
                        .verticalScroll(scrollState)
                ) {
                    Box(modifier = Modifier.padding(paddingValues)) {
                        val metric = rememberMetricHelper(EventRefer.CollageGen)
                        LaunchedEffect(Unit) {
                            metric(
                                MetricEvent.ViewItemList(
                                    EventItemListName.CollageListEntityList,
                                    items = result?.moodboards?.get(index)?.products?.mapIndexed { index, p ->
                                        EventItem(
                                            itemId = p.globalId,
                                            itemName = p.title,
                                            itemCategory = EventItemCategory.Product,
                                            index = index
                                        )
                                    } ?: emptyList()
                                )
                            )
                        }

                        MoodboardRenderer(
                            modifier = Modifier.fillMaxWidth(),
                            item = it,
                            onItemClick = { block -> onItemClick(block) },
                            animate = true,
                            interactive = true,
                        )
                    }
                }
            }
        }


        Row(
            Modifier
                .wrapContentHeight()
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .offset(y = -paddingValues.calculateBottomPadding())
                .padding(bottom = 8.dp, top = 16.dp),
            horizontalArrangement = Arrangement.Center,
        ) {
            Card(colors = CardDefaults.cardColors(Color.White.copy(0.7f))) {
                Row(
                    modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    repeat(pagerState.pageCount) { iteration ->
                        val color =
                            if (pagerState.currentPage == iteration) Color.Black else Color.LightGray

                        Box(
                            modifier = Modifier
                                .padding(2.dp)
                                .background(color)
                                .width(7.dp)
                                .height(5.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ProductAction(
    item: ProductItem?,
    productMap: Map<String, List<ProductItem>>?,
    moodboard: MoodboardEntity,
    onClose: () -> Unit = {},
    onRemix: (ProductItem) -> Unit = {},
    onTryOn: (ProductItem) -> Unit = {},
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
) {
    val replace = { target: ProductItem ->
        item?.let { collageSearchViewModel.replaceBlock(moodboard, item, target) }
        onClose()
    }

    ProductAlternativesDrawer(
        open = item != null,
        onClose = onClose,
        selected = item,
        products = productMap?.get(item?.id),
        onReplace = { replace(it) },
        onRemix = {
            onClose()
            collageSearchViewModel.updateRemixProduct(it)
            onRemix(it)
        },
        onTryOn = {
            onClose()
            onTryOn(it)
        },
    )
}

@Composable
private fun ExtensionPanel(
    query: String? = null,
    imageUrl: String? = null,
    moodboard: MoodboardEntity? = null,
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
    onInspoClick: (Int, SearchInspoItem) -> Unit,
    onPreferenceClick: () -> Unit = {},
    onTryOnClick: (List<MoodboardTryOnGarmentItem>) -> Unit = {},
    result: SearchItem? = null,
) {
    val metric = rememberMetricHelper(EventRefer.CollageGen)
    val scrollState = rememberScrollState()
    var isUserLoggedIn by remember { mutableStateOf(false) }

    val inspoList by collageSearchViewModel.searchInspoResult.collectAsState()
    val inspoStatus by collageSearchViewModel.searchInspoStatus.collectAsState()

    var tryOnOpen by remember { mutableStateOf(false) }

    LaunchedEffect(scrollState) {
        snapshotFlow { scrollState.value }.collect {
            metric(MetricEvent.SelectItem(EventItemListName.InspoList, EventMethod.Swipe))
        }
    }

    LaunchedEffect(Unit) {
        isUserLoggedIn = UserDataStoreManager.isUserLoggedIn()
    }

    LaunchedEffect(query, imageUrl) {
        if (inspoList == null && inspoStatus != TaskStatus.Loading) {
            collageSearchViewModel.getInspoList(query ?: "outfit", imageUrl)
        }
    }

    LaunchedEffect(inspoList) {
        inspoList?.let {
            metric(
                MetricEvent.ViewItemList(
                    EventItemListName.InspoList, it.mapIndexed { index, it ->
                        EventItem(
                            itemName = it.showQuery,
                            itemCategory = EventItemCategory.RecoInspo,
                            index = index
                        )
                    })
            )
        }
    }

    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.horizontalScroll(scrollState)
    ) {
        moodboard?.allTryOnItems?.let {
            it.box1?.let {
                TryOnButton(onClick = {
                    metric(
                        MetricEvent.SelectItem(
                            EventItemListName.TryOnBtn,
                            EventMethod.Click,
                            items = listOf(
                                EventItem(
                                    itemId = moodboard.id,
                                    itemName = result?.reasoning,
                                    itemCategory = EventItemCategory.GeneralCollage
                                )
                            )
                        )
                    )
                    tryOnOpen = true
                })

                TryOnPanelDrawer(open = tryOnOpen, onClose = { tryOnOpen = false }) {
                    MoodboardTryOnPanel(
                        moodboard.allTryOnItems,
                        onClose = { tryOnOpen = false },
                        onTryOn = {
                            tryOnOpen = false
                            onTryOnClick(it)
                        })
                }
            }
        }

        inspoList?.mapIndexed { index, inspo ->
            InspoCard(
                emoji = inspo.emoji ?: "",
                text = inspo.showQuery,
                onClick = { onInspoClick(index, inspo) })
        } ?: run {
            when (inspoStatus) {
                // TODO
                TaskStatus.Loading -> {}
                else -> {}
            }
        }
    }
}

@Composable
private fun InspoCard(
    emoji: String,
    text: String,
    onClick: () -> Unit = {},
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable { onClick() }) {
        Box(modifier = Modifier.padding(horizontal = 4.dp)) {
            Card(
                shape = CircleShape,
                colors = CardDefaults.cardColors(Color.White),
                modifier = Modifier.size(48.dp)
            ) {
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier.fillMaxSize()
                ) {
                    Text(emoji, fontSize = 30.sp)
                }
            }

            Image(
                painter = painterResource(id = R.drawable.icon_add_circle),
                contentDescription = "image description",
                contentScale = ContentScale.None,
                modifier = Modifier
                    .size(16.dp)
                    .align(Alignment.TopEnd)
                    .offset(x = 3.dp, y = (-4).dp)
            )
        }

        Text(
            text,
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            textAlign = TextAlign.Center,
            fontSize = 10.sp,
        )
    }
}

@Composable
fun TryOnButton(onClick: () -> Unit = {}) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable { onClick() }) {
        Box(modifier = Modifier.padding(horizontal = 4.dp)) {
            Card(
                shape = CircleShape,
                colors = CardDefaults.cardColors(Color.Black),
            ) {
                Image(
                    painter = painterResource(R.drawable.icon_try_on_white),
                    contentDescription = null,
                    modifier = Modifier
                        .size(48.dp)
                        .padding(12.dp)
                )
            }
        }

        Text(
            stringResource(R.string.text_try_on).uppercase(),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            textAlign = TextAlign.Center,
            fontSize = 10.sp,
        )
    }
}
