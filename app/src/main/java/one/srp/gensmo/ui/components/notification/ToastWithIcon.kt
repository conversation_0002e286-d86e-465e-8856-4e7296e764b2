package one.srp.gensmo.ui.components.notification

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.remember

@Composable
fun ToastWithIcon(
    text: String,
    subText: String,
    iconType: String,
    onClick: (() -> Unit)?,
    visible: Boolean,
    onDismiss: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 32.dp),
        contentAlignment = Alignment.TopCenter
    ) {
        AnimatedVisibility(
            visible = visible,
            enter = fadeIn(animationSpec = tween(300)),
            exit = fadeOut(animationSpec = tween(300))
        ) {
            Surface(
                modifier = Modifier
                    .shadow(elevation = 1.dp, spotColor = Color(0x1A000000), ambientColor = Color(0x1A000000))
                    .shadow(elevation = 24.dp, spotColor = Color(0x29000000), ambientColor = Color(0x29000000))
                    .width(335.dp)
                    .background(color = Color.White, shape = RoundedCornerShape(size = 8.dp))
                    .padding(horizontal = 16.dp, vertical = 12.dp)
                    .clickable(
                        enabled = onClick != null,
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) { 
                        onClick?.invoke() 
                        onDismiss.invoke()
                    },
                color = Color.Transparent
            ) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.Start
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        if (iconType == "avatar") {
                            Image(
                                painter = painterResource(id = R.drawable.icon_default_avatar),
                                contentDescription = "Avatar",
                                modifier = Modifier.size(40.dp)
                            )
                        }
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = text,
                                color = Color(0xFF333333),
                                style = AppThemeTextStyle.Body16H
                            )
                            if (subText.isNotEmpty()) {
                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = subText,
                                    color = Color(0xFF868D94),
                                    style = AppThemeTextStyle.Body12LightH
                                )
                            }
                        }
                        Image(
                            painter = painterResource(id = R.drawable.icon_bigger),
                            contentDescription = "Arrow",
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
        }
    }
}
