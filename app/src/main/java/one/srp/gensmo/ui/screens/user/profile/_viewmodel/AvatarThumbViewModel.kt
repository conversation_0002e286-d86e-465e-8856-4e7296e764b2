package one.srp.gensmo.ui.screens.user.profile._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import kotlinx.coroutines.flow.StateFlow
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import one.srp.gensmo.ui.navigation.NavActions
import timber.log.Timber
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow

@HiltViewModel
class AvatarThumbViewModel @Inject constructor(
    private val asyncTaskManager: AsyncTaskManager
) : ViewModel() {
    private var _navActions: NavActions? = null
    
    private val _imageUrlState = MutableStateFlow<String?>(null)
    val imageUrlState: StateFlow<String?> = _imageUrlState
    
    var imageUrl: String?
        get() = _imageUrlState.value
        set(value) {
            _imageUrlState.value = value
        }

    val taskStatus: StateFlow<String?> = asyncTaskManager.replicaTaskStatus
    val progress: StateFlow<AsyncTaskManager.Progress> = asyncTaskManager.progress
    val isTaskLoading: StateFlow<Boolean> = asyncTaskManager.isTaskLoading
    val previewUrl: StateFlow<String?> = asyncTaskManager.previewUrl
    // 添加一个变量跟踪上一个状态值
    private var lastStatus: String? = null


    init {
        observeTaskStatus()
    }
    
    fun setNavActions(navActions: NavActions) {
        _navActions = navActions
        asyncTaskManager.setOnTaskCompleted {
            _navActions?.navigateToCloset()
        }
    }
    
    /**
     * 从API刷新模型信息
     */
    private fun refreshModelFromApi() {
        viewModelScope.launch {
            if (!UserDataStoreManager.isUserLoggedIn()) {
                Timber.d("无法刷新模型：用户未登录")
                return@launch
            }
            
            try {
                val response = TryOnService.api.getReplica()
                if (response.isSuccessful) {
                    val body = response.body()
                    if (body == null) {
                        imageUrl = null
                        return@launch
                    }
                    
                    val url = body.modelUrl
                    val id = body.modelId
                    imageUrl = url
                    UserDataStoreManager.saveModelInfo(url, id)
                    Timber.d("模型信息已刷新：$url")
                }
            } catch (e: Exception) {
                Timber.e(e, "刷新模型信息出错")
            }
        }
    }
    
    // 修改loadModelInfo函数，使用新的刷新函数
    fun loadModelInfo() {
        viewModelScope.launch {
            if (UserDataStoreManager.isUserLoggedIn()) {
                // 首先从 UserDataStore 获取
                val (modelUrl, modelId) = UserDataStoreManager.getModelInfo()
                // 如果本地没有存储的 URL，则从 API 获取
                if (modelUrl.isNullOrEmpty() || modelId.isNullOrEmpty()) {
                    refreshModelFromApi()
                } else {
                    imageUrl = modelUrl
                }
            } else {
                Timber.d("user not logged in")
                imageUrl = null
            }
        }
    }

    // 修改监听方法
    fun observeTaskStatus() {
        viewModelScope.launch {
            taskStatus.collect { status ->
                // 如果状态与上次相同，则跳过处理
                if (status == lastStatus) {
                    return@collect
                }
                
                // 记录当前状态
                lastStatus = status
                
                Timber.d("任务状态: $status")
                when (status) {
                    "completed" -> {
                        Timber.d("任务完成")
                        UserDataStoreManager.clearModelInfo()
                        imageUrl = previewUrl.value
                        refreshModelFromApi()
                    }
                    "failure", "failed" -> {
                        Timber.d("任务失败")
                        // 处理失败状态
                    }
                    null -> {
                        // 不做任何事情
                    }
                    else -> {
                        // 只在状态变化时打印一次进度
                        val currentProgress = progress.value
                        Timber.d("当前状态: $status, 进度: ${currentProgress.current}/${currentProgress.total}")
                    }
                }
            }
        }
    }

}
