package one.srp.gensmo.ui.components.search

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.R

@Composable
fun SearchSuggestions(
    suggestions: List<String>,
    onSuggestionClick: (String) -> Unit,
    onRefreshClick: () -> Unit
) {
    if (suggestions.isNotEmpty()) {
        suggestions.forEachIndexed { index, suggestion ->
            if (index == suggestions.size - 1) {
                // 最后一个建议项使用Row包含内容和shuffle按钮
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 内容盒子
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .background(
                                color = Color(0xFFF5F5F5),
                                shape = RoundedCornerShape(size = 4.dp)
                            )
                            .border(
                                width = 0.4.dp,
                                color = Color(0x0A000000),
                                shape = RoundedCornerShape(size = 4.dp)
                            )
                            .clickable {
                                onSuggestionClick(suggestion)
                            }
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 8.dp, vertical = 6.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = suggestion,
                                style = AppThemeTextStyle.Body13H,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.weight(1f, fill = false)
                            )

                            Icon(
                                painter = painterResource(id = R.drawable.icon_arrow_top_right),
                                contentDescription = null,
                                modifier = Modifier
                                    .padding(start = 4.dp)
                                    .size(16.dp),
                                tint = Color(0xFF6F6F6F)
                            )
                        }
                    }

                    // Shuffle按钮与Box并列
                    Box(
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .size(30.dp)
                            .clickable { onRefreshClick() }
                            .background(color = Color(0xFFF5F5F5), shape = RoundedCornerShape(size = 4.dp))
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.icon_shuffle),
                            contentDescription = "刷新",
                            modifier = Modifier
                                .size(16.dp)
                                .align(Alignment.Center),
                            tint = Color.Unspecified,
                        )
                    }
                }
            } else {
                // 普通建议项保持原样
                Box(
                    modifier = Modifier
                        .wrapContentWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp)
                        .background(
                            color = Color(0xFFF5F5F5),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                        .border(
                            width = 0.4.dp,
                            color = Color(0x0A000000),
                            shape = RoundedCornerShape(size = 4.dp)
                        )
                        .clickable {
                            onSuggestionClick(suggestion)
                        }
                ) {
                    Row(
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(horizontal = 8.dp, vertical = 6.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = suggestion,
                            style = AppThemeTextStyle.Body13H,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f, fill = false)
                        )

                        Icon(
                            painter = painterResource(id = R.drawable.icon_arrow_top_right),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(start = 4.dp)
                                .size(16.dp),
                            tint = Color(0xFF6F6F6F)
                        )
                    }
                }
            }
        }
    }
}
