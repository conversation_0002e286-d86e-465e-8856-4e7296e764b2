package one.srp.gensmo.ui.components.navigate

import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.BottomAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.zIndex


@Composable
fun BottomBar(
    modifier: Modifier = Modifier,
    immersive: Boolean = false,
    content: @Composable (() -> Unit) = {},
) {
    BottomAppBar(
        modifier = Modifier
            .zIndex(10f)
            .then(modifier),
        containerColor = if (immersive) Color.Transparent else BottomAppBarDefaults.containerColor
    ) {
        content()
    }
}
