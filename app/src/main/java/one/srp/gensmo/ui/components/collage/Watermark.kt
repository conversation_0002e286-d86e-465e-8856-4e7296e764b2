package one.srp.gensmo.ui.components.collage

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import one.srp.gensmo.R
import one.srp.gensmo.utils.render.renderComposeToBitmap
import timber.log.Timber

enum class ScreenshotStatus {
    Loading, Success, Error
}

@Composable
fun ScreenshotContainer(
    modifier: Modifier = Modifier,
    key: Int = 0,
    width: Int = 600,
    height: Int = 800,
    onPreviewGen: (ImageBitmap) -> Unit = {},
    content: @Composable () -> Unit,
) {
    val context = LocalContext.current

    var screenshotStatus by remember { mutableStateOf(ScreenshotStatus.Loading) }
    var screenshotBitmap by remember { mutableStateOf<ImageBitmap?>(null) }

    LaunchedEffect(key) {
        try {
            val bitmap = renderComposeToBitmap(context, width, height) {
                WatermarkContainer {
                    content()
                }
            }
            val imageBitmap = bitmap.asImageBitmap()

            delay(350)
            screenshotBitmap = imageBitmap
            onPreviewGen(imageBitmap)
            screenshotStatus = ScreenshotStatus.Success
        } catch (err: Exception) {
            screenshotStatus = ScreenshotStatus.Error
            Timber.w(err)
        }
    }

    AnimatedVisibility(visible = screenshotBitmap != null) {
        screenshotBitmap?.let {
            Box(modifier = modifier) {
                Image(
                    bitmap = it,
                    contentDescription = null,
                    modifier = Modifier.matchParentSize()
                )
            }
        }
    }
}

@Composable
fun WatermarkContainer(
    modifier: Modifier = Modifier,
    alpha: Float = 0.5f,
    widthRatio: Float = 0.2f,
    content: @Composable () -> Unit,
) {
    BoxWithConstraints(modifier) {
        val maxWidth = this.maxWidth

        content()

        Image(
            painterResource(R.drawable.image_logo_text_waterprint),
            null,
            alpha = alpha,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .offset((-2).dp, (-2).dp)
                .width(maxWidth * widthRatio)
        )
    }
}
