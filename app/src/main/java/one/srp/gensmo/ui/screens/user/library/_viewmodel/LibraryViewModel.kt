package one.srp.gensmo.ui.screens.user.library._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.model.HistoryQueriesItem
import one.srp.gensmo.data.model.SessionHistoryItem
import one.srp.gensmo.data.model.TryOnHistoryItem
import one.srp.gensmo.data.repository.HistoryQueriesRepository
import one.srp.gensmo.data.repository.TryOnRepository
import one.srp.gensmo.data.repository.session.SessionHistoryRepository
import javax.inject.Inject

@HiltViewModel
class LibraryViewModel @Inject constructor(
    private val tryOnRepository: TryOnRepository,
    private val historyQueriesRepository: HistoryQueriesRepository,
    private val sessionHistoryRepository: SessionHistoryRepository,
) : ViewModel() {

    private val _selectedTab = MutableStateFlow(0)
    val selectedTab: StateFlow<Int> = _selectedTab.asStateFlow()

    val tryOnHistory: StateFlow<List<TryOnHistoryItem>> = tryOnRepository.tryOnHistory
    val historyQueries: StateFlow<List<HistoryQueriesItem>> =
        historyQueriesRepository.historyQueries
    val hasMoreQueries: StateFlow<Boolean> = historyQueriesRepository.hasMore
    val sessionHistory: StateFlow<List<SessionHistoryItem>> = sessionHistoryRepository.sessions
    val hasMoreSessions: StateFlow<Boolean> = sessionHistoryRepository.hasMore

    init {
        loadTryOnHistory(true)
//        loadHistoryQueries(true)
        loadSessionHistory(true)
    }

    fun loadTryOnHistory(isRefresh: Boolean = false) {
        viewModelScope.launch {
            tryOnRepository.loadTryOnHistory(isRefresh)
        }
    }

    fun loadHistoryQueries(isRefresh: Boolean = false) {
        viewModelScope.launch {
            historyQueriesRepository.loadHistoryQueries(isRefresh)
        }
    }

    fun loadSessionHistory(isRefresh: Boolean = false) {
        viewModelScope.launch {
            sessionHistoryRepository.loadSessions(isRefresh)
        }
    }

    fun setSelectedTab(index: Int) {
        _selectedTab.value = index
    }

    fun deleteTryOnHistory(tryOnTaskId: String) {
        viewModelScope.launch {
            tryOnRepository.deleteTryOnHistory(tryOnTaskId)
        }
    }

    fun deleteHistoryQuery(taskId: String) {
        viewModelScope.launch {
            historyQueriesRepository.deleteHistoryQuery(taskId)
        }
    }
}
