package one.srp.gensmo.ui.theme

import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

object AppThemeColors {
    val Black = Color(0xFF222222)
    val Gray800 = Color(0xFF313131)
    val Gray700 = Color(0xFF495057)
    val Gray650 = Color(0xFF6C6F73)
    val Gray600 = Color(0xFF868D94)
    val Gray500 = Color(0xFFB0B5B9)
    val Gray200 = Color(0xFFC9CCCF)
    val Gray100 = Color(0xFFDFE1E3)
    val Gray50 = Color(0xFFF5F5F5)
    val White = Color(0xFFFFFFFF)

    val Red100 = Color(0xFFF8E9ED)
    val Red500 = Color(0xFFFA254C)
    val Red600 = Color(0xFFBB2649)

    val Blue500 = Color(0xFF0224FF)
}

val appThemeLightColorScheme = AppThemeColors.run {
    lightColorScheme(
        primary = Black,
        onPrimary = White,
        primaryContainer = Gray50,
        onPrimaryContainer = Gray800,
        inversePrimary = Gray200,

        secondary = Gray50,
        onSecondary = Gray700,
        secondaryContainer = White,
        onSecondaryContainer = Gray650,

        tertiary = Gray700,
        onTertiary = Gray200,
        tertiaryContainer = White,
        onTertiaryContainer = Gray700,

        background = White,
        onBackground = Black,
        surface = Gray50,
        onSurface = Gray700,
        surfaceVariant = Gray100,
        onSurfaceVariant = Gray700,
        surfaceTint = Black,
        inverseSurface = Gray800,
        inverseOnSurface = Gray50,

        error = Red500,
        onError = White,
        errorContainer = Red100,
        onErrorContainer = Red600,

        outline = Gray600,
        outlineVariant = Gray200,
        scrim = Black,

        surfaceBright = White,
        surfaceContainer = Gray50,
        surfaceContainerHigh = Gray100,
        surfaceContainerHighest = Gray200,
        surfaceContainerLow = Gray50,
        surfaceContainerLowest = White,
        surfaceDim = Gray100
    )
}
