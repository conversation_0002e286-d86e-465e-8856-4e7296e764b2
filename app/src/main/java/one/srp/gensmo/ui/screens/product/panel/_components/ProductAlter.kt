package one.srp.gensmo.ui.screens.product.panel._components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PageSize
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChange
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import kotlinx.coroutines.launch
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import kotlin.math.abs

@Composable
fun ProductAlter(
    selected: ProductItem? = null,
    items: List<ProductItem>,
    onChange: (ProductItem) -> Unit = {},
    refer: EventRefer? = null,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(10.dp),
        modifier = Modifier.pointerInput(Unit) {
            awaitPointerEventScope {
                while (true) {
                    val event = awaitPointerEvent()
                    val dragEvent = event.changes.firstOrNull()

                    if (dragEvent != null
                        && abs(dragEvent.positionChange().y) > abs(dragEvent.positionChange().x / 4)
                        && abs(dragEvent.positionChange().y) > 2
                    ) {
                        event.changes.forEach { it.consume() }
                    }
                }
            }
        }
    ) {
        Row(modifier = Modifier.padding(horizontal = 16.dp)) {
            Text(stringResource(R.string.text_alternatives), style = AppThemeTextStyle.Heading16H)
        }

        ProductAlterCarousel(
            selected = selected,
            items = items,
            onIndexChange = { onChange(items[it]) },
            refer = refer
        )
    }
}

@Composable
private fun ProductAlterCarousel(
    selected: ProductItem? = null,
    items: List<ProductItem>,
    onIndexChange: (Int) -> Unit = {},
    refer: EventRefer? = null,
) {
    val coroutineScope = rememberCoroutineScope()

    val pagerState = rememberPagerState(pageCount = {
        items.size
    })

    val metric = refer?.let { rememberMetricHelper(refer) } ?: run { {} }
    LaunchedEffect(Unit) {
        metric(
            MetricEvent.ViewItemList(
                EventItemListName.ProductList, items = items.mapIndexed { index, product ->
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = product.globalId,
                        itemName = product.title,
                        index = index,
                    )
                })
        )
    }

    LaunchedEffect(items) {
        pagerState.scrollToPage(0)
    }

    val onClick = { index: Int ->
        metric(
            MetricEvent.SelectItem(
                EventItemListName.ProductListProduct, EventMethod.Click, items = listOf(
                    EventItem(
                        itemCategory = EventItemCategory.Product,
                        itemId = items[index].globalId,
                        itemName = items[index].title,
                        index = index,
                    )
                )
            )
        )

        coroutineScope.launch {
            onIndexChange(index)
        }
    }

    Box {
        val pageSize = 60
        val pageSpacing = 8

        HorizontalPager(
            state = pagerState,
            pageSize = PageSize.Fixed(pageSize.dp),
            pageSpacing = pageSpacing.dp,
            contentPadding = PaddingValues(horizontal = 8.dp)
        ) { index ->
            val product = items[index]
            Card(
                modifier = Modifier
                    .size(pageSize.dp)
                    .clickable { onClick(index) },
                border = if (product.globalId == selected?.globalId) BorderStroke(
                    1.dp, AppThemeColors.Black
                ) else null,
                shape = MaterialTheme.shapes.medium,
                colors = CardDefaults.cardColors(MaterialTheme.colorScheme.surface)
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current).data(product.mainImage?.link)
                        .crossfade(true).build(),
                    contentDescription = null,
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }

}
