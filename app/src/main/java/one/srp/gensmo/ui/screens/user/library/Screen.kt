package one.srp.gensmo.ui.screens.user.library

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.R
import one.srp.gensmo.data.model.SearchQueryMessage
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.session.chat._viewmodel.ChatMeta
import one.srp.gensmo.ui.screens.session.chat._viewmodel.SessionChatViewModel
import one.srp.gensmo.ui.screens.tryon._viewmodel.TryOnGenerateViewModel
import one.srp.gensmo.ui.screens.user.library._components.HistoryQueryItem
import one.srp.gensmo.ui.screens.user.library._components.SessionQueryItem
import one.srp.gensmo.ui.screens.user.library._components.TryonHistory
import one.srp.gensmo.ui.screens.user.library._viewmodel.LibraryViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.viewmodel.search.CollageSearchViewModel

@Composable
fun UserLibraryScreen(
    navActions: NavActions = NavActions(),
    tryOnGenerateViewModel: TryOnGenerateViewModel = hiltViewModel(),
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
    viewModel: LibraryViewModel = hiltViewModel(),
    sessionChatViewModel: SessionChatViewModel = hiltViewModel(),
    initialSelectedTab: Int = 0,
    createSession: (SearchQueryMessage) -> Unit = {},
) {
    LaunchedEffect(initialSelectedTab) {
        viewModel.setSelectedTab(initialSelectedTab)
    }

    Column {
        TopBar(onBack = { navActions.back() }, transparent = true) {
            Row(
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = 40.dp)
            ) {
                Text(stringResource(R.string.text_your_activity))
            }
        }

        val tabs = listOf("History", "Try-ons")
        val selectedTab by viewModel.selectedTab.collectAsState()

        Column {
            TabRow(
                selectedTabIndex = selectedTab,
                modifier = Modifier.fillMaxWidth(),
                containerColor = Color.Transparent,
                divider = {},
                indicator = { tabPositions ->
                    if (selectedTab < tabPositions.size) {
                        BoxWithConstraints(contentAlignment = Alignment.BottomCenter) {
                            val paddingH = (this.maxWidth - 110.dp) / tabPositions.size / 2
                            TabRowDefaults.SecondaryIndicator(
                                Modifier
                                    .tabIndicatorOffset(tabPositions[selectedTab])
                                    .padding(horizontal = paddingH),
                            )
                        }
                    }
                },
            ) {
                tabs.forEachIndexed { index, item ->
                    Tab(
                        selected = selectedTab == index,
                        onClick = { viewModel.setSelectedTab(index) },
                        text = {
                            if (selectedTab == index) {
                                Text(
                                    text = item, style = AppThemeTextStyle.Body16H
                                )
                            } else {
                                Text(
                                    text = item,
                                    style = AppThemeTextStyle.Body16LightH.copy(AppThemeColors.Gray700)
                                )
                            }
                        })
                }
            }

            when (selectedTab) {
                0 -> SessionHistoryView(
                    navActions = navActions,
                    sessionChatViewModel = sessionChatViewModel,
                    viewModel = viewModel,
                )

                1 -> TryOnsView(navActions, tryOnGenerateViewModel, viewModel)

            }
        }
    }
}

@Composable
private fun MyStylesView(
    navActions: NavActions = NavActions(),
    collageSearchViewModel: CollageSearchViewModel = hiltViewModel(),
    viewModel: LibraryViewModel = hiltViewModel(),
    onEditQuery: (String, String) -> Unit = { _, _ -> },
) {
    val queries by viewModel.historyQueries.collectAsState()
    val hasMore by viewModel.hasMoreQueries.collectAsState()
    val listState = rememberLazyListState()

    if (queries.isEmpty()) {
        Column(
            modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.Center
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(R.string.text_empty_my_styles),
                    style = AppThemeTextStyle.Body16LightH.copy(AppThemeColors.Gray700)
                )
            }
        }
    } else {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(queries) { query ->
                HistoryQueryItem(query = query, onDelete = { taskId ->
                    viewModel.deleteHistoryQuery(taskId)
                }, onOpenHistory = { taskId ->
                    collageSearchViewModel.clear()
                    navActions.navigateToCollageTask(taskId)
                }, onGenerateCollage = { query, imageUrl ->
                    navActions.navigateToCollageSearch(query, imageUrl)
                }, onEdit = { query, imageUrl ->
                    onEditQuery(query, imageUrl)
                })
            }
        }
    }

    LaunchedEffect(listState) {
        snapshotFlow {
            listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index
        }.collect { lastIndex ->
            if (lastIndex != null && lastIndex >= queries.size - 1 && hasMore) {
                viewModel.loadHistoryQueries()
            }
        }
    }
}

@Composable
private fun TryOnsView(
    navActions: NavActions = NavActions(),
    tryOnGenerateViewModel: TryOnGenerateViewModel = hiltViewModel(),
    viewModel: LibraryViewModel = hiltViewModel(),
) {
    val tryOnHistory by viewModel.tryOnHistory.collectAsState()

    TryonHistory(tryOnHistory = tryOnHistory, onTryOn = { taskId ->
        tryOnGenerateViewModel.clear()
        navActions.navigateToTryOnTask(taskId)
    }, onLoadMore = {
        viewModel.loadTryOnHistory()
    }, onDelete = { taskId ->
        viewModel.deleteTryOnHistory(taskId)
    })
}

@Composable
private fun SessionHistoryView(
    navActions: NavActions = NavActions(),
    sessionChatViewModel: SessionChatViewModel = hiltViewModel(),
    viewModel: LibraryViewModel = hiltViewModel(),
) {
    val sessions by viewModel.sessionHistory.collectAsState()
    val hasMore by viewModel.hasMoreSessions.collectAsState()
    val listState = rememberLazyListState()

    if (sessions.isEmpty()) {
        Column(
            modifier = Modifier.fillMaxWidth(), verticalArrangement = Arrangement.Center
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(R.string.text_empty_my_styles),
                    style = AppThemeTextStyle.Body16LightH.copy(AppThemeColors.Gray700)
                )
            }
        }
    } else {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(sessions) { query ->
                SessionQueryItem(
                    query = query,
                    onOpenHistory = { item ->
                        sessionChatViewModel.setInitialSessionId(
                            ChatMeta(
                                sessionId = item.sessionId,
                                title = item.sessionTitle
                            )
                        )
                        navActions.navigateToSessionChat()
                    },
                )
            }
        }
    }

    LaunchedEffect(listState) {
        snapshotFlow {
            listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index
        }.collect { lastIndex ->
            if (lastIndex != null && lastIndex >= sessions.size - 1 && hasMore) {
                viewModel.loadSessionHistory()
            }
        }
    }
}

