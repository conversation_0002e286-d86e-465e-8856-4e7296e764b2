package one.srp.gensmo.ui.screens.user.profile._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import one.srp.gensmo.data.remote.SavedPagingSource
import javax.inject.Inject

@HiltViewModel
class SavedListViewModel @Inject constructor() : ViewModel() {
    val pageFlow = Pager(
        config = PagingConfig(
            pageSize = 10, enablePlaceholders = false,
        ), pagingSourceFactory = { SavedPagingSource() }).flow.cachedIn(
        viewModelScope
    )
}
