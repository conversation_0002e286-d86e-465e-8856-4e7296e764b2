package one.srp.gensmo.ui.screens.session.view._components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun <T> CarouselContainer(
    modifier: Modifier = Modifier,
    containerModifier: Modifier = Modifier,
    items: List<T>,
    initialPage: Int = 0,
    indicator: Boolean = false,
    onIndexChange: (Int) -> Unit = {},
    renderer: @Composable (T) -> Unit,
) {
    val pagerState = rememberPagerState(
        pageCount = { items.size })

    LaunchedEffect(items) {
        pagerState.scrollToPage(initialPage)
    }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            onIndexChange(page)
        }
    }

    Column(modifier = modifier, verticalArrangement = Arrangement.spacedBy(8.dp)) {
        HorizontalPager(
            state = pagerState, modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
                .then(containerModifier)
        ) { index ->
            items[index]?.let {
                renderer(it)
            }
        }

        if (indicator) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                CarouselIndicator(pagerState = pagerState)
            }
        }
    }
}

@Composable
fun CarouselIndicator(modifier: Modifier = Modifier, pagerState: PagerState) {
    if (pagerState.pageCount <= 1) return

    Row(
        Modifier
            .wrapContentHeight()
            .then(modifier),
        horizontalArrangement = Arrangement.Center,
    ) {
        Card(colors = CardDefaults.cardColors(Color.White.copy(0.7f))) {
            Row(
                modifier = Modifier.padding(horizontal = 4.dp, vertical = 2.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                repeat(pagerState.pageCount) { iteration ->
                    val color =
                        if (pagerState.currentPage == iteration) Color.Black else Color.LightGray

                    Box(
                        modifier = Modifier
                            .padding(2.dp)
                            .background(color)
                            .width(7.dp)
                            .height(5.dp)
                    )
                }
            }
        }
    }
}
