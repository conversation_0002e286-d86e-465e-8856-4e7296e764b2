package one.srp.gensmo.ui.screens.user.settings

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.gensmo.BuildConfig
import one.srp.gensmo.R
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.mime.openUrl

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserSettingsScreen(
    navActions: NavActions = NavActions(),
    viewModel: UserProfileViewModel = hiltViewModel(),
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scrollState = rememberScrollState()

    val coroutineScope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    var showLogoutConfirmation by remember { mutableStateOf(false) }
    var versionClickCount by remember { mutableIntStateOf(0) }

    fun logout() {
        viewModel.logout()

        coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
            if (!sheetState.isVisible) {
                showLogoutConfirmation = false
                navActions.navigateToFeedRecommend()
            }
        }
    }

    fun gotoCheckIn() {
        navActions.navigateToUserCheckIn()
    }

    fun gotoAccount() {
        navActions.navigateToUserAccount()
    }

    fun handleVersionClick() {
        versionClickCount++
        if (versionClickCount >= 10) {
            versionClickCount = 0
            coroutineScope.launch {
                try {
                    val userId = UserDataStoreManager.getUserId()
                    if (userId != null) {
                        clipboardManager.setText(AnnotatedString(userId))
                        snackbarHostState.showSnackbar("User id has been copied to clipboard: $userId")
                    } else {
                        snackbarHostState.showSnackbar("Failed to get user id")
                    }
                } catch (e: Exception) {
                    snackbarHostState.showSnackbar("复制失败: ${e.message}")
                }
            }
        }
    }

    Scaffold(
        topBar = {
            TopBar(onBack = { navActions.back() }, transparent = true) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(end = 40.dp)
                ) {
                    Text(stringResource(R.string.text_settings))
                }
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .verticalScroll(scrollState)
        ) {
            SettingsItem(title = stringResource(R.string.text_account)) {
                Column {
                    FullWidthButton(
                        text = stringResource(R.string.text_check__in),
                        iconResId = R.drawable.icon_user_check_in,
                        onClick = { gotoCheckIn() })

                    FullWidthButton(
                        text = stringResource(R.string.text_account),
                        iconResId = R.drawable.icon_account,
                        onClick = { gotoAccount() })
                }
            }

            SettingsItem(title = stringResource(R.string.text_social)) {
                Column {
                    FullWidthButton(
                        text = stringResource(R.string.text_contact_us_on_discord),
                        iconResId = R.drawable.icon_discord_gray,
                        onClick = { openUrl(context, "https://discord.gg/9VjmFRKCqY") })
                    FullWidthButton(
                        text = stringResource(R.string.text_instagram),
                        iconResId = R.drawable.icon_ins_gray,
                        onClick = { openUrl(context, "https://www.instagram.com/gensmo_ig/") })
                    FullWidthButton(
                        text = stringResource(R.string.text_tiktok),
                        iconResId = R.drawable.icon_tiktok_gray,
                        onClick = { openUrl(context, "https://www.tiktok.com/@gensmo") })
                    FullWidthButton(
                        text = stringResource(R.string.text_x),
                        iconResId = R.drawable.icon_x_gray,
                        onClick = { openUrl(context, "https://x.com/Gensmo_official") })
                }
            }

            SettingsItem(title = stringResource(R.string.text_about)) {
                Column {
                    FullWidthButton(
                        text = stringResource(R.string.text_about_gensmo),
                        iconResId = R.drawable.icon_gensmo_logo_gray,
                        onClick = { openUrl(context, "https://gensmo.com/about/") })
                    FullWidthButton(
                        text = stringResource(R.string.text_terms_of_service),
                        iconResId = R.drawable.icon_terms_gray,
                        onClick = { openUrl(context, "https://gensmo.com/about/terms/") })
                    FullWidthButton(
                        text = stringResource(R.string.text_privacy_policy),
                        iconResId = R.drawable.icon_privacy_gray,
                        onClick = { openUrl(context, "https://gensmo.com/about/privacy/") })
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            TextButton(
                modifier = Modifier.fillMaxWidth(),
                onClick = { showLogoutConfirmation = true },
                shape = RectangleShape,
            ) {
                Row(modifier = Modifier.fillMaxWidth()) {
                    Text(
                        stringResource(R.string.text_log_out),
                        style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Red500)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                "${stringResource(R.string.text_version)} ${BuildConfig.VERSION_NAME}",
                style = AppThemeTextStyle.Body16LightH.copy(
                    AppThemeColors.Gray700
                ),
                modifier = Modifier
                    .padding(12.dp)
                    .clickable { handleVersionClick() }
            )
        }

        if (showLogoutConfirmation) {
            ModalBottomSheet(
                onDismissRequest = { showLogoutConfirmation = false },
                sheetState = sheetState,
                dragHandle = null,
            ) {
                Column(modifier = Modifier.background(MaterialTheme.colorScheme.surface)) {
                    Column(
                        modifier = Modifier
                            .background(MaterialTheme.colorScheme.background)
                            .fillMaxWidth()
                            .padding(top = 8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                    ) {
                        Text(
                            stringResource(R.string.text_are_you_sure_you_want_to_log_out),
                            style = AppThemeTextStyle.Body11H.copy(
                                AppThemeColors.Gray700
                            )
                        )

                        TextButton(
                            modifier = Modifier.fillMaxWidth(), onClick = {
                                logout()
                            }) {
                            Text(
                                stringResource(R.string.text_log_out),
                                style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Red500)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Column(
                        modifier = Modifier
                            .background(MaterialTheme.colorScheme.background)
                            .fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        TextButton(
                            modifier = Modifier.fillMaxWidth(), onClick = {
                                coroutineScope.launch { sheetState.hide() }.invokeOnCompletion {
                                    if (!sheetState.isVisible) {
                                        showLogoutConfirmation = false
                                    }
                                }
                            }) {
                            Text(
                                stringResource(R.string.text_cancel),
                                style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray700)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SettingsItem(title: String, button: @Composable () -> Unit) {
    Column {
        Text(
            text = title,
            style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray500),
            modifier = Modifier.padding(16.dp)
        )
        button()
    }
}

@Composable
private fun FullWidthButton(
    text: String,
    @DrawableRes iconResId: Int,
    onClick: () -> Unit,
) {
    TextButton(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp),
        shape = RectangleShape,
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Image(
                    painter = painterResource(id = iconResId),
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )

                Text(text = text, style = AppThemeTextStyle.Body16H)
            }
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

