package one.srp.gensmo.ui.screens.detail.editor._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import one.srp.gensmo.data.repository.PublishRepository
import timber.log.Timber
import javax.inject.Inject

data class PostEditorUiState(
    val title: String = "",
    val description: String = "",
    val isLoading: Boolean = false,
    val isPublished: Boolean = false,
    val error: String? = null,
)

@HiltViewModel
class PostEditorViewModel @Inject constructor(
    private val publishRepository: PublishRepository,
) : ViewModel() {

    private val _uiState = MutableStateFlow(PostEditorUiState())
    val uiState: StateFlow<PostEditorUiState> = _uiState.asStateFlow()

    fun updateTitle(title: String) {
        _uiState.value = _uiState.value.copy(title = title)
    }

    fun updateDescription(description: String) {
        _uiState.value = _uiState.value.copy(description = description)
    }

    fun publishPost(
        feedType: String,
        documentId: String,
        onSuccess: () -> Unit = {},
        onFail: () -> Unit = {},
    ) {
        val currentState = _uiState.value

        _uiState.value = currentState.copy(
            isLoading = true,
            error = null
        )

        viewModelScope.launch {
            publishRepository.publishPost(
                feedType = feedType,
                documentId = documentId,
                title = currentState.title,
                description = currentState.description
            ).collect { result ->
                result.fold(
                    onSuccess = {
                        Timber.d("发布成功")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            isPublished = true,
                            error = null
                        )
                        onSuccess()
                    },
                    onFailure = { throwable ->
                        Timber.e(throwable, "发布失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = throwable.message ?: "Post failed."
                        )
                        onFail()
                    }
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun reset() {
        _uiState.value = PostEditorUiState()
    }
}
