package one.srp.gensmo.ui.screens.tryon.create._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeTextStyle

enum class BodyShapeOption(val displayName: String) {
    INVERTED_TRIANGLE("Inverted triangle"),
    RECTANGLE("Rectangle"),
    TRIANGLE("Triangle"),
    HOURGLASS("Hourglass"),
    OVAL("Oval")
}

@Composable
fun BodyShapeSelector(
    selectedBodyShape: BodyShapeOption,
    onBodyShapeSelected: (BodyShapeOption) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "Body shape",
            style = AppThemeTextStyle.Heading18D.copy(
                color = Color(0xFF000000),
                fontWeight = FontWeight(700)
            ),
            modifier = Modifier.padding(bottom = 8.dp).align(Alignment.Start)
        )
        Text(
            text = "Choose the shape that best reflects your proportions.",
            style = AppThemeTextStyle.Body12LightH.copy(
                fontWeight = FontWeight(400),
                color = Color(0xFF495057)
            ),
            modifier = Modifier.padding(bottom = 16.dp).align(Alignment.Start)
        )
        
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .selectableGroup(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            BodyShapeOption.entries.chunked(3).forEach { rowItems ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    rowItems.forEach { bodyShape ->
                        BodyShapeCard(
                            bodyShape = bodyShape,
                            selected = bodyShape == selectedBodyShape,
                            onSelect = { onBodyShapeSelected(bodyShape) },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    // 如果最后一行不满3个，添加空白占位
                    repeat(3 - rowItems.size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }
    }
}

@Composable
fun BodyShapeCard(
    bodyShape: BodyShapeOption,
    selected: Boolean,
    onSelect: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor = Color.White
    
    Card(
        modifier = modifier
            .selectable(
                selected = selected,
                onClick = onSelect,
                role = Role.RadioButton
            ),
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 8.dp, vertical = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(
                    id = when (bodyShape) {
                        BodyShapeOption.RECTANGLE -> R.drawable.icon_shape_rectangle
                        BodyShapeOption.INVERTED_TRIANGLE -> R.drawable.icon_shape_inverted_triangle
                        BodyShapeOption.TRIANGLE -> R.drawable.icon_shape_inverted_triangle
                        BodyShapeOption.HOURGLASS -> R.drawable.icon_shape_hourglass
                        BodyShapeOption.OVAL -> R.drawable.icon_shape_oval
                    }
                ),
                contentDescription = bodyShape.displayName,
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
                    .then(
                        if (selected) {
                            Modifier.border(
                                width = 2.dp,
                                color = Color(0xFF2C2C2C),
                                shape = RoundedCornerShape(16.dp)
                            )
                        } else {
                            Modifier
                        }
                    )
            )
            
            Text(
                text = bodyShape.displayName,
                style = AppThemeTextStyle.Body12H.copy(
                    fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal
                ),
                color = if (selected) Color(0xFF2C2C2C) else Color(0xFF9C9C9C)
            )
        }
    }
}
