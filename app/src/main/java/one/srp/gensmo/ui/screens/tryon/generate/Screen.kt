package one.srp.gensmo.ui.screens.tryon.generate
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.tryon._viewmodel.TryOnGenerateViewModel
import kotlinx.coroutines.delay
import androidx.compose.ui.draw.blur
import androidx.activity.compose.BackHandler

@Composable
fun TryOnGenerateScreen(
    navActions: NavActions = NavActions(),
    tryOnParams: TryOnParams? = null,
    viewModel: TryOnGenerateViewModel = hiltViewModel(),
) {
    val queryStatus = viewModel.queryStatus.collectAsState().value
    val queryResult = viewModel.queryResult.collectAsState().value
    var isScreenActive by remember { mutableStateOf(true) }

    BackHandler {
        isScreenActive = false
        navActions.back()
    }

    var userModelImage by remember { mutableStateOf<String?>(null) }
    LaunchedEffect(Unit) {
        val model = UserDataStoreManager.getModelInfo()
        userModelImage = if (model.first.isNullOrEmpty()) {
            "https://gem-user-image.favie.yesy.online/user_upload/20250301/3ff8ead9-86ea-4f6f-9a04-5b415d9f2943.png"
        } else {
            model.first
        }
    }

    tryOnParams?.let {
        LaunchedEffect(it) {
            viewModel.generateTask(it)
        }

        when (queryStatus) {
            TaskStatus.Fail -> {
                BaseError(onClick = { navActions.back() })
            }

            TaskStatus.Success -> {
                LaunchedEffect(Unit) {
                    if (isScreenActive) {
                        queryResult?.tryOnTaskId?.let {
                            navActions.navigateToTryOnTask(it, skip = true)
                        }
                    }
                }
            }

            else -> {
                LoadingPage(
                    navActions = navActions,
                    params = it,
                    modelImage = userModelImage,
                    onBack = { isScreenActive = false }
                )
            }
        }
    } ?: run {
        BaseError(onClick = { navActions.back() })
    }
}

@Composable
private fun LoadingPage(
    navActions: NavActions = NavActions(),
    params: TryOnParams,
    modelImage: String? = null,
    onBack: () -> Unit = {},
) {
    var progress by remember { mutableStateOf(0) }
    
    LaunchedEffect(Unit) {
        var currentProgress = 0
        while (currentProgress < 99) {
            delay(100) // 每100ms更新一次
            currentProgress += 1
            progress = currentProgress
        }
    }

    Scaffold(topBar = {
        TopBar(immersive = true, onBack = { 
            onBack()
            navActions.back() 
        })
    }) { paddingValues ->
        Box(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxWidth()
                .fillMaxHeight(0.8f),
        ) {
            // 图片部分
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(modelImage)
                    .crossfade(true)
                    .build(),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .blur(radius = 20.dp)
                    .graphicsLayer {
                        alpha = 0.2f
                        scaleX = 1.0f
                        scaleY = 1.0f
                    },
                contentScale = ContentScale.Fit
            )
            
            // 文字部分
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text("Getting your Avatar dressed...", color = Color(0xFF222222))
                Text(
                    text = "$progress%",
                    color = Color(0xFF222222),
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}
