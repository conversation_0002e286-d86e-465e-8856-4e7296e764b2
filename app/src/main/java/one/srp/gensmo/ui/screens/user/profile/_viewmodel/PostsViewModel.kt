package one.srp.gensmo.ui.screens.user.profile._viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import one.srp.gensmo.data.model.FeedItem
import one.srp.gensmo.data.remote.PublishHistoryPagingSource
import javax.inject.Inject

@HiltViewModel
class PostsViewModel @Inject constructor() : ViewModel() {

    // 缓存不同 targetUserId 的 Pager 实例，避免重复创建和请求
    private val pagingFlows = mutableMapOf<String?, Flow<PagingData<FeedItem>>>()

    fun getPublishHistoryFlow(targetUserId: String? = null): Flow<PagingData<FeedItem>> {
        return pagingFlows.getOrPut(targetUserId) {
            Pager(
                config = PagingConfig(
                    pageSize = 10,
                    enablePlaceholders = false,
                ),
                pagingSourceFactory = { PublishHistoryPagingSource(targetUserId) }
            ).flow.cachedIn(viewModelScope)
        }
    }
}
