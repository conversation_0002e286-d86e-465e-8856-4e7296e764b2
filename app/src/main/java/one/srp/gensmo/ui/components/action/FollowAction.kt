package one.srp.gensmo.ui.components.action

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import kotlinx.coroutines.launch
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.data.remote.CommunityService
import one.srp.gensmo.ui.screens.tryon.create._components.LoginModal
import one.srp.gensmo.viewmodel.tryon.CreateViewModel

@Composable
fun FollowAction(
    initialState: Boolean = false,
    userId: String,
    onFollow: (<PERSON><PERSON><PERSON>) -> Unit = {},
    refer: EventRefer,
    content: @Composable (Boolean, (() -> Unit)) -> Unit,
) {
    var isFollowing by remember(initialState) { mutableStateOf(initialState) }

    val metric = rememberMetricHelper(refer)

    LaunchedEffect(userId) {
        runCatching {
            val res = CommunityService.api.checkFollowStatus(userId)
            if (res.isSuccessful) {
                when (res.body()?.status) {
                    FollowStatus.Followed.value, FollowStatus.Mutual.value -> {
                        isFollowing = true
                    }

                    FollowStatus.FollowedBack.value, FollowStatus.NotFollowed.value -> {
                        isFollowing = false
                    }

                    else -> {}
                }
            }
        }
    }

    val coroutineScope = rememberCoroutineScope()
    fun clickFollow() {
        val target = !isFollowing
        isFollowing = target

        onFollow(target)

        coroutineScope.launch {
            try {
                if (target) {
                    CommunityService.api.followUser(userId)
                } else {
                    CommunityService.api.unfollowUser(userId)
                }
            } catch (e: Exception) {
                isFollowing = !target
            }
        }
    }

    val context = LocalContext.current
    val createViewModel: CreateViewModel = hiltViewModel(key = "FollowAction")
    val uiState by createViewModel.uiState.collectAsState()

    fun clickFollowWithCheck() {
        if (createViewModel.isUserLoggedIn()) {
            clickFollow()
        } else {
            createViewModel.updateLoginDialog(true)
            createViewModel.setLoginSource("default_avatar")
        }
    }

    content(isFollowing) { clickFollowWithCheck() }

    if (uiState.showLoginDialog) {
        LoginModal(
            onDismiss = { createViewModel.updateLoginDialog(false) },
            isLoading = uiState.isLoading,
            onGoogleLogin = {
                createViewModel.loginWithGoogle(
                    context = context,
                    onSuccess = {
                        clickFollow()
                    }
                )
                createViewModel.updateLoginDialog(false)
            },
            onAppleLogin = {
                createViewModel.loginWithApple(
                    context = context,
                    onSuccess = {
                        clickFollow()
                    }
                )
                createViewModel.updateLoginDialog(false)
            }
        )
    }
}

enum class FollowStatus(val value: String) {
    NotFollowed("notFollowed"),
    Followed("followed"),
    Mutual("mutual"),
    FollowedBack("followedBack"),
}
