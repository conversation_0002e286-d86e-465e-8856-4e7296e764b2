package one.srp.gensmo.ui.components.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun FeedFollowButton(
    modifier: Modifier = Modifier,
    isFollowing: Boolean = false,
    onClick: () -> Unit = {},
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.White,
            contentColor = if (isFollowing) AppThemeColors.Gray700 else AppThemeColors.Red600,
            disabledContainerColor = AppThemeColors.Gray50,
            disabledContentColor = AppThemeColors.Gray700,
        ),
        border = BorderStroke(
            width = 1.dp,
            color = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.Red600
        ),
    ) {
        Text(
            stringResource(if (isFollowing) R.string.text_following else R.string.text_follow),
            style = AppThemeTextStyle.Body12H
        )
    }
}

@Composable
fun ProfileFollowButton(
    modifier: Modifier = Modifier,
    isFollowing: Boolean = false,
    onClick: () -> Unit = {},
) {
    Button(
        onClick = onClick,
        modifier = modifier,
        shape = MaterialTheme.shapes.medium,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = if (isFollowing) AppThemeColors.Gray50 else AppThemeColors.Red600,
            contentColor = if (isFollowing) AppThemeColors.Gray700 else AppThemeColors.White,
            disabledContainerColor = AppThemeColors.Gray50,
            disabledContentColor = AppThemeColors.Gray700,
        ),
    ) {
        Text(
            stringResource(if (isFollowing) R.string.text_following else R.string.text_follow),
            style = AppThemeTextStyle.Body12H
        )
    }
}