package one.srp.gensmo.ui.screens.user.profile._components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.MetricEvent
import one.srp.core.analytics.hooks.rememberMetricHelper
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.gensmo.R
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.user.profile._viewmodel.AvatarThumbViewModel
import one.srp.gensmo.ui.screens.user.profile._viewmodel.UserProfileViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeElevation
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun ProfileHeader(
    modifier: Modifier = Modifier,
    navActions: NavActions = NavActions(),
    viewModel: UserProfileViewModel = hiltViewModel(),
) {
    LaunchedEffect(Unit) {
        viewModel.getUserInfo()
    }
    // Initialize metric helper for library button
    val metric = rememberMetricHelper(EventRefer.Profile)

    val avatarVM: AvatarThumbViewModel = hiltViewModel()
    val imageUrlState = avatarVM.imageUrlState.collectAsState()
    val imageUrl = imageUrlState.value

    fun gotoCloset() {
        // 埋点：管理模特按钮点击
        metric(
            MetricEvent.SelectItem(
                itemListName = if (imageUrl.isNullOrBlank()) EventItemListName.NoAvatarCreateBtn else EventItemListName.ManageAvatarBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterAvatarManagement
            )
        )
        navActions.navigateToCloset()
    }

    fun gotoSettings() {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.SettingsBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterSetting
            )
        )
        navActions.navigateToUserSettings()
    }

    // Navigate to user library with metric
    fun gotoLibrary() {
        metric(
            MetricEvent.SelectItem(
                itemListName = EventItemListName.HistoryBtn,
                method = EventMethod.Click,
                actionType = EventActionType.EnterHistory
            )
        )
        navActions.navigateToUserLibrary()
    }

    Column(modifier = modifier) {
        Row {
            ClosetButton(onClick = { gotoCloset() }, navActions = navActions)

            Spacer(modifier = Modifier.weight(1f))

            IconButton(onClick = { gotoLibrary() }) {
                Image(
                    painterResource(R.drawable.icon_tryon_history),
                    null,
                    modifier = Modifier.size(24.dp),
                    colorFilter = ColorFilter.tint(Color(0xFF868D94))
                )
            }

            IconButton(onClick = { gotoSettings() }) {
                Image(
                    painterResource(R.drawable.icon_settings),
                    null,
                    modifier = Modifier.size(24.dp),
                    colorFilter = ColorFilter.tint(Color(0xFF868D94))
                )
            }
        }

        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            ProfileAvatar(
                onEdit = {
                    // 埋点：头像按钮点击进入编辑
                    metric(
                        MetricEvent.SelectItem(
                            itemListName = EventItemListName.PortraitBtn,
                            method = EventMethod.Click,
                            actionType = EventActionType.EnterProfileEdit
                        )
                    )
                    navActions.navigateToUserAccountEdit()
                },
                viewModel = viewModel
            )
        }
    }
}

@Composable
private fun ClosetButton(
    onClick: () -> Unit,
    navActions: NavActions,
) {
    Button(
        onClick = onClick,
        shape = MaterialTheme.shapes.medium,
        colors = ButtonDefaults.buttonColors(
            Color.Transparent,
            MaterialTheme.colorScheme.onSurface
        ),
        contentPadding = PaddingValues(0.dp),
        elevation = ButtonDefaults.elevatedButtonElevation(AppThemeElevation.extraSmall)
    ) {
        val gradientBrush = Brush.linearGradient(
            colors = listOf(
                AppThemeColors.White,
                AppThemeColors.White,
                AppThemeColors.Gray50,
            ),
            start = Offset(0f, Float.POSITIVE_INFINITY),
            end = Offset(Float.POSITIVE_INFINITY, 0f)
        )

        Row(
            modifier = Modifier
                .background(brush = gradientBrush)
                .height(40.dp)
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            AvatarThumb(navActions = navActions)
            Text("Manage your Avatar", style = AppThemeTextStyle.Body12H)
            Icon(Icons.Default.ChevronRight, null, modifier = Modifier.size(16.dp))
        }
    }
}

@Composable
private fun ProfileAvatar(
    onEdit: () -> Unit = {},
    viewModel: UserProfileViewModel = hiltViewModel(),
) {
    val userName = viewModel.userName.collectAsState()
    val userAvatar = viewModel.userAvatar.collectAsState()

    Column(
        modifier = Modifier.height(132.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Box {
            Box(
                modifier = Modifier
                    .size(96.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                AsyncImage(
                    model = when (userAvatar.value) {
                        "icon_random_thumb" -> R.drawable.icon_random_thumb
                        "icon_unregister" -> R.drawable.icon_unregister
                        else -> userAvatar.value
                    },
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                    error = painterResource(id = R.drawable.icon_unregister)
                )
            }

            Button(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(24.dp),
                shape = MaterialTheme.shapes.medium,
                onClick = { onEdit() },
                contentPadding = PaddingValues(4.dp),
                colors = ButtonDefaults.buttonColors(
                    MaterialTheme.colorScheme.surface,
                    MaterialTheme.colorScheme.onSurface
                )
            ) {
                Image(painterResource(R.drawable.icon_edit), null)
            }
        }

        // TODO:
        Text("@${userName.value}", style = AppThemeTextStyle.Heading18D)
    }
}
