package one.srp.gensmo.ui.components.collage

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import one.srp.gensmo.data.model.MoodboardContent
import one.srp.gensmo.utils.render.renderComposeToBitmap
import timber.log.Timber

@Composable
fun MoodboardPreview(item: MoodboardContent, onPreviewGen: (ImageBitmap) -> Unit = {}) {
    val context = LocalContext.current

    var screenshotStatus by remember { mutableStateOf(ScreenshotStatus.Loading) }
    var screenshotBitmap by remember { mutableStateOf<ImageBitmap?>(null) }

    LaunchedEffect(item) {
        try {
            val bitmap = renderComposeToBitmap(
                context, item.width.toInt(), (item.height ?: (item.width / 0.7)).toInt()
            ) {
                WatermarkContainer {
                    MoodboardRenderer(modifier = Modifier.fillMaxWidth(), item = item, offscreen = true)
                }
            }
            val imageBitmap = bitmap.asImageBitmap()

            delay(350)
            screenshotBitmap = imageBitmap
            onPreviewGen(imageBitmap)
            screenshotStatus = ScreenshotStatus.Success
        } catch (err: Exception) {
            Timber.tag("MoodboardPreview").e(err)
            screenshotStatus = ScreenshotStatus.Error
        }
    }

    AnimatedVisibility(visible = screenshotBitmap != null) {
        screenshotBitmap?.let {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .padding(4.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color.White),
            ) {
                Image(
                    bitmap = it,
                    contentDescription = "preview",
                    contentScale = ContentScale.FillHeight,
                    modifier = Modifier.fillMaxHeight()
                )
            }
        }
    }
}
