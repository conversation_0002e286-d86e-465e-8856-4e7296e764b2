package one.srp.gensmo.ui.screens.tryon.task._components

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowUpward
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import one.srp.gensmo.R
import one.srp.gensmo.data.model.OutfitComment
import one.srp.gensmo.data.model.OutfitEnthusiast
import one.srp.gensmo.ui.screens.tryon.task._viewmodel.TryOnBackgroundViewModel
import one.srp.gensmo.ui.theme.AppThemeFontFamily
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import java.util.Random

@Composable
fun PromptPanel(
    modifier: Modifier = Modifier,
    item: OutfitComment,
    onChangeBackground: (String?) -> Unit = {},
    tryOnBackgroundViewModel: TryOnBackgroundViewModel = hiltViewModel(),
    onOpenSearchPanel: (String?) -> Unit = {},
) {
    val context = LocalContext.current
    val polling = tryOnBackgroundViewModel.polling.collectAsState().value
    val bgSize = tryOnBackgroundViewModel.bgStateMap.collectAsState().value.size

    Column(
        modifier = Modifier.then(modifier),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item.outfitEnthusiast?.map { EnthusiastCard(it) }

        item.scenarioGuru?.let {
            ExtensionCard(
                painterResource(R.drawable.icon_star),
                extractContentParts(it.comment),
                onClick = {
                    if (polling) {
                        Toast.makeText(context, "Loading...", Toast.LENGTH_SHORT).show()
                    } else if (bgSize > 10) {
                        Toast.makeText(context, "Too many requests.", Toast.LENGTH_SHORT).show()
                    } else {
                        onChangeBackground(it.backgroundPrompt)
                    }
                })
        }

        item.searchStylist?.let {
            ExtensionCard(
                painterResource(R.drawable.icon_tryon_search),
                extractContentParts(it.searchSuggestionComment),
                onClick = {
                    onOpenSearchPanel(it.searchQuery)
                })
        }
    }
}

@Composable
private fun EnthusiastCard(item: OutfitEnthusiast) {
    val fakeAvatar = remember { fakeUserAvatar() }

    Card(
        colors = CardDefaults.cardColors(Color.White),
        elevation = CardDefaults.cardElevation(2.dp),
        shape = MaterialTheme.shapes.medium,
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Image(
                painter = painterResource(fakeAvatar),
                contentDescription = null,
                modifier = Modifier
                    .width(22.dp)
                    .height(22.dp)
                    .clip(CircleShape)
            )

            Text(
                item.text,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = AppThemeTextStyle.Body13H
            )
        }
    }
}

@Composable
private fun ExtensionCard(painter: Painter, parts: BracketContent, onClick: () -> Unit = {}) {
    val fullText = parts.ori.replace("<${parts.inside}>", parts.inside)
    val startIndex = fullText.indexOf(parts.inside)
    val endIndex = startIndex + parts.inside.length

    val annotatedString = buildAnnotatedString {
        append(fullText)
        addStyle(
            style = SpanStyle(
                color = Color(0xFF000000),
                fontFamily = AppThemeFontFamily.BodyDefault,
                fontSize = 13.sp,
                fontWeight = FontWeight(600),
                textDecoration = TextDecoration.Underline
            ),
            start = startIndex,
            end = endIndex
        )
        addStringAnnotation(
            tag = "URL",
            annotation = parts.inside,
            start = startIndex,
            end = endIndex
        )
    }

    Card(
        colors = CardDefaults.cardColors(Color.White),
        elevation = CardDefaults.cardElevation(2.dp),
        shape = MaterialTheme.shapes.medium,
    ) {
        Row(
            modifier = Modifier.padding(8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier
                    .width(22.dp)
                    .height(22.dp)
                    .clip(CircleShape)
            )

            Text(
                text = annotatedString,
                style = AppThemeTextStyle.Body13H,
                maxLines = 2,
                modifier = Modifier
                    .weight(1f)
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null,
                        onClick = onClick
                    )
            )

            Button(
                onClick = onClick,
                shape = MaterialTheme.shapes.small,
                modifier = Modifier.size(22.dp),
                contentPadding = PaddingValues(4.dp)
            ) {
                Icon(Icons.Default.ArrowUpward, null)
            }
        }
    }
}

private fun fakeUserAvatar(): Int {
    val randomIndex = Random().nextInt(10) + 1
    val avatarResourceId = "fake_avatar_$randomIndex"
    val drawableId = R.drawable::class.java.getField(avatarResourceId).getInt(null)
    return drawableId
}

private data class BracketContent(
    val ori: String,
    val before: String,
    val inside: String,
    val after: String,
)

private fun extractContentParts(text: String? = null): BracketContent {
    if (text == null) return BracketContent(text ?: "", "", "", "")

    val pattern = "(.+)<(.+)>(.+)".toRegex()
    return pattern.find(text)?.let { match ->
        BracketContent(
            text,
            before = match.groupValues[1].trim(),
            inside = match.groupValues[2].trim(),
            after = match.groupValues[3].trim()
        )
    } ?: BracketContent(text, "", "", "")
}
