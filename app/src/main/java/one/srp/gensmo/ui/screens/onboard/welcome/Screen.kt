package one.srp.gensmo.ui.screens.onboard.welcome

import android.view.ViewGroup
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import one.srp.gensmo.R
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.integration.LaunchAction
import one.srp.gensmo.utils.integration.LaunchActionDetector

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OnboardWelcomeScreen(navActions: NavActions = NavActions()) {
    val context = LocalContext.current
    LaunchedEffect(Unit) {
        LaunchActionDetector.recordFirstLaunch(
            context,
            LaunchAction.USER_ONBOARDING
        )
    }

    val coroutineScope = rememberCoroutineScope()
    fun endPlay() {
        navActions.navigateToFeedRecommend()

//        coroutineScope.launch {
//            val isLogin = UserDataStoreManager.isUserLoggedIn()
//            if (isLogin) {
//                navActions.navigateToFeedRecommend()
//            } else {
//                navActions.navigateToUserLogin()
//            }
//        }
    }

    val player = remember {
        ExoPlayer.Builder(context).build().apply {
            val rawUri =
                "android.resource://${context.packageName}/${R.raw.video_onboarding}".toUri()
            setMediaItem(MediaItem.fromUri(rawUri))
            playWhenReady = true
            prepare()
        }
    }

    DisposableEffect(player) {
        val listener = object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_ENDED -> {
                        endPlay()
                    }

                    else -> {}
                }
            }
        }
        player.addListener(listener)
        onDispose {
            player.removeListener(listener)
            player.release()
        }
    }

    Scaffold(topBar = {
        TopBar(
            transparent = true,
            navIconEnable = false,
            action = {
                Row(modifier = Modifier.padding(horizontal = 16.dp)) {
                    TextButton(onClick = { endPlay() }) {
                        Text(
                            stringResource(R.string.text_skip),
                            style = AppThemeTextStyle.Body16H
                        )
                    }
                }
            },
        )
    }) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(2.dp),
        ) {
            Column(
                horizontalAlignment = Alignment.Start,
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    "LOOK FOR",
                    style = AppThemeTextStyle.Heading32D,
                )

                Text(
                    "EVERY WEAR.",
                    style = AppThemeTextStyle.Heading32D,
                )
            }

            Box {
                AndroidView(
                    factory = {
                        PlayerView(it).apply {
                            this.player = player
                            useController = false
                            layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT
                            )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                )

                Column(modifier = Modifier.align(Alignment.Center)) {
                    Spacer(modifier = Modifier.weight(3f))

                    Text(
                        "GENSMO",
                        style = AppThemeTextStyle.Heading32D.copy(AppThemeColors.White.copy(0.95f)),
                    )

                    Spacer(modifier = Modifier.weight(3f))
                }
            }
        }
    }
}
