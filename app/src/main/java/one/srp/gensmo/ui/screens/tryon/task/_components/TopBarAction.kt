package one.srp.gensmo.ui.screens.tryon.task._components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material.icons.outlined.PersonOutline
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import one.srp.gensmo.R
import one.srp.gensmo.ui.theme.AppThemeTextStyle

@Composable
fun TopBarAction(
    onChangeAvatar: () -> Unit = {},
    onDownload: () -> Unit = {},
    onShare: () -> Unit = {},
    onViewTryOn: () -> Unit = {},
) {
    var expanded by remember { mutableStateOf(false) }
    fun close() {
        expanded = false
    }

    Box(modifier = Modifier.padding(end = 4.dp)) {
        TextButton(
            onClick = { expanded = !expanded },
            modifier = Modifier.size(40.dp),
            contentPadding = PaddingValues(0.dp)
        ) {
            Icon(
                Icons.Default.MoreHoriz, null, modifier = Modifier.size(30.dp)
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false },
            containerColor = MaterialTheme.colorScheme.background,
        ) {
            DropdownMenuItem(
                text = {
                    Text(
                        stringResource(R.string.text_change_avatar),
                        style = AppThemeTextStyle.Body13H
                    )
                },
                onClick = {
                    close()
                    onChangeAvatar()
                },
                leadingIcon = { Icon(Icons.Outlined.PersonOutline, null) }
            )

            DropdownMenuItem(
                text = {
                    Text(
                        stringResource(R.string.text_download_look),
                        style = AppThemeTextStyle.Body13H
                    )
                },
                onClick = {
                    close()
                    onDownload()
                },
                leadingIcon = { Icon(painterResource(R.drawable.icon_download), null) }
            )

            DropdownMenuItem(
                text = {
                    Text(
                        stringResource(R.string.text_share),
                        style = AppThemeTextStyle.Body13H
                    )
                },
                onClick = {
                    close()
                    onShare()
                },
                leadingIcon = { Icon(painterResource(R.drawable.icon_share_line), null) }
            )

            DropdownMenuItem(
                text = {
                    Text(
                        stringResource(R.string.text_view_try_ons),
                        style = AppThemeTextStyle.Body13H
                    )
                },
                onClick = {
                    close()
                    onViewTryOn()
                },
                leadingIcon = { Icon(painterResource(R.drawable.icon_tryon_history), null) }
            )
        }
    }
}