package one.srp.gensmo.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

object AppThemeElevation {
    val extraSmall = 1.dp
    val small = 6.dp
    val medium = 12.dp
    val large = 24.dp
    val extraLarge = 32.dp
}

object AppThemeRadius{
    val extraSmall = 2.dp
    val small = 2.dp
    val medium = 4.dp
    val large = 8.dp
    val extraLarge = 16.dp
    val extraExtraLarge = 20.dp
}

val appThemeShapes = Shapes(
    extraSmall = RoundedCornerShape(AppThemeRadius.extraSmall),
    small = RoundedCornerShape(AppThemeRadius.small),
    medium = RoundedCornerShape(AppThemeRadius.medium),
    large = RoundedCornerShape(AppThemeRadius.large),
    extraLarge = RoundedCornerShape(AppThemeRadius.extraLarge)
)
