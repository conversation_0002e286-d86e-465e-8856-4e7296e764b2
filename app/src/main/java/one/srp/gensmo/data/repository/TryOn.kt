package one.srp.gensmo.data.repository

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import one.srp.gensmo.data.model.TryOnBackgroundParams
import one.srp.gensmo.data.model.TryOnBackgroundRes
import one.srp.gensmo.data.model.TryOnBackgroundTaskRes
import one.srp.gensmo.data.remote.apis.TryOnApi
import one.srp.gensmo.data.repository.utils.PollState
import one.srp.gensmo.data.repository.utils.parseOrThrow
import one.srp.gensmo.data.repository.utils.poll
import javax.inject.Inject

data class PollTask<T>(
    val job: Job,
    val flow: Flow<PollState<T>>?,
)

class TryOnBackgroundRepository @Inject constructor(
    private val api: TryOnApi,
) {
    fun submitChangeBackground(params: TryOnBackgroundParams): Flow<Result<TryOnBackgroundTaskRes>> =
        flow {
            emit(runCatching {
                parseOrThrow { api.submitChangeBackground(params) }
            })
        }.flowOn(Dispatchers.IO)


    fun pollChangeBackground(
        generateUuid: String,
    ): Flow<PollState<TryOnBackgroundRes>> =
        poll(shouldContinue = {
            it.getOrNull()?.status != "success"
        }) {
            parseOrThrow { api.checkChangeBackground(generateUuid) }
        }.flowOn(Dispatchers.IO)
}

