package one.srp.gensmo.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class TryOnParams(
    val isAsync: Boolean = false,
    val modelId: String? = null,
    val tryOnTaskId: String? = null,
    val moodboardId: String? = null,
    val products: List<ProductItem>? = null,
    val userImage: String? = null,
    val taskId: String? = null,
    val userImageTag: String? = null,
    val garmentType: String? = null,
    val internalImageList: List<String>? = null,
    val isFeed: Boolean = false,

    // NOTE: Web Socket only
    val messageId: String? = null,
)

@Serializable
data class TryOnTaskItem(
    val tryOnTaskId: String? = null,
    val garmentType: String? = null,
    val modelId: String? = null,
    val tryOnUrl: String? = null,
    val outfitDescription: OutfitDescription? = null,
    val reasoning: String? = null,
    val tryOnUrlType: String? = null,
    val userId: String? = null,
    val products: List<ProductItem>? = null,
    val outfitComment: OutfitComment? = null,
    val status: String? = null,
    var useDefaultModel: Boolean? = null,
    var isFavorited: Boolean? = null,
    val moodboardId: String? = null,
    val streetTryOnTaskId: String? = null,
    val userImageTag: String? = null,
    val userImageUrl: String? = null,
    val changeBackgroundImagesUrl: List<ChangeBackgroundItem>? = null,
)

@Serializable
data class ChangeBackgroundItem(
    val generateUuid: String,
    val imageUrl: String,
)

@Serializable
data class OutfitComment(
    val outfitEnthusiast: List<OutfitEnthusiast>? = null,
    val searchStylist: SearchStylist? = null,
    val scenarioGuru: ScenarioGuru? = null,
)

@Serializable
data class OutfitEnthusiast(
    val text: String,
)

@Serializable
data class SearchStylist(
    val searchSuggestionComment: String? = null,
    val searchQuery: String? = null,
    val searchProductsResult: List<ProductItem>? = null,
)

@Serializable
data class ScenarioGuru(
    val comment: String? = null,
    val backgroundPrompt: String? = null,
)

@Serializable
data class OutfitDescription(
    val colors: List<TryOnColor>? = null,
    val description: String? = null,
)

@Serializable
data class TryOnColor(
    val hex: String,
    val name: String,
)

@Serializable
data class TryonReplicaResponse(
    val modelId: String,
    val modelUrl: String,
    val userPreference: UserPreference,
)

@Serializable
data class UserPreference(
    val ageGroup: String? = null,
    val gender: String? = null,
    val race: String? = null,
    val bodyType: List<String>? = null,
)

@Serializable
data class UserPreferenceForReplica(
    val bodyType: List<String>? = null,
    val bodyShape: String? = null,
    val otherNeeds: String? = null,
)

@Serializable
data class DeleteReplicaResponse(
    val status: String,
)

@Serializable
data class DeleteReplicaModelResponse(
    val status: String,
)

@Serializable
@JvmInline
value class TryOnHistoryResponse(
    val list: List<TryOnHistoryItem> = emptyList(),
)

@Serializable
data class TryOnHistoryItem(
    @SerialName("try_on_task_id")
    val tryOnTaskId: String,
    @SerialName("last_updated")
    val lastUpdated: Long,
    @SerialName("try_on_url")
    val tryOnUrl: String,
)

@Serializable
data class ValidateReplicaFaceParams(
    val userImageUrl: String,
    val isAsync: Boolean = false,
    val skipFaceDetection: Boolean = false,
)

@Serializable
data class GenerateReplicaHistoryParams(
    val limit: Int,
    val lastId: String? = null,
)

@Serializable
data class GenerateReplicaFaceParams(
    val replicaTaskId: String,
    val isAsync: Boolean,
    val type: String,
    val preference: UserPreferenceForReplica,
    val registerModel: Boolean,
)

@Serializable
data class ValidateReplicaFaceResponse(
    @SerialName("is_valid")
    val isValid: Boolean,
    @SerialName("valid_message")
    val validMessage: String,
    @SerialName("replica_task_id")
    val replicaTaskId: String,
    @SerialName("status")
    val status: String,
)

@Serializable
data class ReplicaListItem(
    @SerialName("model_id")
    val modelId: String,
    @SerialName("model_url")
    val modelUrl: String,
)

@Serializable
data class ReplicaTaskStatusResponse(
    @SerialName("replica_task_id")
    val replicaTaskId: String,
    val preference: UserPreferenceForReplica? = null,
    @SerialName("user_image_url")
    val userImageUrl: String,
    val status: String,
    @SerialName("replica_list_size")
    val replicaListSize: Int? = null,
    @SerialName("replica_list")
    val replicaList: List<ReplicaListItem>? = null,
    val type: String,
    @SerialName("total_steps")
    val totalSteps: Int,
    @SerialName("cur_step")
    val curStep: Int,
)

@Serializable
data class TryOnBackgroundParams(
    val imageUrl: String,
    val prompt: String,
    val negativePrompt: String,
    val tryOnTaskId: String,
    val isAsync: Boolean = false,

    // NOTE: for session socket
    val messageId: String? = null,
)

@Serializable
data class TryOnBackgroundTaskRes(
    val generateUuid: String,
    val msg: String,
)

@Serializable
data class TryOnBackgroundRes(
    val changeBackgroundImageUrl: String? = null,
    val status: String,
    val msg: String,
)

@Serializable
data class GenerateReplicaHistoryResponse(
    val errno: Int,
    val errmsg: String,
    @SerialName("last_id")
    val lastId: String?,
    @SerialName("has_more")
    val hasMore: Boolean,
    val data: List<GenerateReplicaHistoryItem>,
)

@Serializable
data class GenerateReplicaHistoryItem(
    val preference: ReplicaPreference? = null,
    @SerialName("replica_list_size")
    val replicaListSize: Int,
    @SerialName("replica_list")
    val replicaList: List<ReplicaListItem>,
)

@Serializable
data class ReplicaPreference(
    @SerialName("age_group")
    val ageGroup: String? = null,
    val gender: String? = null,
    val race: String? = null,
    @SerialName("body_type")
    val bodyType: List<String>? = null,
    @SerialName("body_shape")
    val bodyShape: String,
    @SerialName("other_needs")
    val otherNeeds: String? = null,
    val glasses: String? = null,
)

@Serializable
data class UpdateReplicaParams(
    val modelId: String,
    val modelUrl: String,
)

@Serializable
data class UpdateReplicaResponse(
    val status: String,
)

@Serializable
data class TryOnTaskTryOnItems(
    val box1: List<MoodboardTryOnBoxItem<ProductItem>>? = null,
    val box2: List<MoodboardTryOnBoxItem<ProductItem>>? = null,
)
