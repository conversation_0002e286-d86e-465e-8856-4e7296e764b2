package one.srp.gensmo.data.repository

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import one.srp.gensmo.data.model.HistoryQueriesItem
import one.srp.gensmo.data.remote.UserService
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HistoryQueriesRepository @Inject constructor() {
    private val _historyQueries = MutableStateFlow<List<HistoryQueriesItem>>(emptyList())
    val historyQueries: StateFlow<List<HistoryQueriesItem>> = _historyQueries.asStateFlow()
    
    private val _hasMore = MutableStateFlow(true)
    val hasMore: StateFlow<Boolean> = _hasMore.asStateFlow()
    
    private var currentPage = 1
    private val pageSize = 10
    
    suspend fun loadHistoryQueries(isRefresh: Boolean = false) {
        if (isRefresh) {
            currentPage = 1
            _hasMore.value = true
            _historyQueries.value = emptyList()
        }

        if (!_hasMore.value) return

        try {
            val response = UserService.api.getHistoryQueries(
                page = currentPage,
                limit = pageSize
            )
            if (response.isSuccessful) {
                val body = response.body()!!
                Timber.d("获取查询历史记录成功，数量：${body.data.size}")
                
                if (body.data.isEmpty()) {
                    _hasMore.value = false
                } else {
                    _historyQueries.value = if (currentPage == 1) {
                        body.data
                    } else {
                        _historyQueries.value + body.data
                    }
                    currentPage++
                    _hasMore.value = currentPage <= body.pagination.totalPages
                }
            } else {
                Timber.e("获取查询历史记录失败: ${response.message()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "获取查询历史记录失败")
        }
    }

    suspend fun deleteHistoryQuery(taskId: String) {
        try {
            val response = UserService.api.deleteHistoryQuery(taskId)
            if (response.isSuccessful) {
                Timber.d("删除查询历史记录成功")
                loadHistoryQueries(true)
            }
        } catch (e: Exception) {
            Timber.e(e, "删除查询历史记录失败")
        }
    }
}
