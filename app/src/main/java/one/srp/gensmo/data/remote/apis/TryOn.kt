package one.srp.gensmo.data.remote.apis

import one.srp.gensmo.data.model.DeleteReplicaModelResponse
import one.srp.gensmo.data.model.DeleteReplicaResponse
import one.srp.gensmo.data.model.GenerateReplicaFaceParams
import one.srp.gensmo.data.model.GenerateReplicaHistoryParams
import one.srp.gensmo.data.model.GenerateReplicaHistoryResponse
import one.srp.gensmo.data.model.ReplicaTaskStatusResponse
import one.srp.gensmo.data.model.TryOnBackgroundParams
import one.srp.gensmo.data.model.TryOnBackgroundRes
import one.srp.gensmo.data.model.TryOnBackgroundTaskRes
import one.srp.gensmo.data.model.TryOnHistoryResponse
import one.srp.gensmo.data.model.TryOnParams
import one.srp.gensmo.data.model.TryOnTaskItem
import one.srp.gensmo.data.model.TryonReplicaResponse
import one.srp.gensmo.data.model.UpdateReplicaParams
import one.srp.gensmo.data.model.UpdateReplicaResponse
import one.srp.gensmo.data.model.ValidateReplicaFaceParams
import one.srp.gensmo.data.model.ValidateReplicaFaceResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface TryOnApi {
    @GET("/get_replica")
    suspend fun getReplica(
    ): Response<TryonReplicaResponse>

    @POST("/delete_replica")
    suspend fun deleteReplica(
    ): Response<DeleteReplicaResponse>

    @DELETE("/replica/{model_id}")
    suspend fun deleteReplicaModel(
        @Path("model_id") modelId: String,
    ): Response<DeleteReplicaModelResponse>

    @GET("/try_on_history")
    suspend fun getTryOnHistory(
        @Query("offset") offset: Int = 0,
        @Query("limit") limit: Int = 20
    ): Response<TryOnHistoryResponse>

    @DELETE("/try_on_history/{try_on_task_id}")
    suspend fun deleteTryOnHistory(
        @Path("try_on_task_id") tryOnTaskId: String,
    ): Response<Unit>

    @POST("/bind_user_replica")
    suspend fun updateReplica(
        @Body body: UpdateReplicaParams,
    ): Response<UpdateReplicaResponse>

    @POST("/tryon/try_on/v2")
    suspend fun generateTryOnTask(
        @Body body: TryOnParams,
    ): Response<TryOnTaskItem>

    @GET("/tryon/get_try_on_task")
    suspend fun getTryOnTask(
        @Query("try_on_task_id") taskId: String,
    ): Response<TryOnTaskItem>

    @POST("/replica/generate/history")
    suspend fun generateReplicaHistory(
        @Body body: GenerateReplicaHistoryParams,
    ): Response<GenerateReplicaHistoryResponse>

    @POST("/replica/valid")
    suspend fun validateReplicaFace(
        @Body body: ValidateReplicaFaceParams,
    ): Response<ValidateReplicaFaceResponse>

    @POST("/replica/generate/v2")
    suspend fun generateReplicaFace(
        @Body body: GenerateReplicaFaceParams,
    ): Response<Unit>

    @GET("/replica/generate/{replica_task_id}")
    suspend fun getReplicaStatus(
        @Path("replica_task_id") replicaTaskId: String,
    ): Response<ReplicaTaskStatusResponse>

    @POST("/tryon/change_background/submit")
    suspend fun submitChangeBackground(
        @Body body: TryOnBackgroundParams,
    ): Response<TryOnBackgroundTaskRes>

    @POST("/tryon/change_background/check")
    suspend fun checkChangeBackground(
        @Query("generate_uuid") generateUuid: String,
    ): Response<TryOnBackgroundRes>
}
