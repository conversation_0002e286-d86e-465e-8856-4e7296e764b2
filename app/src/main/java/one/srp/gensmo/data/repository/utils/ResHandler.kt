package one.srp.gensmo.data.repository.utils

import retrofit2.Response
import timber.log.Timber

suspend fun <T> parseOrNull(fetcher: suspend () -> Response<T>): T? = try {
    fetcher().takeIf { it.isSuccessful }?.body()
} catch (e: Exception) {
    Timber.e(e)
    null
}

suspend fun <T> parseOrThrow(fetcher: suspend () -> Response<T>): T = try {
    val response = fetcher()
    if (response.isSuccessful) {
        response.body() ?: throw IllegalStateException("Response body is null")
    } else {
        throw IllegalStateException("Request failed with code: ${response.code()}")
    }
} catch (e: Exception) {
    Timber.e(e)
    throw e
}
