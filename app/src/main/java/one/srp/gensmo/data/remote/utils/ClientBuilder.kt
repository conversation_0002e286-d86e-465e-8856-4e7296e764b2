package one.srp.gensmo.data.remote.utils

import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import kotlinx.coroutines.runBlocking
import okhttp3.ConnectionPool
import okhttp3.Dns
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import one.srp.gensmo.data.store.ABDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.data.utils.JSON
import one.srp.gensmo.utils.env.MetaInfo
import retrofit2.Retrofit
import timber.log.Timber
import java.io.IOException
import java.net.ConnectException
import java.net.InetAddress
import java.net.NoRouteToHostException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.math.min
import kotlin.math.pow
import kotlin.random.Random

fun createRetrofitInstance(
    baseUrl: String,
    client: OkHttpClient,
    type: String = "restful",
): Retrofit {
    return Retrofit.Builder().apply {
        baseUrl(baseUrl)
        client(client)

        when (type) {
            "restful" -> {
                addConverterFactory(NullOnEmptyConverterFactory())
                addConverterFactory(JSON.asConverterFactory("application/json".toMediaType()))
            }

            else -> {
                addConverterFactory(JSON.asConverterFactory("application/json".toMediaType()))
            }
        }
    }.build()
}

/**
 * 智能DNS缓存，减少DNS解析失败
 */
private class CachingDns : Dns {
    private val cache = ConcurrentHashMap<String, List<InetAddress>>()
    private val cacheTimestamps = ConcurrentHashMap<String, Long>()
    private val cacheTimeout = TimeUnit.MINUTES.toMillis(30) // 30分钟缓存

    override fun lookup(hostname: String): List<InetAddress> {
        val now = System.currentTimeMillis()
        val cached = cache[hostname]
        val timestamp = cacheTimestamps[hostname] ?: 0

        // 如果缓存有效，直接返回
        if (cached != null && (now - timestamp) < cacheTimeout) {
            return cached
        }

        return try {
            val addresses = Dns.SYSTEM.lookup(hostname)
            // 更新缓存
            cache[hostname] = addresses
            cacheTimestamps[hostname] = now
            addresses
        } catch (e: UnknownHostException) {
            // 如果DNS解析失败，尝试使用缓存的地址
            cached ?: throw e
        }
    }
}

/**
 * 创建带有智能重试机制的网络拦截器
 * 支持指数退避 + 随机抖动，避免雷群效应
 */
private fun createRetryInterceptor(
    maxRetries: Int = 3,
    baseDelayMs: Long = 1000L,
    maxDelayMs: Long = 10000L,
    backoffMultiplier: Double = 2.0,
): Interceptor = Interceptor { chain ->
    val request = chain.request()
    var response: okhttp3.Response? = null
    var lastException: IOException? = null

    var attempt = 0
    while (attempt <= maxRetries) {
        try {
            response?.close() // 关闭之前的响应
            response = chain.proceed(request)

            // 如果响应成功，直接返回
            if (response.isSuccessful) {
                if (attempt > 0) {
                    Timber.d("网络请求重试成功 (attempt ${attempt + 1}/${maxRetries + 1})")
                }
                return@Interceptor response
            }

            // 对于HTTP错误码，检查是否需要重试
            if (!shouldRetryHttpError(response.code) || attempt == maxRetries) {
                return@Interceptor response
            }

            Timber.d("HTTP错误重试 ${response.code} (attempt ${attempt + 1}/${maxRetries + 1})")

        } catch (e: IOException) {
            lastException = e

            // 检查是否应该重试这个异常
            if (!shouldRetryException(e) || attempt == maxRetries) {
                if (attempt > 0) {
                    Timber.w(
                        "网络请求最终失败 (attempt ${attempt + 1}/${maxRetries + 1}): ${
                            getExceptionType(
                                e
                            )
                        }"
                    )
                }
                throw e
            }

            if (attempt == 0) {
                Timber.d("网络异常，开始重试: ${getExceptionType(e)}")
            }
        }

        // 准备下一次重试
        attempt++

        // 如果还有重试机会，等待后重试
        if (attempt <= maxRetries) {
            val delay = calculateRetryDelay(attempt - 1, baseDelayMs, maxDelayMs, backoffMultiplier)
            Thread.sleep(delay)
        }
    }

    // 如果所有重试都失败了
    lastException?.let { throw it }
    response ?: throw IOException("网络请求失败，已重试${maxRetries + 1}次")
}

/**
 * 判断是否应该对异常进行重试
 */
private fun shouldRetryException(exception: IOException): Boolean {
    return when (exception) {
        // 网络连接问题 - 应该重试
        is UnknownHostException -> true  // DNS解析失败
        is ConnectException -> true      // 连接失败
        is SocketTimeoutException -> true // 超时
        is NoRouteToHostException -> true // 无路由

        // Socket相关问题 - 根据具体信息判断
        is SocketException -> {
            val message = exception.message?.lowercase() ?: ""
            message.contains("connection reset") ||
                    message.contains("software caused connection abort") ||
                    message.contains("broken pipe") ||
                    message.contains("network is unreachable") ||
                    message.contains("host is unreachable") ||
                    message.contains("connection timed out") ||
                    message.contains("no route to host")
        }

        // 其他特定IO异常 - 根据具体信息判断
        else -> {
            val message = exception.message?.lowercase() ?: ""
            message.contains("unexpected end of stream") ||
                    message.contains("connection pool shut down") ||
                    message.contains("timeout") ||
                    message.contains("failed to connect") ||
                    message.contains("stream was reset") ||
                    message.contains("protocol exception")
        }
    }
}

/**
 * 判断HTTP错误码是否应该重试
 */
private fun shouldRetryHttpError(code: Int): Boolean {
    return when (code) {
        408, // Request Timeout
        429, // Too Many Requests (但需要注意退避策略)
        500, // Internal Server Error
        502, // Bad Gateway
        503, // Service Unavailable
        504, // Gateway Timeout
        507, // Insufficient Storage
        509, // Bandwidth Limit Exceeded
            -> true

        else -> false
    }
}

/**
 * 获取异常类型的简短描述
 */
private fun getExceptionType(exception: IOException): String {
    return when (exception) {
        is UnknownHostException -> "DNS解析失败"
        is ConnectException -> "连接失败"
        is SocketTimeoutException -> "连接超时"
        is NoRouteToHostException -> "网络不可达"
        is SocketException -> "Socket异常"
        else -> "网络IO异常"
    }
}

/**
 * 计算重试延迟时间（指数退避 + 随机抖动）
 */
private fun calculateRetryDelay(
    attempt: Int,
    baseDelayMs: Long,
    maxDelayMs: Long,
    backoffMultiplier: Double,
): Long {
    // 指数退避
    val exponentialDelay = (baseDelayMs * backoffMultiplier.pow(attempt.toDouble())).toLong()

    // 添加随机抖动 (±25%)，避免雷群效应
    val jitter = exponentialDelay * 0.25
    val randomJitter = Random.nextDouble(-jitter, jitter).toLong()

    val finalDelay = min(exponentialDelay + randomJitter, maxDelayMs)
    return maxOf(finalDelay, baseDelayMs) // 确保最小延迟
}

fun createOkHttpClient(type: String = "", timeoutSeconds: Long = 60L): OkHttpClient {
    val loggingInterceptor = HttpLoggingInterceptor().apply {
        level = HttpLoggingInterceptor.Level.BODY
    }

    val headerInterceptor = Interceptor { chain ->
        val originalRequest = chain.request()
        val token = runBlocking { UserDataStoreManager.getToken() }
        val deviceId = runBlocking { DeviceDataStoreManager.getDeviceId() }
        val abInfo = runBlocking { ABDataStoreManager.getABTestConfigJsonString() }
        val newRequest = originalRequest.newBuilder().header("f-version", MetaInfo.VERSION)
            .header("f-source", MetaInfo.PLATFORM)
            .header("f-ab-info", abInfo)
            .header("Deviceid", deviceId)
            .header("X-Gensmo-Active-Id", deviceId)
            .header("X-Gensmo-Timestamp", (System.currentTimeMillis() * 1000).toString()).apply {
                if (token != null) {
                    header("Authorization", "Bearer $token")
                }
            }.build()
        chain.proceed(newRequest)
    }

    return OkHttpClient.Builder().apply {
        // 基础超时设置
        readTimeout(timeoutSeconds, TimeUnit.SECONDS)
        writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
        connectTimeout(15, TimeUnit.SECONDS)
        callTimeout(timeoutSeconds + 30, TimeUnit.SECONDS)

        // 连接池优化 - 保持更多连接，减少重新建立连接的开销
        connectionPool(ConnectionPool())

        // 使用智能DNS缓存
        dns(CachingDns())

        // 支持失败重试
        retryOnConnectionFailure(true)

        addInterceptor(loggingInterceptor)

        when (type) {
            "restful" -> {
                addInterceptor(headerInterceptor)
                // 主要API请求：更多重试次数，适中的延迟
                addInterceptor(
                    createRetryInterceptor(
                        maxRetries = 4,
                        baseDelayMs = 800L,
                        maxDelayMs = 10000L,
                        backoffMultiplier = 2.0
                    )
                )
            }

            "websocket" -> {
                // WebSocket通常有自己的重连机制
                connectTimeout(10, TimeUnit.SECONDS)
                addInterceptor(headerInterceptor)
            }

            "metric" -> {
                // 埋点请求：快速失败，避免影响用户体验
                connectTimeout(5, TimeUnit.SECONDS)
                callTimeout(15, TimeUnit.SECONDS)
                addInterceptor(
                    createRetryInterceptor(
                        maxRetries = 2,
                        baseDelayMs = 300L,
                        maxDelayMs = 2000L,
                        backoffMultiplier = 1.5
                    )
                )
            }

            else -> {
                // 默认配置：基础重试
                addInterceptor(
                    createRetryInterceptor(
                        maxRetries = 2,
                        baseDelayMs = 1000L,
                        maxDelayMs = 8000L,
                        backoffMultiplier = 2.0
                    )
                )
            }
        }
    }.build()
}
