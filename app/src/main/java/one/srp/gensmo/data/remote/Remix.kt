package one.srp.gensmo.data.remote

import one.srp.gensmo.data.remote.utils.RemoteClients
import retrofit2.http.POST
import retrofit2.http.Query

object RemixService {
    val api: RemixServiceApi by lazy {
        RemoteClients.workflow.create(RemixServiceApi::class.java)
    }
}

interface RemixServiceApi {
    @POST("/feed_moodboard_remix")
    suspend fun reportFeedRemix(
        @Query("moodboard_id") moodboardId: String,
    )
}
