package one.srp.gensmo.data.remote.apis

import one.srp.gensmo.data.model.SessionHistoryRes
import one.srp.gensmo.data.model.SessionItemRes
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface SessionApi {
    @GET("/chat/users/sessions")
    suspend fun getSessionHistory(
        @Query("limit") limit: Int,
        @Query("skip") skip: Int,
    ): Response<SessionHistoryRes>

    @GET("/chat/sessions/{session_id}/messages")
    suspend fun getSessionMessages(
        @Path("session_id") sessionId: String,
        @Query("limit") limit: Int,
        @Query("skip") skip: Int,
    ): Response<SessionItemRes>
}
