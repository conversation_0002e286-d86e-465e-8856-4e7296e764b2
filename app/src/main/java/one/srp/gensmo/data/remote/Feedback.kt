package one.srp.gensmo.data.remote

import kotlinx.serialization.Serializable
import one.srp.gensmo.data.remote.utils.RemoteClients
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

object FeedbackService {
    val api: FeedbackServiceApi by lazy {
        RemoteClients.workflow.create(FeedbackServiceApi::class.java)
    }
}

interface FeedbackServiceApi {
    @POST("/feedback")
    suspend fun sendFeedback(
        @Body input: FeedbackInput,
    ): Response<FeedbackRes>
}

@Serializable
data class FeedbackRes(
    val status: String? = null,
)

@Serializable
data class FeedbackInput(
    val taskId: String,
    val moodboardId: String,
    val feedbackType: String,
    val feedbackContent: String = "",
)
