package one.srp.gensmo.data.store

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import one.srp.gensmo.data.model.UserInfoResponse
import one.srp.gensmo.data.remote.AccountService
import one.srp.gensmo.data.remote.DeviceIdRequest
import one.srp.gensmo.data.remote.UserService
import one.srp.gensmo.utils.integration.OneSignalManager
import timber.log.Timber


// Create the DataStore instance
val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_data")

// Define keys for your data
object UserPreferencesKeys {
    val TOKEN = stringPreferencesKey("token")
    val LOGIN_TYPE = intPreferencesKey("login_type")
    val USER_INFO = stringPreferencesKey("user_info")  // 用于存储 UserInfoResponse
    val MODEL_URL = stringPreferencesKey("model_url")
    val MODEL_ID = stringPreferencesKey("model_id")  // 添加新的key用于存储model id
    val GUEST_USER_ID = stringPreferencesKey("guest_user_id")  // 添加新的key用于存储游客ID
}

// 定义登录类型的常量
object LoginType {
    const val LOGGED_OUT = 0    // 登出状态
    const val LOGGED_IN = 1     // 登录状态
    const val GUEST = 2         // 游客状态
}

// 创建一个单例对象来管理 DataStore
object UserDataStoreManager {
    private var cachedToken: String? = null
    private var cachedLoginType: Int? = null
    private var cachedUserInfo: UserInfoResponse? = null
    private var cachedModelUrl: String? = null
    private var cachedModelId: String? = null  // 添加新的缓存变量
    private var cachedGuestUserId: String? = null  // 添加游客ID缓存
    private var dataStore: DataStore<Preferences>? = null
    private var oneSignalManager: OneSignalManager? = null

    fun initialize(context: Context, oneSignalManager: OneSignalManager? = null) {
        dataStore = context.dataStore
        this.oneSignalManager = oneSignalManager
    }

    private suspend fun saveToken(token: String) {
        dataStore?.edit { preferences ->
            preferences[UserPreferencesKeys.TOKEN] = token
        }
        cachedToken = token  // 更新缓存的token
    }

    suspend fun getToken(): String? {
        if (cachedToken != null) {
            return cachedToken
        }

        cachedToken = dataStore?.data?.map { preferences ->
            preferences[UserPreferencesKeys.TOKEN]
        }?.first()
        return cachedToken
    }

    private suspend fun clearToken() {
        dataStore?.edit { preferences ->
            preferences.remove(UserPreferencesKeys.TOKEN)
        }
        cachedToken = null
    }

    private suspend fun saveLoginType(type: Int) {
        dataStore?.edit { preferences ->
            preferences[UserPreferencesKeys.LOGIN_TYPE] = type
        }
        cachedLoginType = type
    }

    suspend fun getLoginType(): Int {
        if (cachedLoginType != null) {
            return cachedLoginType!!
        }

        cachedLoginType = dataStore?.data?.map { preferences ->
            preferences[UserPreferencesKeys.LOGIN_TYPE] ?: LoginType.LOGGED_OUT
        }?.first() ?: LoginType.LOGGED_OUT
        return cachedLoginType!!
    }

    private suspend fun clearGuestUserId() {
        dataStore?.edit { preferences ->
            preferences.remove(UserPreferencesKeys.GUEST_USER_ID)
        }
        cachedGuestUserId = null
    }

    suspend fun saveUserLogin(token: String) {
        saveToken(token)
        saveLoginType(LoginType.LOGGED_IN)
        clearGuestUserId()
    }

    private suspend fun saveGuestLogin(token: String) {
        saveToken(token)
        saveLoginType(LoginType.GUEST)
    }

    suspend fun clearUserLogin() {
        clearToken()
        clearUserInfo()
        clearModelInfo()  // 替换原来的 clearModelUrl
        saveLoginType(LoginType.LOGGED_OUT)
    }

    suspend fun initializeGuestTokenIfNeeded() {
        val token = getToken()
        if (token == null) {
            try {
                val deviceId = DeviceDataStoreManager.getDeviceId()
                val deviceIdRequest = DeviceIdRequest(deviceId = deviceId)
                val response = AccountService.api.getUnregisterUserToken(deviceIdRequest)
                if (response.isSuccessful) {
                    val unregisterUserToken = response.body()
                    Timber.d("游客Token: ${unregisterUserToken?.jwtToken?.accessToken}")
                    saveGuestLogin(unregisterUserToken?.jwtToken?.accessToken ?: "")
                    // 保存游客ID
                    unregisterUserToken?.uid?.let { uid ->
                        saveGuestUserId(uid)
                        oneSignalManager?.registerUser(uid)
                    }
                } else {
                    Timber.e("获取游客token失败: ${response.code()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "获取游客token时发生异常")
            }
        } else {
            Timber.d("Token已存在")
        }
    }

    private suspend fun saveGuestUserId(uid: String) {
        dataStore?.edit { preferences ->
            preferences[UserPreferencesKeys.GUEST_USER_ID] = uid
        }
        cachedGuestUserId = uid
    }

    private suspend fun getGuestUserId(): String? {
        if (cachedGuestUserId != null) {
            return cachedGuestUserId
        }

        cachedGuestUserId = dataStore?.data?.map { preferences ->
            preferences[UserPreferencesKeys.GUEST_USER_ID]
        }?.first()
        return cachedGuestUserId
    }

    suspend fun saveUserInfo(userInfo: UserInfoResponse) {
        val userInfoJson = kotlinx.serialization.json.Json.encodeToString(
            UserInfoResponse.serializer(),
            userInfo
        )
        dataStore?.edit { preferences ->
            preferences[UserPreferencesKeys.USER_INFO] = userInfoJson
        }
        cachedUserInfo = userInfo
    }

    suspend fun getUserInfo(): UserInfoResponse? {
        if (cachedUserInfo != null) {
            return cachedUserInfo
        }

        return try {
            val userInfoJson = dataStore?.data?.map { preferences ->
                preferences[UserPreferencesKeys.USER_INFO]
            }?.first()

            userInfoJson?.let {
                kotlinx.serialization.json.Json.decodeFromString<UserInfoResponse>(it)
            }?.also {
                cachedUserInfo = it
            }
        } catch (e: Exception) {
            Timber.e(e, "解析用户信息失败")
            null
        }
    }

    private suspend fun clearUserInfo() {
        dataStore?.edit { preferences ->
            preferences.remove(UserPreferencesKeys.USER_INFO)
        }
        cachedUserInfo = null
    }

    suspend fun getLoggedInUserInfo() {
        if (getLoginType() == LoginType.LOGGED_IN) {
            Timber.d("getUserInfo logged in")
            try {
                val response = AccountService.api.getUserInfo()
                if (response.isSuccessful) {

                    val userInfo = response.body()
                    userInfo?.uidStr?.let {
                        val userProfile = UserService.api.getUserProfile(it)
                        if (userProfile.isSuccessful) {
                            val userProfileBody = userProfile.body()
                            userProfileBody?.profilePicture?.let { profilePicture ->
                                userInfo.avatar = profilePicture
                            }
                        }
                    }

                    Timber.d("用户信息: $userInfo")
                    userInfo?.let {
                        saveUserInfo(it)  // 保存 UserInfoResponse 对象
                        
                        // 在获取到用户信息后注册OneSignal用户
                        val userId = it.uidStr ?: it.uid.toString()
                        oneSignalManager?.registerUser(userId)
                    }
                } else {
                    Timber.e("获取用户信息失败: ${response.code()}")
                }
            } catch (e: Exception) {
                Timber.e(e, "获取用户信息时发生异常")
            }
        } else {
            Timber.d("getUserInfo login type: ${getLoginType()}")
        }
    }

    suspend fun isUserLoggedIn(): Boolean {
        return getLoginType() == LoginType.LOGGED_IN
    }

    /**
     * 获取用户ID
     * @return 用户ID字符串，如果用户未登录或信息不存在则返回null
     */
    suspend fun getUserId(): String? {
        val userInfo = getUserInfo()
        if (userInfo != null) {
            return userInfo.uidStr ?: userInfo.uid.toString()
        }
        
        // 如果没有用户信息，尝试获取游客ID
        if (getLoginType() == LoginType.GUEST) {
            return getGuestUserId()
        }
        
        return null
    }

    suspend fun saveModelInfo(url: String?, id: String?) {
        dataStore?.edit { preferences ->
            preferences[UserPreferencesKeys.MODEL_URL] = url ?: ""
            preferences[UserPreferencesKeys.MODEL_ID] = id ?: ""
        }
        cachedModelUrl = url
        cachedModelId = id
    }

    suspend fun getModelInfo(): Pair<String?, String?> {
        if (cachedModelUrl != null && cachedModelId != null) {
            return Pair(cachedModelUrl, cachedModelId)
        }

        val modelData = dataStore?.data?.map { preferences ->
            Pair(
                preferences[UserPreferencesKeys.MODEL_URL],
                preferences[UserPreferencesKeys.MODEL_ID]
            )
        }?.first() ?: Pair(null, null)

        cachedModelUrl = modelData.first
        cachedModelId = modelData.second
        return modelData
    }

    suspend fun clearModelInfo() {
        dataStore?.edit { preferences ->
            preferences.remove(UserPreferencesKeys.MODEL_URL)
            preferences.remove(UserPreferencesKeys.MODEL_ID)
        }
        cachedModelUrl = null
        cachedModelId = null
    }
}