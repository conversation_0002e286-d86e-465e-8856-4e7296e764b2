package one.srp.gensmo.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SearchInspoRes(
    val queryExtensionList: List<SearchInspoItem>,
    val inspoTitle: String? = null,
)

@Serializable
data class SearchInspoItem(
    val searchQuery: String? = null,
    val showQuery: String,
    val emoji: String? = null,
    val imageUrl: String? = null,
)

@Serializable
data class SearchVibeResponse(
    @SerialName("query_list")
    val queryList: List<String>,
)

@Serializable
data class QueryPolishingResponse(
    val text: String,
    val budget: String,
    val style: List<String>,
)

@Serializable
@JvmInline
value class SugResponse(val list: List<String> = emptyList())

@Serializable
data class SearchV2Request(
    val query: String,
    val imageUrl: String? = null,
    @SerialName("use_online_image_seg")
    val useOnlineImageSeg: Boolean = false,
    @SerialName("is_async")
    val isAsync: Boolean,
    @SerialName("inspo_label")
    val inspoLabel: List<String>? = null,
    val budget: String = "0",
    @SerialName("specified_product")
    val specifiedProduct: ProductItem? = null,
)

// NOTE: web socket 中要求全部参数必传，单独写类型，后续需要统一
@Serializable
data class SearchParams(
    val debugLevel: Int,
    val query: String,
    val budget: String,
    val isAsync: Boolean,
    val route: String,
    val isPresetQuery: Boolean,
    val moodboardVersion: String,
    val imageUrl: String,

    // NOTE: web socket only
    val messageId: String? = null,
    val useOnlineImageSeg: Boolean = false,
    val inspoLabel: List<String>? = null,
    val specifiedProduct: ProductItem? = null,
)