package one.srp.gensmo.data.remote.utils

import one.srp.gensmo.utils.env.EnvConf

object HttpClients {
    val default by lazy {
        createOkHttpClient("restful", 60)
    }

    val metric by lazy {
        createOkHttpClient("metric", 60)
    }

    val websocket by lazy {
        createOkHttpClient("websocket", 0)
    }
}

object RemoteClients {
    val workflow by lazy {
        createRetrofitInstance(
            baseUrl = EnvConf.baseUrl.workflow,
            client = HttpClients.default
        )
    }

    val account by lazy {
        createRetrofitInstance(
            baseUrl = EnvConf.baseUrl.account,
            client = HttpClients.default
        )
    }

    val recommend by lazy {
        createRetrofitInstance(
            baseUrl = EnvConf.baseUrl.recommend,
            client = HttpClients.default
        )
    }

    val metric by lazy {
        createRetrofitInstance(
            baseUrl = EnvConf.baseUrl.metric,
            client = HttpClients.metric,
            type = "metric"
        )
    }
}
