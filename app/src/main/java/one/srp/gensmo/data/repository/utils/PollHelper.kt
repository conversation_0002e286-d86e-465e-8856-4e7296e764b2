package one.srp.gensmo.data.repository.utils

import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.isActive
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds

fun <T> poll(
    interval: Duration = 1000.milliseconds,
    initialDelay: Duration = Duration.ZERO,
    maxAttempts: Int = 30,
    retryOnError: Boolean = true,
    shouldContinue: suspend (Result<T>) -> Boolean = { true },
    block: suspend () -> T,
): Flow<PollState<T>> = flow {
    var attempts = 0

    if (initialDelay > Duration.ZERO) {
        delay(initialDelay)
    }

    while (attempts < maxAttempts && currentCoroutineContext().isActive) {
        val result = runCatching { block() }.onFailure { e ->
            emit(PollState.Error(e))
            if (!retryOnError) {
                throw e
            }
        }

        if (!shouldContinue(result)) {
            emit(PollState.Success(result.getOrThrow()))
            return@flow
        } else {
            emit(PollState.Polling(result.getOrThrow()))
        }

        if (attempts < maxAttempts - 1) {
            delay(interval)
        }
        attempts++
    }

    emit(PollState.Timeout())
}.cancellable()
