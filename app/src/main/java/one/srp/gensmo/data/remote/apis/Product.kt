package one.srp.gensmo.data.remote.apis

import okhttp3.MultipartBody
import one.srp.gensmo.data.model.ProductItem
import one.srp.gensmo.data.model.TryOnTaskTryOnItems
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface ProductApi {
    @Multipart
    @POST("/user_upload_product")
    suspend fun uploadCustomProduct(
        @Query("product_list_id") productListId: String,
        @Part files: MultipartBody.Part,
    ): Response<ProductItem>

    @GET("/product/{product_id}")
    suspend fun getProduct(
        @Path("product_id") productId: String,
    ): Response<ProductItem>

    @GET("/moodboard/tryon_product/{moodboard_id}")
    suspend fun getTryOnProduct(
        @Path("moodboard_id") moodboardId: String,
    ): Response<TryOnTaskTryOnItems>

    @GET("/moodboard/tryon_product_with_tag/{moodboard_id}/{tag}")
    suspend fun getTryOnProductWithTag(
        @Path("moodboard_id") moodboardId: String,
        @Path("tag") tag: String,
    ): Response<List<ProductItem>>
}
