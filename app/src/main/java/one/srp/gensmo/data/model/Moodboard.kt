package one.srp.gensmo.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class MoodboardContentBlockPosition(
    val x: Float? = null,
    val y: Float? = null,
    val z: Float? = null,
)

@Serializable
data class MoodboardContentBlockSize(
    val width: Float? = null,
    val height: Float? = null,
)

@Serializable
data class MoodboardContentBlockPadding(
    val top: Float? = null,
    val right: Float? = null,
    val bottom: Float? = null,
    val left: Float? = null,
)

@Serializable
data class MoodboardContentBlockShadow(
    val color: String? = null,
    val offsetX: Float? = null,
    val offsetY: Float? = null,
    val blur: Float? = null,
    val spread: Float? = null,
)

@Serializable
data class MoodboardContentBlockStyle(
    val padding: MoodboardContentBlockPadding? = null,
    val textAlign: String? = null,
    val fontFamily: String? = null,
    val fontSize: Float? = null,
    val fontWeight: Float? = null,
    val color: String? = null,
    val backgroundColor: String? = null,
    val rotate: Float? = null,
    val shadow: MoodboardContentBlockShadow? = null,
    val display: String? = null,
)


@Serializable
data class MoodboardContentBlockContent(
    val type: String,
    val content: String,
    val link: String? = null,
    val field: String? = null,
    val style: MoodboardContentBlockStyle? = null,
)

@Serializable
data class MoodboardContentBlock(
    val position: MoodboardContentBlockPosition,
    val size: MoodboardContentBlockSize,
    val content: MoodboardContentBlockContent,
    val id: String? = null,
    val globalId: String? = null,
)

@Serializable
data class MoodboardContent(
    val width: Float,
    val height: Float? = null,
    val background: String? = null,
    val blocks: List<MoodboardContentBlock>,
)

@Serializable
data class MoodboardEntity(
    val id: String,
    val content: String,
    var parsedContent: MoodboardContent? = null,
    val products: List<ProductItem>,
    val isTryOn: Boolean? = false,
    val allTryOnItems: MoodboardTryOnItems? = null,
    var isFavorited: Boolean? = false,
)

@Serializable
data class MoodboardTryOnItems(
    val box1: List<MoodboardTryOnBoxItem<MoodboardTryOnGarmentItem>>? = null,
    val box2: List<MoodboardTryOnBoxItem<MoodboardTryOnGarmentItem>>? = null,
)

@Serializable
data class MoodboardTryOnBoxItem<T>(
    val top: List<T>? = null,
    val bottom: List<T>? = null,
    @SerialName("full-body")
    val fullBody: List<T>? = null,
)

@Serializable
data class MoodboardTryOnGarmentItem(
    val imageUrl: String? = null,
    val garmentType: String? = null,
    val itemType: String? = null,
    val itemId: String? = null,
    val mainImage: MainImage? = null,

    // NOTE: temp compat
    val globalId: String? = null,
)

@Serializable
data class MoodboardItem(
    val moodboardId: String,
    val moodboards: MoodboardEntity? = null,
    val query: String? = null,
    val reasoning: String? = null,
    val image: String? = null,
    val imageUrl: String? = null,
    val remix: Int? = null,
    val createdUserId: String? = null,

    val userAvatar: String? = null,
    val username: String? = null,

    val taskId: String? = null,
    val searchProductList: Map<String, List<ProductItem>>? = null,
    val isTryOn: Boolean? = false,
)

@Serializable
data class SearchItem(
    val taskId: String,
    val moodboards: List<MoodboardEntity>? = null,
    val query: String? = null,
    val reasoning: String? = null,
    val searchProductList: Map<String, List<ProductItem>>? = null,
    val image: String? = null,
    val imageUrl: String? = null,
    val imageDescription: String? = null,
    val status: String? = null,
    val displayText: String? = null,
    val errorMessage: String? = null,
    val imageWidth: Float? = null,
    val imageHeight: Float? = null,
    val useUserPreference: Boolean? = null,
    val inFeedWhiteList: Boolean? = null,
    val userImageTag: String? = null,
)

@Serializable
data class PostSearchItem(
    val taskId: String,
    val moodboards: MoodboardEntity? = null,
    val query: String? = null,
    val reasoning: String? = null,
    val searchProductList: Map<String, List<ProductItem>>? = null,
    val image: String? = null,
    val imageUrl: String? = null,
    val imageDescription: String? = null,
    val status: String? = null,
    val displayText: String? = null,
    val errorMessage: String? = null,
    val imageWidth: Float? = null,
    val imageHeight: Float? = null,
    val useUserPreference: Boolean? = null,
    val inFeedWhiteList: Boolean? = null,
    val userImageTag: String? = null,
    val isTryOn: Boolean? = false,
    val moodboardId: String? = null,
)