package one.srp.gensmo.data.store

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.util.UUID
import timber.log.Timber

// 创建 DataStore 实例
private val Context.deviceDataStore: DataStore<Preferences> by preferencesDataStore(name = "device_data")

// 定义数据键
private object DevicePreferencesKeys {
    val DEVICE_ID = stringPreferencesKey("device_id")
}

// 创建设备ID管理器单例
object DeviceDataStoreManager {
    private var cachedDeviceId: String? = null
    private var dataStore: DataStore<Preferences>? = null

    suspend fun initialize(context: Context) {
        dataStore = context.deviceDataStore
        
        // 预先加载设备ID到缓存（如果存在的话）
        try {
            cachedDeviceId = dataStore?.data?.map { preferences ->
                preferences[DevicePreferencesKeys.DEVICE_ID]
            }?.first()
        } catch (e: Exception) {
            // 预加载失败不影响后续使用，getDeviceId()会重新尝试
            Timber.w(e, "预加载设备ID失败，将在首次调用时重新获取")
        }
    }

    private suspend fun saveDeviceId(deviceId: String) {
        dataStore?.edit { preferences ->
            preferences[DevicePreferencesKeys.DEVICE_ID] = deviceId
        }
        cachedDeviceId = deviceId
    }

    suspend fun getDeviceId(): String {
        // 如果有缓存值，直接返回
        if (cachedDeviceId != null) {
            return cachedDeviceId!!
        }

        // 从 DataStore 获取设备ID
        val existingDeviceId = dataStore?.data?.map { preferences ->
            preferences[DevicePreferencesKeys.DEVICE_ID]
        }?.first()

        if (existingDeviceId != null) {
            // 如果存在设备ID，缓存并返回
            cachedDeviceId = existingDeviceId
            return existingDeviceId
        } else {
            // 如果不存在，生成新的设备ID并保存
            val newDeviceId = generateNewDeviceId()
            saveDeviceId(newDeviceId)
            return newDeviceId
        }
    }

    private fun generateNewDeviceId(): String {
        return UUID.randomUUID().toString()
    }

}
