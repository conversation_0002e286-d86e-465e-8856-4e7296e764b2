package one.srp.gensmo.data.repository

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import one.srp.gensmo.data.model.TryOnHistoryItem
import one.srp.gensmo.data.model.GenerateReplicaHistoryItem
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.data.model.GenerateReplicaHistoryParams
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TryOnRepository @Inject constructor() {
    private val _tryOnHistory = MutableStateFlow<List<TryOnHistoryItem>>(emptyList())
    val tryOnHistory: StateFlow<List<TryOnHistoryItem>> = _tryOnHistory.asStateFlow()
    
    private val _replicaHistory = MutableStateFlow<List<GenerateReplicaHistoryItem>>(emptyList())
    val replicaHistory: StateFlow<List<GenerateReplicaHistoryItem>> = _replicaHistory.asStateFlow()
    
    private val _hasMoreReplicaHistory = MutableStateFlow(false)
    val hasMoreReplicaHistory: StateFlow<Boolean> = _hasMoreReplicaHistory.asStateFlow()
    
    private var _lastReplicaHistoryId: String? = null
    
    private var currentOffset = 0
    private val pageSize = 20
    private var hasMore = true
    
    suspend fun loadTryOnHistory(isRefresh: Boolean = false) {
        if (isRefresh) {
            currentOffset = 0
            hasMore = true
            _tryOnHistory.value = emptyList()
        }

        if (!hasMore) return

        try {
            val response = TryOnService.api.getTryOnHistory(
                offset = currentOffset,
                limit = pageSize
            )
            if (response.isSuccessful) {
                val body = response.body()!!
                Timber.d("获取试衣历史记录成功，数量：${body.list.size}")
                if (body.list.isEmpty()) {
                    hasMore = false
                } else {
                    _tryOnHistory.value = if (currentOffset == 0) {
                        body.list
                    } else {
                        _tryOnHistory.value + body.list
                    }
                    currentOffset += body.list.size
                }
            } else {
                Timber.e("获取试衣历史记录失败: ${response.message()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "获取试衣历史记录失败")
        }
    }

    suspend fun deleteTryOnHistory(tryOnTaskId: String) {
        val response = TryOnService.api.deleteTryOnHistory(tryOnTaskId)
        if (response.isSuccessful) {
            Timber.d("删除试衣历史记录成功")
            loadTryOnHistory(true)
        } else {
            Timber.e("删除试衣历史记录失败: ${response.message()}")
        }
    }
    
    suspend fun loadReplicaHistory(limit: Int = 10, lastId: String? = null) {
        try {
            val response = TryOnService.api.generateReplicaHistory(
                GenerateReplicaHistoryParams(limit = limit, lastId = lastId)
            )
            if (response.isSuccessful) {
                val body = response.body()!!
                if (body.errno == 0) {
                    Timber.d("获取生成历史记录成功，数量：${body.data.size}")
                    _replicaHistory.value = if (lastId == null) body.data else _replicaHistory.value + body.data
                    _hasMoreReplicaHistory.value = body.hasMore
                    Timber.d("hasMoreReplicaHistory: ${body.hasMore}")
                    _lastReplicaHistoryId = body.lastId
                } else {
                    Timber.e("获取生成历史记录失败: ${body.errmsg}")
                }
            } else {
                Timber.e("获取生成历史记录失败: ${response.message()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "获取生成历史记录失败")
        }
    }
    
    fun getLastReplicaHistoryId(): String? = _lastReplicaHistoryId

    fun removeModelFromHistory(modelId: String) {
        val updatedHistory = _replicaHistory.value.map { item ->
            item.copy(
                replicaList = item.replicaList.filter { it.modelId != modelId }
            )
        }.filter { it.replicaList.isNotEmpty() }
        _replicaHistory.value = updatedHistory
    }
} 