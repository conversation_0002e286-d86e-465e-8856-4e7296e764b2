package one.srp.gensmo.data.repository.utils

sealed interface BaseState<out T> {
    data object Loading : BaseState<Nothing>
    data class Success<T>(val data: T) : BaseState<T>
    data class Error(val error: Throwable? = null) : BaseState<Nothing>
}

sealed interface PollState<out T> {
    data object Idle : PollState<Nothing>
    data class Polling<T>(val data: T) : PollState<T>
    data class Success<T>(val data: T) : PollState<T>
    data class Error(val error: Throwable? = null) : PollState<Nothing>
    data class Timeout(val error: Throwable? = null) : PollState<Nothing>
}
