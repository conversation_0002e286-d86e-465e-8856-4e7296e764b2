package one.srp.gensmo.data.model

import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonClassDiscriminator
import kotlinx.serialization.json.JsonContentPolymorphicSerializer
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonNamingStrategy
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.modules.SerializersModule

@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonClassDiscriminator("type")
sealed class ChatMessage {
    abstract val sessionId: String
    abstract val messageId: String
    abstract val role: String
    abstract val visible: Boolean
    abstract fun alterId(id: String): ChatMessage
}

object ChatMessageSerializer : JsonContentPolymorphicSerializer<ChatMessage>(ChatMessage::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<ChatMessage> {
        val json = element.jsonObject
        return when (json["type"]?.jsonPrimitive?.content) {
            "session_start" -> ContainerMessage.serializer()
            "search_query" -> SearchQueryMessage.serializer()
            "search_loading" -> SearchLoadingMessage.serializer()
            "search_res" -> SearchResMessage.serializer()
            "tryon_query" -> TryOnQueryMessage.serializer()
            "tryon_loading" -> TryOnLoadingMessage.serializer()
            "tryon_res" -> TryOnResMessage.serializer()
            else -> {
                UnknownMessage.serializer()
            }
        }
    }
}

@OptIn(ExperimentalSerializationApi::class)
val ChatJson = Json {
    ignoreUnknownKeys = true
    namingStrategy = JsonNamingStrategy.SnakeCase
    coerceInputValues = true
    isLenient = true
    serializersModule = SerializersModule {
        polymorphicDefaultDeserializer(ChatMessage::class) { ChatMessageSerializer }
    }
}

enum class ChatMessageRole(val value: String) {
    Root("root"), User("user"), Assistant("assistant"), System("system"),
}

@Serializable
@SerialName("unknown")
data class UnknownMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val type: String,
    val value: JsonElement,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
@SerialName("unexpect_error")
data class UnexpectErrorMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: UnexpectErrorMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class UnexpectErrorMessageWrapper(
    val unexpectError: UnexpectErrorBody?,
)

@Serializable
data class UnexpectErrorBody(
    val errorInfo: String?,
    val messageId: String?,
)

@Serializable
@SerialName("session_start")
data class ContainerMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: String? = null,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
@SerialName("search_query")
data class SearchQueryMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: SearchQueryMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class SearchQueryMessageWrapper(
    val searchQuery: SearchParams,
)

@Serializable
@SerialName("search_loading")
data class SearchLoadingMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: SearchLoadingMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class SearchLoadingMessageWrapper(
    val searchLoading: SearchItem,
)

@Serializable
@SerialName("search_res")
data class SearchResMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: SearchResMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class SearchResMessageWrapper(
    val searchQuery: SearchParams,
    val searchRes: SearchItem,
    val inspo: SearchInspoRes,
)

@Serializable
@SerialName("tryon_query")
data class TryOnQueryMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: TryOnQueryMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class TryOnQueryMessageWrapper(
    val tryonQuery: TryOnParams,
)

@Serializable
@SerialName("tryon_loading")
data class TryOnLoadingMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: TryOnLoadingMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class TryOnLoadingMessageWrapper(
    val tryonLoading: TryOnTaskItem,
)

@Serializable
@SerialName("tryon_res")
data class TryOnResMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: TryOnResMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class TryOnResMessageWrapper(
    val tryonQuery: TryOnParams? = null,
    val tryonRes: TryOnTaskItem,
)

@Serializable
@SerialName("tryon_changebg_query")
data class TryOnChangebgQueryMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: TryOnChangebgQueryMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class TryOnChangebgQueryMessageWrapper(
    val tryonChangebgQuery: TryOnBackgroundParams,
)

@Serializable
@SerialName("tryon_changebg_loading")
data class TryOnChangebgLoadingMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: TryOnChangebgLoadingMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class TryOnChangebgLoadingMessageWrapper(
    val tryonChangebgLoading: TryOnBackgroundRes,
    val tryonRes: TryOnTaskItem? = null,
)

@Serializable
@SerialName("tryon_changebg_res")
data class TryOnChangebgResMessage(
    override val sessionId: String,
    override val messageId: String,
    override val role: String,
    override val visible: Boolean,
    val value: TryOnChangebgResMessageWrapper,
) : ChatMessage() {
    override fun alterId(id: String): ChatMessage {
        return this.copy(sessionId = id)
    }
}

@Serializable
data class TryOnChangebgResMessageWrapper(
    val tryonChangebgQuery: TryOnBackgroundParams,
    val tryonChangebgRes: TryOnBackgroundRes,
    val tryonRes: TryOnTaskItem,
)

// Session History

@Serializable
data class SessionHistoryItem(
    val sessionId: String,
    val userUid: String,
    val createdAt: Double,
    val lastUpdated: Double,
    val totalMessages: Int,
    val sessionTitle: String? = null,
)

@Serializable
data class SessionHistoryRes(
    val sessions: List<SessionHistoryItem> = emptyList(),
    val totalCount: Int = 0,
    val limit: Int = 0,
    val skip: Int = 0,
)

@Serializable
data class SessionItemRes(
    val messages: List<ChatMessage> = emptyList(),
    val totalCount: Int = 0,
    val limit: Int = 0,
    val skip: Int = 0,
)