package one.srp.gensmo.data.remote.apis

import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST

interface MetricApi {
    @Headers(
        "CF-Access-Client-Id: c724496c4808635e45a222714408a577.access",
        "CF-Access-Client-Secret: 92fe597049379e86c2c630a82a4e91cd18c9e8a5a22718beb206fa50fb9b01c2",
        "content-type: application/json",
    )
    @POST("/gensmo_android.log")
    suspend fun postApiMetric(
        @Body eventData: RequestBody,
    ): Response<Unit>
}
