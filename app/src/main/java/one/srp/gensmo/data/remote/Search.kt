package one.srp.gensmo.data.remote

import one.srp.gensmo.data.model.QueryPolishingResponse
import one.srp.gensmo.data.model.SearchVibeResponse
import one.srp.gensmo.data.model.SugResponse
import one.srp.gensmo.data.remote.utils.RemoteClients
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query


interface SearchVibeServiceApi {

    @POST("/prepping_my_vibe")
    suspend fun preppingMyVibe(
        @Query("request_type") requestType: String = "text",
    ): Response<SearchVibeResponse>

    @GET("/imagination_query")
    suspend fun imaginationQuery(
    ): Response<SugResponse>

    @GET("/query_polishing/v2")
    suspend fun queryPolishing(
        @Query("query") query: String
    ): Response<QueryPolishingResponse>
}

object SearchVibeService {
    val api: SearchVibeServiceApi by lazy {
        RemoteClients.workflow.create(SearchVibeServiceApi::class.java)
    }
}
