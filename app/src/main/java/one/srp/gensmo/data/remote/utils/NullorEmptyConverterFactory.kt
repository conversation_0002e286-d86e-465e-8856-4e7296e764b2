package one.srp.gensmo.data.remote.utils

import okhttp3.ResponseBody
import okhttp3.ResponseBody.Companion.toResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type

class NullOnEmptyConverterFactory : Converter.Factory() {
    override fun responseBodyConverter(
        type: Type,
        annotations: Array<out Annotation>,
        retrofit: Retrofit
    ): Converter<ResponseBody, *> {
        val delegate = retrofit.nextResponseBodyConverter<Any>(this, type, annotations)
        return Converter<ResponseBody, Any> { body ->
            if (body.contentLength() == 0L) {
                null
            } else {
                val bodyString = body.string()
                if (bodyString == "null") {
                    null
                } else {
                    val newBody = bodyString.toResponseBody(body.contentType())
                    delegate.convert(newBody)
                }
            }
        }
    }
}