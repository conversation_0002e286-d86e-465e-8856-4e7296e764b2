package one.srp.gensmo.data.model

import kotlinx.serialization.Serializable

@Serializable
data class HomePageInfo(
    val topImage: String? = null,
    val bottleImage: String? = null,
    val greetings: String? = null,
    val loginPosition: String? = null,
    val eventStartTime: Long? = null,
    val eventEndTime: Long? = null,
    val errorCode: Int? = null,
    val topFeature: List<HomePageInfoTopFeature>? = null,
    val isChangeBackground: Boolean? = null,
    val chooseAStyle: List<HomePageInfoStyle>? = null,
    val imageCompress: Int? = null,
    val imageQuality: HomePageInfoImageQuality? = null,
    val miniActiveEntrance: List<HomePageInfoActivity>? = null,
)

@Serializable
data class HomePageInfoTopFeature(
    val icon: String? = null,
    val fixedIcon: String? = null,
    val title: String? = null,
    val fixedTitle: String? = null,
    val router: String? = null
)

@Serializable
data class HomePageInfoStyle(
    val title: String? = null,
    val image: String? = null
)

@Serializable
data class HomePageInfoImageQuality(
    val maxFileSize: Int? = null,
    val shortSideLength: Int? = null
)

@Serializable
data class HomePageInfoActivity(
    val iconLink: String? = null,
    val entranceLink: String? = null
)
