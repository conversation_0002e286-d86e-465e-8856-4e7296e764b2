package one.srp.gensmo.data.remote

import kotlinx.serialization.Serializable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import one.srp.gensmo.data.remote.utils.RemoteClients
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import java.io.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@Serializable
data class PresignedUrlResponse(
    val presignedUrl: String,
    val publicUrl: String
)

@Serializable
data class SegmentImg(
    val url: String,
)

typealias SegmentImgRes = Map<String, SegmentImg>


interface ImageServiceApi {
    @POST("/image/segment")
    suspend fun segmentImage(
        @Body body: List<String>,
    ): Response<SegmentImgRes>

    @GET("/image/presigned")
    suspend fun getPresignedUrl(
        @Query("purpose") purpose: String,
        @Query("width") width: Int,
        @Query("height") height: Int,
    ): Response<PresignedUrlResponse>
}

object ImageService {
    val api: ImageServiceApi by lazy {
        RemoteClients.workflow.create(ImageServiceApi::class.java)
    }
    
    // 使用预签名URL上传图片 - 修改为挂起函数
    suspend fun uploadWithPresignedUrl(presignedUrl: String, file: File): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 创建一个新的OkHttpClient实例
                val client = okhttp3.OkHttpClient()
                    
                val request = okhttp3.Request.Builder()
                    .url(presignedUrl)
                    .put(file.asRequestBody("image/*".toMediaTypeOrNull()))
                    .build()
                    
                val response = client.newCall(request).execute()
                response.isSuccessful
            } catch (e: Exception) {
                e.printStackTrace()
                false
            }
        }
    }
}
