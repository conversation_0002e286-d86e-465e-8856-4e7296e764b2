package one.srp.gensmo.analytics

import okhttp3.RequestBody
import one.srp.core.analytics.providers.CustomAnalyticsApiClient
import one.srp.gensmo.data.remote.MetricService
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of CustomAnalyticsApiClient using existing MetricService
 * This integrates the analytics module with the existing metrics infrastructure
 */
@Singleton
class MetricServiceAnalyticsApiClient @Inject constructor() : CustomAnalyticsApiClient {
    
    override suspend fun postAnalyticsEvent(requestBody: RequestBody) {
        try {
            // 直接使用现有的 MetricService API
            // 这里复用了现有的埋点上报逻辑，包括失败重试机制
            val response = MetricService.api.postApiMetric(requestBody)
            
            if (response.isSuccessful) {
                Timber.d("Analytics event posted successfully via MetricService")
            } else {
                Timber.w("MetricService API returned error: ${response.code()}")
                throw Exception("MetricService API error: ${response.code()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to post analytics event via MetricService")
            throw e
        }
    }
    
    override suspend fun postUserProperties(requestBody: RequestBody) {
        try {
            // 用户属性通过同样的 MetricService API 发送
            // 可以考虑添加特殊的事件类型标识来区分用户属性事件
            val response = MetricService.api.postApiMetric(requestBody)
            
            if (response.isSuccessful) {
                Timber.d("User properties posted successfully via MetricService")
            } else {
                Timber.w("MetricService API returned error for user properties: ${response.code()}")
                throw Exception("MetricService API error for user properties: ${response.code()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to post user properties via MetricService")
            throw e
        }
    }
    
    override suspend fun postUserId(requestBody: RequestBody) {
        try {
            // 用户ID通过同样的 MetricService API 发送
            // 可以考虑添加特殊的事件类型标识来区分用户ID事件
            val response = MetricService.api.postApiMetric(requestBody)
            
            if (response.isSuccessful) {
                Timber.d("User ID posted successfully via MetricService")
            } else {
                Timber.w("MetricService API returned error for user ID: ${response.code()}")
                throw Exception("MetricService API error for user ID: ${response.code()}")
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to post user ID via MetricService")
            throw e
        }
    }
}
