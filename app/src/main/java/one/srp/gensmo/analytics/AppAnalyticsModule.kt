package one.srp.gensmo.analytics

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import one.srp.core.analytics.providers.CustomAnalyticsApiClient
import javax.inject.Singleton

/**
 * Application-level Analytics Module
 * 
 * This module provides application-specific implementations for the analytics providers.
 * It overrides the default implementations from the core analytics module with
 * concrete implementations that integrate with the existing app infrastructure.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class AppAnalyticsModule {
    
    /**
     * Binds the MetricService implementation to the CustomAnalyticsApiClient interface
     * This replaces the default implementation with one that uses the existing MetricService
     */
    @Binds
    @Singleton
    abstract fun bindCustomAnalyticsApiClient(
        metricServiceApiClient: MetricServiceAnalyticsApiClient
    ): CustomAnalyticsApiClient
}
