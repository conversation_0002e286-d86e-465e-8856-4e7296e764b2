package one.srp.gensmo.utils.auth

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import androidx.credentials.ClearCredentialStateRequest
import androidx.credentials.Credential
import androidx.credentials.CredentialManager
import androidx.credentials.CustomCredential
import androidx.credentials.GetCredentialRequest
import androidx.credentials.exceptions.GetCredentialException
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential.Companion.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL
import com.google.firebase.Firebase
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthWebException
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.OAuthProvider
import com.google.firebase.auth.auth
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.tasks.await
import one.srp.gensmo.data.remote.AccountService
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.ui.screens.user.login.UserInfo
import one.srp.gensmo.utils.metrics.MetricData
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val auth: FirebaseAuth = Firebase.auth
    private val credentialManager = CredentialManager.create(context)

    fun getUserInfo(): UserInfo? {
        return auth.currentUser?.let { user ->
            UserInfo(
                uid = user.uid,
                displayName = user.displayName,
                email = user.email,
                phoneNumber = user.phoneNumber,
                photoUrl = user.photoUrl?.toString(),
                isEmailVerified = user.isEmailVerified,
                providerId = user.providerId
            )
        }
    }

    suspend fun startGoogleLogin(
        clientId: String,
        activity: Activity,
        onSuccess: () -> Unit,
        onError: (String) -> Unit,
        onLoading: () -> Unit
    ) {
        try {
            onLoading()
            
            // 使用 CredentialManager
            val googleIdOption = GetSignInWithGoogleOption.Builder(clientId).build()
            val request = GetCredentialRequest.Builder()
                .addCredentialOption(googleIdOption)
                .build()

            val result = credentialManager.getCredential(
                context = activity,
                request = request
            )
            
            handleGoogleSignIn(result.credential, onSuccess, onError)
        } catch (e: GetCredentialException) {
            Timber.d(e, "Google sign in failed, falling back to web sign in")
            try {
                startWebSignIn(activity, onSuccess, onError)
            } catch (e: Exception) {
                Timber.e(e, "Both Google and web sign in failed")
                onError("Sign in failed on both Google and web")
            }
        }
    }

    private suspend fun startWebSignIn(
        activity: Activity,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        val provider = OAuthProvider.newBuilder("google.com").apply {
            scopes = listOf("email", "profile")
        }.build()

        try {
            val authResult = auth.startActivityForSignInWithProvider(activity, provider).await()
            val realIdToken = authResult?.user?.getIdToken(false)?.result?.token
            if (realIdToken != null) {
                exchangeAuthToken(realIdToken, onSuccess, onError)
            } else {
                onError("Failed to get ID Token")
            }
        } catch (e: ActivityNotFoundException) {
            Timber.e(e, "Web sign in failed. Is a browser installed?")
            onError("Sign in failed. A web browser is required.")
        } catch (e: Exception) {
            Timber.e(e, "Web sign in failed")
            onError("Sign in failed: ${e.localizedMessage}")
        }
    }

    suspend fun startAppleLogin(
        activity: Activity,
        onSuccess: () -> Unit,
        onError: (String) -> Unit,
        onLoading: () -> Unit
    ) {
        try {
            onLoading()

            val provider = OAuthProvider.newBuilder("apple.com").apply {
                scopes = listOf("email", "name")
            }.build()

            val pendingAuthResult = auth.pendingAuthResult
            val authResult = if (pendingAuthResult != null) {
                pendingAuthResult.await()
            } else {
                try {
                    auth.startActivityForSignInWithProvider(activity, provider).await()
                } catch (e: Exception) {
                    if (e.message?.contains("phenotype") == true || 
                        e.message?.contains("com.google.android.gms") == true) {
                        onError("Google Play Services may be required")
                        return
                    }
                    throw e
                }
            }

            val realIdToken = authResult?.user?.getIdToken(false)?.result?.token
            if (realIdToken != null) {
                exchangeAuthToken(realIdToken, onSuccess, onError)
            } else {
                onError("Failed to get ID Token")
            }
        } catch (e: Exception) {
            when (e) {
                is FirebaseAuthWebException -> {
                    Timber.d("FirebaseAuthWebException: ${e.localizedMessage}")
                    onError("")
                }
                else -> {
                    Timber.e(e, "Apple sign in failed")
                    onError("Sign in failed")
                }
            }
        }
    }

    suspend fun signOut() {
        auth.signOut()
        
        try {
            val clearRequest = ClearCredentialStateRequest()
            credentialManager.clearCredentialState(clearRequest)
        } catch (e: Exception) {
            Timber.d(e, "清除凭证状态失败")
        }
        
        UserDataStoreManager.clearUserLogin()
    }

    private suspend fun handleGoogleSignIn(
        credential: Credential,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        if (credential is CustomCredential && credential.type == TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
            val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
            firebaseAuthWithGoogle(googleIdTokenCredential.idToken, onSuccess, onError)
        } else {
            onError("Unsupported credential type")
        }
    }

    private suspend fun firebaseAuthWithGoogle(
        idToken: String,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            val credential = GoogleAuthProvider.getCredential(idToken, null)
            val result = auth.signInWithCredential(credential).await()
            val realIdToken = result.user?.getIdToken(false)?.await()?.token
            if (realIdToken != null) {
                exchangeAuthToken(realIdToken, onSuccess, onError)
            } else {
                onError("Failed to get ID Token")
            }
        } catch (e: Exception) {
            onError("Login failed: ${e.message}")
        }
    }

    private suspend fun exchangeAuthToken(
        token: String,
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        try {
            val bearerToken = "Bearer $token"
            val response = AccountService.api.exchangeAuth(bearerToken)
            
            if (response.isSuccessful) {
                val tokenResponse = response.body()
                if (tokenResponse != null) {
                    UserDataStoreManager.saveUserLogin(tokenResponse.accessToken)
                    UserDataStoreManager.getLoggedInUserInfo()
                    MetricData.logEventAF("login")
                    onSuccess()
                } else {
                    onError("Authentication failed: response body is empty")
                }
            } else {
                onError("Authentication failed: ${response.code()}")
            }
        } catch (e: Exception) {
            onError("Authentication failed: ${e.localizedMessage}")
        }
    }
}
