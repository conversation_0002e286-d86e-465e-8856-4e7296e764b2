package one.srp.gensmo.utils.asyncTask

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import one.srp.gensmo.data.remote.TryOnService
import one.srp.gensmo.ui.components.notification.ToastManager
import one.srp.gensmo.ui.components.notification.ToastType
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AsyncTaskManager @Inject constructor(
) {
    private var replicaTaskId: String? = null
    private var pollingJob: Job? = null
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    data class Progress(val current: Int, val total: Int)

    private val _progress = MutableStateFlow(Progress(0, 0))
    val progress: StateFlow<Progress> = _progress.asStateFlow()

    private val _replicaTaskStatus = MutableStateFlow<String?>(null)
    val replicaTaskStatus: StateFlow<String?> = _replicaTaskStatus.asStateFlow()

    private val _isTaskLoading = MutableStateFlow(false)
    val isTaskLoading: StateFlow<Boolean> = _isTaskLoading.asStateFlow()

    private val _previewUrl = MutableStateFlow<String?>(null)
    val previewUrl: StateFlow<String?> = _previewUrl.asStateFlow()

    private var onTaskCompleted: (() -> Unit)? = null

    fun setReplicaTaskStatus(status: String) {
        _replicaTaskStatus.value = status
        _isTaskLoading.value = status != "completed" && status != "failure" && status != "failed"
    }

    fun setReplicaTaskId(taskId: String) {
        replicaTaskId = taskId
        Timber.d("Starting polling for task ID: $taskId")
        startPolling()
    }

    private fun updateProgress(curStep: Int, total: Int) {
        _progress.value = Progress(curStep, total)
    }

    fun setOnTaskCompleted(navigation: () -> Unit) {
        onTaskCompleted = navigation
    }

    private fun startPolling() {
        Timber.d("startPolling called")
        pollingJob?.cancel()
        resetReplicaTask()
        pollingJob = scope.launch {
            Timber.d("Polling job started")
            while (isActive) {
                val taskId = replicaTaskId ?: break
                try {
                    val response = TryOnService.api.getReplicaStatus(taskId)
                    Timber.d("Polling response: ${response.code()}")
                    if (response.isSuccessful) {
                        val status = response.body()?.status
                        Timber.d("Polling response: $status")
                        updateProgress(
                            response.body()?.curStep ?: 0,
                            response.body()?.totalSteps ?: 0
                        )

                        status?.let { currentStatus ->
                            when (currentStatus) {
                                "completed" -> {
                                    val result = response.body()
                                    val firstReplicaUrl = result?.replicaList?.firstOrNull()?.modelUrl
                                    if (!firstReplicaUrl.isNullOrEmpty()) {
                                        _previewUrl.value = firstReplicaUrl
                                        Timber.d("Preview URL set: $firstReplicaUrl")
                                    }

                                    Timber.d("Replica task completed: $result")
                                    ToastManager.show(
                                        text = "Your Avatar's now ready. Try it out to see how outfits could look on you.",
                                        type = ToastType.IMAGE,
                                        imageUrl = firstReplicaUrl ?: "",
                                        onClick = { onTaskCompleted?.invoke() }
                                    )
                                    setReplicaTaskStatus(currentStatus)
                                    clearReplicaTaskId()
                                    return@launch
                                }
                                "failure", "failed" -> {
                                    ToastManager.show(
                                        text = "Too popular! Your Avatar is temporarily on hold.",
                                        type = ToastType.ERROR
                                    )
                                    Timber.d("Replica task failed: $currentStatus")
                                    setReplicaTaskStatus(currentStatus)
                                    clearReplicaTaskId()
                                    return@launch
                                }
                                else -> {
                                    setReplicaTaskStatus(currentStatus)
                                    delay(2000)
                                }
                            }
                        }
                    } else {
                        clearReplicaTaskId()
                        break
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Polling error")
                    clearReplicaTaskId()
                    break
                }
            }
        }
    }

    fun stopPolling() {
        pollingJob?.cancel()
        pollingJob = null
    }

    fun clearReplicaTaskId() {
        stopPolling()
        replicaTaskId = null
    }

    fun resetReplicaTask() {
        _progress.value = Progress(0, 0)
        _replicaTaskStatus.value = null
        _isTaskLoading.value = false
        _previewUrl.value = null
    }
}
