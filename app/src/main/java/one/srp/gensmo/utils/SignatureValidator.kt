package one.srp.gensmo.utils

import android.content.Context
import android.content.pm.PackageManager
import one.srp.gensmo.BuildConfig
import timber.log.Timber
import java.security.MessageDigest

object SignatureValidator {

    // TODO: 请将此处的 SHA256 指纹替换为您自己的发布证书指纹。
    // 您可以使用以下命令获取您的指纹:
    // keytool -list -v -keystore <您的 keystore 路径> -alias <您的 alias>
    // 在输出中找到 "SHA256" 证书指纹，并将其复制到此处。
    // 例如: "FA:C6:17:45:DC:09:03:78:6F:44:89:42:36:D4:44:2C:94:18:CB:3B:55:53:F0:43:43:97:75:14:21:54:3D:33"
    // 为了增加安全性，建议您不要将指纹明文存储在代码中。
    // 您可以考虑将其存储在 build.gradle 的 BuildConfig 字段中，或者通过其他加密方式保护。
    private const val EXPECTED_SHA256_FINGERPRINT = BuildConfig.RELEASE_CERT_SHA256
    private const val EXPECTED_SHA256_DEV_FINGERPRINT = BuildConfig.RELEASE_CERT_SHA256_DEV

    fun validate(context: Context): Boolean {
        if (EXPECTED_SHA256_FINGERPRINT.isBlank()) {
            // 在 debug 构建或未配置指纹时，我们跳过校验
            Timber.w("Signature fingerprint is not configured. Skipping validation.")
            return true
        }

        try {
            val packageName = context.packageName
            val packageManager = context.packageManager

            val packageInfo = packageManager.getPackageInfo(
                packageName,
                PackageManager.GET_SIGNING_CERTIFICATES
            )

            val signatures = packageInfo.signingInfo?.apkContentsSigners

            if (signatures.isNullOrEmpty()) {
                Timber.e("No signatures found.")
                return false
            }

            val signature = signatures[0]
            val signatureBytes = signature.toByteArray()

            val md = MessageDigest.getInstance("SHA-256")
            val digest = md.digest(signatureBytes)
            val fingerprint = digest.joinToString(separator = ":") { "%02X".format(it) }

            Timber.d("App Signature Fingerprint: $fingerprint")

            val matchProd = fingerprint.equals(EXPECTED_SHA256_FINGERPRINT, ignoreCase = true)
            val matchDev = fingerprint.equals(EXPECTED_SHA256_DEV_FINGERPRINT, ignoreCase = true)

            return matchProd || matchDev

        } catch (e: Exception) {
            Timber.e(e, "Signature validation error")
            return false
        }
    }
} 