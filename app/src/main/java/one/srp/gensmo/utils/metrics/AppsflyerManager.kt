package one.srp.gensmo.utils.metrics

import android.content.Context
import android.net.Uri
import androidx.core.net.toUri
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import com.appsflyer.AppsFlyerLib
import com.appsflyer.deeplink.DeepLinkListener
import com.appsflyer.deeplink.DeepLinkResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import timber.log.Timber

// 添加深链接回调接口
interface DeepLinkCallback {
    fun onDeepLinkReceived(deepLinkUri: Uri?, originalLink: String? = null)
}

class AppsflyerManager(private val context: Context) {
    private val appsflyerKey = "f6qE4shTi3M42cM567769T"
    private var deepLinkCallback: DeepLinkCallback? = null

    private val Context.metricsAfDataStore by preferencesDataStore(name = "metrics_af_prefs")
    private val hasFirstOpenedKey = booleanPreferencesKey("hasFirstOpened")

    // 添加设置回调的方法
    fun setDeepLinkCallback(callback: DeepLinkCallback) {
        this.deepLinkCallback = callback
    }

    fun init(debug: Boolean = false) {
        val appsflyer = AppsFlyerLib.getInstance()

        appsflyer.init(appsflyerKey, null, context)
        if (debug) appsflyer.setDebugLog(true)
        
        val deepLinkListener = object : DeepLinkListener {
            override fun onDeepLinking(deepLinkResult: DeepLinkResult) {
                when {
                    deepLinkResult.status == DeepLinkResult.Status.FOUND -> {
                        println("deepLinkResult: $deepLinkResult")
                        val deepLink = deepLinkResult.deepLink
                        val deepLinkValue = deepLink.deepLinkValue
                        println("deepLinkValue: $deepLinkValue")
                        
                        // 从 clickEvent 中获取完整的链接信息
                        val clickEvent = deepLink.clickEvent
                        val originalLink = clickEvent?.optString("link") ?: ""
                        if (clickEvent != null) {
                            val host = clickEvent.optString("host")
                            val path = clickEvent.optString("path")
                            println("host: $host, path: $path")
                            
                            // 优先检查 host 和 path - App Links
                            if (host == "gensmo.com" && path.startsWith("/app-entry")) {
                                println("处理 gensmo.com/app-entry App Link")
                                try {
                                    // 使用 link 字段或构造完整的 URI
                                    val link = clickEvent.optString("link")
                                    val uri = if (link.isNotEmpty()) {
                                        link.toUri()
                                    } else {
                                        // fallback: 构造 URI
                                        Uri.Builder()
                                            .scheme("https")
                                            .authority(host)
                                            .path(path)
                                            .apply {
                                                // 添加其他查询参数
                                                clickEvent.keys().forEach { key ->
                                                    if (key !in listOf("host", "path", "scheme", "link", "is_deferred")) {
                                                        val value = clickEvent.optString(key)
                                                        if (value.isNotEmpty()) {
                                                            appendQueryParameter(key, value)
                                                        }
                                                    }
                                                }
                                            }
                                            .build()
                                    }
                                        handleGensmoAppEntry(uri)
                                } catch (e: Exception) {
                                    println("Failed to handle gensmo.com/app-entry: ${e.message}")
                                }
                            } else {
                                // 不是 App Link，继续处理营销链接
                                handleOtherDeepLinks(deepLinkValue, originalLink)
                            }
                        } else {
                            // clickEvent 为空，处理营销链接
                            handleOtherDeepLinks(deepLinkValue, originalLink)
                        }
                    }
                }
            }
        }
        
        appsflyer.subscribeForDeepLink(deepLinkListener)
        appsflyer.start(context)

        CoroutineScope(Dispatchers.IO).launch {
            if (!checkFirstOpen()) {
                logFirstOpen()
            }
        }
    }

    fun getAppsflyerId(): String? {
        return AppsFlyerLib.getInstance().getAppsFlyerUID(context)
    }

    private suspend fun checkFirstOpen(): Boolean {
        val prefs = context.metricsAfDataStore.data.map { preferences ->
            preferences[hasFirstOpenedKey] == true
        }
        return prefs.first()
    }

    private suspend fun logFirstOpen() {
        context.metricsAfDataStore.edit { preferences ->
            preferences[hasFirstOpenedKey] = true
        }

        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues["type"] = "first open"
        AppsFlyerLib.getInstance().logEvent(context, "first open", eventValues)
    }

    fun logEvent(eventName: String, eventValues: Map<String, Any>) {
        AppsFlyerLib.getInstance().logEvent(context, eventName, eventValues)
    }
    
    /**
     * 处理 gensmo.com/app-entry App Link
     */
    private fun handleGensmoAppEntry(uri: Uri) {
        try {
            Timber.d("AppsFlyer 处理 gensmo.com/app-entry: $uri")
            
            // 解析 query 参数
            val queryParams = mutableMapOf<String, String>()
            uri.queryParameterNames.forEach { paramName ->
                val paramValue = uri.getQueryParameter(paramName)
                queryParams[paramName] = paramValue ?: ""
            }
            
            Timber.d("App Entry Query 参数: $queryParams")
            
            // 记录 AppsFlyer 事件
            val eventValues = mutableMapOf<String, Any>()
            eventValues["source"] = "app_link"
            eventValues["host"] = uri.host ?: ""
            eventValues["path"] = uri.path ?: ""
            queryParams.forEach { (key, value) ->
                eventValues["param_$key"] = value
            }
            logEvent("app_entry_opened", eventValues)
            
            // 执行导航逻辑 - 根据需要可在此添加业务逻辑
            Timber.d("App Entry 处理完成，参数: $queryParams")
            
        } catch (e: Exception) {
            Timber.e(e, "处理 gensmo.com/app-entry 失败: $uri")
        }
    }
    
        /**
     * 处理其他深链接（营销链接等）
     */
    private fun handleOtherDeepLinks(deepLinkValue: String?, originalLink: String? = null) {
        if (deepLinkValue.isNullOrEmpty()) {
            println("deepLinkValue 为空，直接调用回调")
            println("原始链接: $originalLink")
            // 如果 deepLinkValue 为空，直接调用回调
            deepLinkCallback?.onDeepLinkReceived(null, originalLink)
            return
        }
        
        try {
            val uri = deepLinkValue.toUri()
            println("AppsFlyer 收到营销深链接: $uri")
            println("原始链接: $originalLink")
            deepLinkCallback?.onDeepLinkReceived(uri, originalLink)
        } catch (e: Exception) {
            Timber.e(e, "Failed to parse deep link: $deepLinkValue")
        }
    }
} 