package one.srp.gensmo.utils.bridge

import android.content.Context
import com.smallbuer.jsbridge.core.BridgeHandler
import com.smallbuer.jsbridge.core.Bridge
import com.smallbuer.jsbridge.core.CallBackFunction
import com.smallbuer.jsbridge.core.BridgeWebView
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import timber.log.Timber
import one.srp.gensmo.BuildConfig
import javax.inject.Inject
import javax.inject.Singleton
/**
 * JSBridge 管理器
 * 使用 lzyzsd/JsBridge 库实现 WebView 和原生代码之间的通信
 */
@Singleton
class JSBridgeManager @Inject constructor() {
    
    private var bridgeWebView: BridgeWebView? = null
    // 在构造函数中注册所有默认处理器
    init {
        // 逐个注册所有处理器
        Bridge.INSTANCE.apply {
            registerHandler("_defaultHandler", DefaultHandler())
            registerHandler("getDeviceId", DeviceIdHandler())
            registerHandler("getUserId", UserIdHandler())
            registerHandler("getUserToken", UserTokenHandler())
        }
        Timber.d("JSBridgeManager初始化时已注册所有默认处理器")
    }
    
    /**
     * 初始化 JSBridge
     * @param webView BridgeWebView 实例
     * @param onBridgeReady 当 Bridge 准备就绪时的回调
     */
    fun init(webView: BridgeWebView, onBridgeReady: () -> Unit) {
        this.bridgeWebView = webView
        
        // 设置通用的 WebView 配置
        setupWebViewConfig(webView)
        // Bridge 注册完成后回调
        onBridgeReady()
        
        Timber.d("JSBridge 初始化完成")
    }
    
    /**
     * 设置 WebView 通用配置
     */
    private fun setupWebViewConfig(webView: BridgeWebView) {
        webView.settings.apply {
            // 设置自定义 User-Agent
            userAgentString = "Mozilla/5.0 (Linux; Android ${android.os.Build.VERSION.RELEASE}) Gensmo/${BuildConfig.VERSION_NAME}"
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
       // bridgeWebView?.destroy()
        bridgeWebView = null
        
        Timber.d("JSBridge 资源已释放")
    }

    // 将所有Handler改为内部类
    private inner class DefaultHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            Timber.d("默认处理器收到数据: $data")
            function?.onCallBack("默认处理器响应")
        }
    }

    private inner class DeviceIdHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            MainScope().launch {
                try {
                    val deviceId = DeviceDataStoreManager.getDeviceId()
                    function?.onCallBack(deviceId)
                } catch (e: Exception) {
                    Timber.e(e, "获取设备ID失败")
                    function?.onCallBack("")
                }
            }
        }
    }

    private inner class UserIdHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            MainScope().launch {
                try {
                    val userId = UserDataStoreManager.getUserId() ?: ""
                    function?.onCallBack(userId)
                } catch (e: Exception) {
                    Timber.e(e, "获取用户ID失败")
                    function?.onCallBack("")
                }
            }
        }
    }

    private inner class UserTokenHandler : BridgeHandler() {
        override fun handler(context: Context?, data: String?, function: CallBackFunction?) {
            MainScope().launch {
                try {
                    val token = UserDataStoreManager.getToken() ?: ""
                    Timber.d("获取用户令牌: $token")
                    function?.onCallBack(token)
                } catch (e: Exception) {
                    Timber.e(e, "获取用户令牌失败")
                    function?.onCallBack("")
                }
            }
        }
    }

}
