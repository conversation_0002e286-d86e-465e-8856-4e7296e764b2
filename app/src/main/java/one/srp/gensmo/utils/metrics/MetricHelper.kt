package one.srp.gensmo.utils.metrics

import android.os.Bundle
import com.google.firebase.Firebase
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.analytics
import com.google.firebase.analytics.logEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import one.srp.core.analytics.events.MetricEventooo
import one.srp.gensmo.BuildConfig
import one.srp.gensmo.data.remote.MetricService
import one.srp.gensmo.utils.env.ENV
import one.srp.gensmo.utils.env.EnvConf
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger

// 添加全局变量来跟踪连续失败次数
private val consecutiveFailures = AtomicInteger(0)
private const val MAX_CONSECUTIVE_FAILURES = 3
private const val BACKOFF_DELAY_MS = 60000L // 1分钟

suspend fun sendMetric(event: MetricEventooo, instance: FirebaseAnalytics = Firebase.analytics) {
    if (BuildConfig.DEBUG) {
        Timber.d("[Metric]: $event")
    }

    sendApiMetric(event)

    instance.logEvent(event.eventName) {
        event.block(this)
    }
}

private fun Bundle.toJsonObject(): JSONObject {
    val jsonObject = JSONObject()

    keySet().forEach { key ->
        val value = get(key)
        when (value) {
            is Bundle -> {
                jsonObject.put(key, value.toJsonObject())
            }

            is Array<*> -> {
                val jsonArray = JSONArray()
                value.forEach { item ->
                    when (item) {
                        is Bundle -> jsonArray.put(item.toJsonObject())
                        null -> jsonArray.put(null)
                        else -> jsonArray.put(item.toString())
                    }
                }
                jsonObject.put(key, jsonArray)
            }

            is List<*> -> {
                val jsonArray = JSONArray()
                value.forEach { item ->
                    when (item) {
                        is Bundle -> jsonArray.put(item.toJsonObject())
                        null -> jsonArray.put(null)
                        else -> jsonArray.put(item.toString())
                    }
                }
                jsonObject.put(key, jsonArray)
            }

            null -> jsonObject.put(key, JSONObject.NULL)
            else -> jsonObject.put(key, value.toString())
        }
    }

    return jsonObject
}

private fun Bundle.toRequestBody(): RequestBody {
    val jsonObject = toJsonObject()
    val jsonString = jsonObject.toString()
    return jsonString.toRequestBody("application/json".toMediaTypeOrNull())
}

suspend fun sendApiMetric(event: MetricEventooo) {
    if (EnvConf.env != ENV.PRODUCTION) return

    // 如果连续失败次数过多，临时跳过发送
    if (consecutiveFailures.get() >= MAX_CONSECUTIVE_FAILURES) {
        Timber.d("Metric API 连续失败${consecutiveFailures.get()}次，暂时跳过发送")

        // 异步尝试重置计数器
        CoroutineScope(Dispatchers.IO).launch {
            delay(BACKOFF_DELAY_MS)
            if (consecutiveFailures.get() >= MAX_CONSECUTIVE_FAILURES) {
                Timber.d("尝试重置 Metric API 失败计数器")
                consecutiveFailures.set(0)
            }
        }
        return
    }

    try {
        val body = event.params().toRequestBody()
        MetricService.api.postApiMetric(body)

        // 成功发送后重置失败计数器
        consecutiveFailures.set(0)

    } catch (e: Exception) {
        // 增加失败计数
        val failureCount = consecutiveFailures.incrementAndGet()

        // 记录详细的网络错误信息
        when (e) {
            // ConnectException 和 NoRouteToHostException 是 SocketException 的子类
            is java.net.ConnectException -> {
                Timber.w("Metric API 连接失败($failureCount/$MAX_CONSECUTIVE_FAILURES): ${e.message}")
            }

            is java.net.NoRouteToHostException -> {
                Timber.w("Metric API 无路由($failureCount/$MAX_CONSECUTIVE_FAILURES): 网络路由不可达")
            }

            // SocketTimeoutException 是 InterruptedIOException 的子类，InterruptedIOException 是 IOException 的子类
            is java.net.SocketTimeoutException -> {
                Timber.w("Metric API 连接超时($failureCount/$MAX_CONSECUTIVE_FAILURES): ${e.message}")
            }

            // UnknownHostException 和 ProtocolException 是 IOException 的直接子类
            is java.net.UnknownHostException -> {
                Timber.w("Metric API 域名解析失败($failureCount/$MAX_CONSECUTIVE_FAILURES): ${e.message} - 可能是网络连接问题")
            }

            is java.net.ProtocolException -> {
                Timber.w("Metric API 协议错误($failureCount/$MAX_CONSECUTIVE_FAILURES): ${e.message}")
            }

            // SocketException (父类，在所有子类之后)
            is java.net.SocketException -> {
                // 专门处理网络连接中断的情况
                val message = e.message ?: "未知Socket错误"
                when {
                    message.contains("Software caused connection abort") -> {
                        Timber.w("Metric API 连接被中断($failureCount/$MAX_CONSECUTIVE_FAILURES): 网络连接意外中断")
                    }

                    message.contains("Connection reset") -> {
                        Timber.w("Metric API 连接被重置($failureCount/$MAX_CONSECUTIVE_FAILURES): 服务器重置连接")
                    }

                    message.contains("Broken pipe") -> {
                        Timber.w("Metric API 连接中断($failureCount/$MAX_CONSECUTIVE_FAILURES): 网络管道中断")
                    }

                    message.contains("Network is unreachable") -> {
                        Timber.w("Metric API 网络不可达($failureCount/$MAX_CONSECUTIVE_FAILURES): 网络连接不可用")
                    }

                    message.contains("Host is unreachable") -> {
                        Timber.w("Metric API 主机不可达($failureCount/$MAX_CONSECUTIVE_FAILURES): 无法连接到目标主机")
                    }

                    else -> {
                        Timber.w("Metric API Socket错误($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }
                }
            }

            // IOException (最顶层父类，在所有子类之后)
            is java.io.IOException -> {
                val message = e.message ?: "未知网络错误"
                when {
                    message.contains("ENETUNREACH") -> {
                        Timber.w("Metric API 网络不可达($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("ETIMEDOUT") -> {
                        Timber.w("Metric API 连接超时($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("ECONNABORTED") -> {
                        Timber.w("Metric API 连接被终止($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("ECONNRESET") -> {
                        Timber.w("Metric API 连接被重置($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("ECONNREFUSED") -> {
                        Timber.w("Metric API 连接被拒绝($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("EHOSTUNREACH") -> {
                        Timber.w("Metric API 主机不可达($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("No route to host") -> {
                        Timber.w("Metric API 无路由到主机($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    message.contains("failed to connect") -> {
                        Timber.w("Metric API 连接失败($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }

                    else -> {
                        Timber.w("Metric API 网络IO错误($failureCount/$MAX_CONSECUTIVE_FAILURES): $message")
                    }
                }
            }

            is retrofit2.HttpException -> {
                // HTTP错误（如404、500等）
                Timber.w("Metric API HTTP错误($failureCount/$MAX_CONSECUTIVE_FAILURES): ${e.code()} ${e.message()}")
            }

            else -> {
                Timber.e(
                    e,
                    "Metric API 未知错误($failureCount/$MAX_CONSECUTIVE_FAILURES): ${e.javaClass.simpleName}"
                )
            }
        }

        // 如果连续失败次数达到阈值，记录警告
        if (failureCount >= MAX_CONSECUTIVE_FAILURES) {
            Timber.w("Metric API 连续失败达到阈值，将暂停发送${BACKOFF_DELAY_MS / 1000}秒")
        }
    }
}

