package one.srp.gensmo.utils.mime

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Environment
import android.provider.MediaStore
import coil3.ImageLoader
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import coil3.request.allowHardware
import coil3.toBitmap
import coil3.toUri
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.data.remote.ImageService
import one.srp.gensmo.utils.integration.trackEventOnSentry
import timber.log.Timber
import java.io.File

fun downloadBitmapImage(
    context: Context,
    bitmap: Bitmap,
    fileName: String = "image_${System.currentTimeMillis()}.png",
): Boolean {
    val resolver: ContentResolver = context.contentResolver

    val contentValues = ContentValues().apply {
        put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)
        put(MediaStore.MediaColumns.MIME_TYPE, "image/png")
        put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
        put(MediaStore.MediaColumns.IS_PENDING, 1)
    }

    val imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
    imageUri?.let {
        try {
            resolver.openOutputStream(it)?.use { outputStream ->
                if (!bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)) {
                    return false
                } else {
                    contentValues.clear()
                    contentValues.put(MediaStore.MediaColumns.IS_PENDING, 0)
                    resolver.update(it, contentValues, null, null)

                    return true
                }
            } ?: run {
                return false
            }
        } catch (e: Exception) {
            resolver.delete(it, null, null)
            Timber.e(e)
            return false
        }
    } ?: run {
        return false
    }
}

suspend fun downloadUrlImage(
    context: Context,
    url: String?,
    fileName: String = "image_${System.currentTimeMillis()}.png",
): Boolean {
    if (url == null) return false

    val bitmap = urlToBitmap(context, url)
    bitmap?.let {
        return downloadBitmapImage(context, bitmap, fileName)
    } ?: run {
        return false
    }
}

suspend fun urlToBitmap(
    context: Context,
    url: String,
): Bitmap? {
    val loader = ImageLoader(context)
    val request = ImageRequest.Builder(context).data(url).allowHardware(false).build()
    val result = loader.execute(request)
    if (result is SuccessResult) {
        val bitmap = result.image.toBitmap()
        return bitmap
    } else {
        return null
    }
}

/**
 * 修改图片URL以添加宽度参数
 * 仅支持一级域名为yesy.online或iminsp.com的URL
 * 如果URL已包含宽度参数，则替换为新的宽度
 * @param imageUrl 原始图片URL
 * @param width 期望设置的图片宽度
 * @return 修改后的URL，如果域名不支持则返回原始URL
 */
fun resizeImage(imageUrl: String, width: Int): String {
    if (imageUrl.isBlank()) {
        return imageUrl
    }

    if (width <= 0) {
        return imageUrl
    }

    // 检查URL是否属于支持的域名
    val supportedDomains = listOf("yesy.online", "iminsp.com")
    val isSupportedDomain = supportedDomains.any { domain ->
        imageUrl.contains(domain)
    }

    if (!isSupportedDomain) {
        return imageUrl // 不支持的域名返回原始URL
    }

    // 检查URL是否已包含宽度参数
    val widthPattern = "cdn-cgi/image/width=\\d+"
    val regex = Regex(widthPattern)

    return if (regex.containsMatchIn(imageUrl)) {
        // 替换现有的宽度参数
        imageUrl.replace(regex, "cdn-cgi/image/width=$width")
    } else {
        // 添加新的宽度参数
        try {
            // 分割URL以提取协议、域名和路径
            val urlParts = imageUrl.split("://", limit = 2)
            if (urlParts.size < 2) return imageUrl

            val protocol = urlParts[0]
            val remaining = urlParts[1]

            // 分割域名和路径
            val parts = remaining.split("/", limit = 2)
            val domain = parts[0]
            val path = if (parts.size > 1) parts[1] else ""

            // 构建新的URL
            "$protocol://$domain/cdn-cgi/image/width=$width/$path"
        } catch (e: Exception) {
            Timber.e(e, "处理URL时出错: $imageUrl")
            imageUrl // 处理失败时返回原始URL
        }
    }
}

/**
 * 上传本地或网络图片，返回可用的图片 URL
 *
 * @param imageUri 原始图片 URI 字符串
 * @param onState 更新 TaskStatus 的函数
 * @return 上传后或原始的 URL，失败时返回 null
 */
suspend fun tranImageUriToUrl(
    imageUri: String?,
    onState: (TaskStatus) -> Unit = {},
): String? {
    if (imageUri.isNullOrEmpty()) return null

    // 已经是网络 URL，直接返回
    if (imageUri.startsWith("http://") || imageUri.startsWith("https://")) {
        return imageUri
    }

    val file = run {
        val uri = imageUri.toUri()
        if (uri.scheme == "file") File(uri.path!!) else File(imageUri)
    }

    return try {
        val startTime = System.currentTimeMillis()
        trackEventOnSentry("network_request", mapOf("path" to "/upload_image"))

        // 读取图片宽高
        val opts = BitmapFactory.Options().apply { inJustDecodeBounds = true }
        BitmapFactory.decodeFile(file.absolutePath, opts)
        val (width, height) = opts.outWidth to opts.outHeight

        // 获取预签名 URL
        val presignedResp = ImageService.api.getPresignedUrl(
            purpose = "user_upload", width = width, height = height
        )
        if (!presignedResp.isSuccessful) {
            Timber.e("获取预签名URL失败: ${presignedResp.errorBody()?.string()}")
            onState(TaskStatus.Fail)
            trackEventOnSentry(
                "network_failure",
                mapOf("path" to "/upload_image", "error" to "Presign failed")
            )
            return null
        }
        val presignedData = presignedResp.body() ?: run {
            Timber.e("预签名数据为空")
            onState(TaskStatus.Fail)
            trackEventOnSentry(
                "network_failure",
                mapOf("path" to "/upload_image", "error" to "Empty presign")
            )
            return null
        }

        // 上传
        val ok = ImageService.uploadWithPresignedUrl(presignedData.presignedUrl, file)
        if (!ok) {
            Timber.e("使用预签名URL上传图片失败")
            onState(TaskStatus.Fail)
            trackEventOnSentry(
                "network_failure",
                mapOf("path" to "/upload_image", "error" to "Upload failed")
            )
            return null
        }

        // 成功
        val duration = (System.currentTimeMillis() - startTime) / 1000.0
        Timber.d("图片上传统计 - duration: ${duration}s")
        trackEventOnSentry(
            "network_timing",
            mapOf("path" to "/upload_image", "custom_duration" to duration)
        )
        presignedData.publicUrl
    } catch (e: Exception) {
        Timber.e(e, "上传图片过程中发生错误")
        onState(TaskStatus.Fail)
        trackEventOnSentry(
            "network_failure",
            mapOf("path" to "/upload_image", "error" to (e.message ?: "未知错误"))
        )
        null
    }
}
