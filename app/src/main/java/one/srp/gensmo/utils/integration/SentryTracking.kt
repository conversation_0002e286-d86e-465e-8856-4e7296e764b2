package one.srp.gensmo.utils.integration

import io.sentry.Sentry
import io.sentry.ISpan
import io.sentry.SpanStatus

/**
 * 使用 Sentry 跟踪通用事件
 * @param name 事件名称
 * @param extraData 事件相关数据 (可选)
 */
fun trackEventOnSentry(name: String, extraData: Map<String, Any>? = null) {
    val transaction = Sentry.startTransaction("event:$name", "event")
    var span: ISpan? = null
    
    try {
        span = transaction.startChild("event_tracking")
        span.setData("name", name)
        
        // 添加额外数据
        extraData?.forEach { (key, value) ->
            span.setData(key, value)
        }
        
        span.finish()
    } catch (e: Exception) {
        span?.throwable = e
        span?.status = SpanStatus.INTERNAL_ERROR
        span?.finish()
    } finally {
        transaction.finish()
    }
}
