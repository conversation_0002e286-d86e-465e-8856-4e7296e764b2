package one.srp.gensmo.utils.env

import one.srp.gensmo.BuildConfig


enum class ENV {
    DEVELOPMENT, STAGING, PRODUCTION,
}

data class EnvConfig(
    val env: ENV, val serverHost: String, val origin: String, val baseUrl: BaseUrl,
)

data class BaseUrl(
    val account: String, val recommend: String, val workflow: String, val metric: String,
)

object EnvConf {
    private val config = getEnvConfig(BuildConfig.APP_ENV)

    val env = config.env
    val serverHost = config.serverHost
    val origin = config.origin
    val baseUrl = config.baseUrl
}

fun getEnvConfig(env: String): EnvConfig {
    return when (env) {
        "development" -> {
            EnvConfig(
                env = ENV.DEVELOPMENT,
                serverHost = "favie.yesy.dev",
                origin = "https://gensmo.nosay.info",
                baseUrl = BaseUrl(
                    account = "https://account.favie.yesy.dev",
                    recommend = "https://gateway.yesy.dev",
                    workflow = "https://gem-workflow.favie.yesy.dev",
                    metric = "https://t.iminsp.com",
                )
            )
        }

        "staging" -> {
            EnvConfig(
                env = ENV.STAGING,
                serverHost = "favie.yesy.live",
                origin = "https://gensmo.nosay.live",
                baseUrl = BaseUrl(
                    account = "https://account.favie.yesy.live",
                    recommend = "https://gateway.yesy.live",
                    workflow = "https://gem-workflow.favie.yesy.live",
                    metric = "https://t.iminsp.com",
                )
            )
        }

        else -> {
            EnvConfig(
                env = ENV.PRODUCTION,
                serverHost = "gsmo.ai",
                origin = "https://gensmo.com",
                baseUrl = BaseUrl(
                    account = "https://account.gsmo.ai",
                    recommend = "https://gateway.gsmo.ai",
                    workflow = "https://gem-workflow.gsmo.ai",
                    metric = "https://t.iminsp.com",
                )
            )
        }
    }
}
