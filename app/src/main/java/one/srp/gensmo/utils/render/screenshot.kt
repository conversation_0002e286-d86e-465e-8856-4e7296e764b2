package one.srp.gensmo.utils.render

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.core.graphics.createBitmap
import androidx.core.view.isInvisible
import kotlinx.coroutines.delay

private fun Context.findActivity(): Activity? = when (this) {
    is Activity -> this
    is ContextWrapper -> baseContext.findActivity()
    else -> null
}

suspend fun renderComposeToBitmap(
    context: Context,
    width: Int = 600,
    height: Int = 800,
    delayMs: Long = 500,
    content: @Composable () -> Unit,
): Bitmap {
    val activity =
        context.findActivity() ?: throw IllegalArgumentException("Context must be an Activity.")

    val rootView = activity.window.decorView as ViewGroup

    val frameLayout = FrameLayout(context).apply {
        layoutParams = ViewGroup.LayoutParams(width, height)
        isInvisible = true
    }

    val composeView = ComposeView(context).apply {
        setContent {
            content()
        }
        layoutParams = ViewGroup.LayoutParams(width, height)
        setLayerType(View.LAYER_TYPE_SOFTWARE, null)
    }

    frameLayout.addView(composeView)
    rootView.addView(frameLayout)

    composeView.measure(
        View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY),
        View.MeasureSpec.makeMeasureSpec(height, View.MeasureSpec.EXACTLY)
    )
    composeView.layout(0, 0, width, height)

    val bitmap = createBitmap(width, height)
    val canvas = Canvas(bitmap)

    delay(delayMs)

    composeView.draw(canvas)

    rootView.removeView(frameLayout)

    return bitmap
}
