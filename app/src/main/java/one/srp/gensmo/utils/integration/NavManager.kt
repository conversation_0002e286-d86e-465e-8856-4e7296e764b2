package one.srp.gensmo.utils.integration

import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.Lifecycle
import androidx.navigation.NavController
import one.srp.gensmo.ui.navigation.navigateDeeplink
import timber.log.Timber
import java.lang.ref.WeakReference

/**
 * 统一的导航管理器
 * 使用WeakReference避免内存泄漏，为所有需要导航功能的模块提供服务
 */
object NavManager {
    private var navControllerRef: WeakReference<NavController>? = null
    private val pendingDeepLinks = mutableListOf<Uri>()
    private val processingDeepLinks = mutableSetOf<Uri>() // 正在处理的深链接
    private val handler = Handler(Looper.getMainLooper())
    private val  maxRetryCount = 10
    private val retryDelayMs = 500L

    /**
     * 设置NavController
     */
    fun setNavController(navController: NavController) {
        navControllerRef = WeakReference(navController)
        // NavController设置后，处理待处理的深链接
        processPendingDeepLinks()
    }

    /**
     * 获取NavController（如果仍然有效）
     */
    fun getNavController(): NavController? {
        return navControllerRef?.get().also { navController ->
            if (navController == null && navControllerRef != null) {
                Timber.w("NavController引用为空或已被回收")
            } else if (navController != null) {
                Timber.d("NavController引用有效")
            }
        }
    }

    /**
     * 执行深链接导航
     */
    fun navigateDeepLink(uri: Uri) {
        Timber.d("NavManager收到深链接请求: $uri")
        
        // 检查是否已经在处理这个深链接
        synchronized(processingDeepLinks) {
            if (processingDeepLinks.contains(uri)) {
                Timber.w("深链接正在处理中，跳过重复请求: $uri")
                return
            }
        }
        
        val navController = getNavController()
        
        if (navController != null && isNavControllerReady(navController)) {
            Timber.d("NavController可用，直接执行深链接导航: $uri")
            executeDeepLink(navController, uri)
        } else {
            Timber.w("NavController不可用或未准备就绪，添加到待处理队列: $uri")
            addToPendingDeepLinks(uri)
            // 只有在NavController不就绪时才开始重试检查
            scheduleRetryDeepLink(uri, 1)
        }
    }
    
    /**
     * 检查NavController是否准备就绪（不仅仅是存在，还要能正常工作）
     */
    private fun isNavControllerReady(navController: NavController): Boolean {
        return try {
            // 检查基本状态
            val currentDestination = navController.currentDestination
            if (currentDestination == null) {
                Timber.d("NavController未就绪：currentDestination为null")
                return false
            }
            
            // 检查当前BackStackEntry的状态
            val currentEntry = navController.currentBackStackEntry
            if (currentEntry == null) {
                Timber.d("NavController未就绪：currentBackStackEntry为null")
                return false
            }
            
            // 检查生命周期状态是否至少达到CREATED
            val lifecycle = currentEntry.lifecycle
            val currentState = lifecycle.currentState
            val isStateReady = currentState.isAtLeast(Lifecycle.State.CREATED)
            
            if (!isStateReady) {
                Timber.d("NavController未就绪：当前状态为${currentState}，需要至少CREATED")
                return false
            }
            
            // 检查是否有pending operations
            try {
                navController.graph // 尝试访问graph，如果抛异常说明状态不稳定
            } catch (e: Exception) {
                Timber.d(e, "NavController未就绪：访问graph异常")
                return false
            }
            
            Timber.d("NavController状态检查通过：destination=${currentDestination.route}, state=${currentState}")
            return true
            
        } catch (e: Exception) {
            Timber.w(e, "NavController状态检查异常")
            return false
        }
    }
    
    /**
     * 添加到待处理深链接队列
     */
    private fun addToPendingDeepLinks(uri: Uri) {
        synchronized(pendingDeepLinks) {
            if (!pendingDeepLinks.contains(uri)) {
                pendingDeepLinks.add(uri)
                Timber.d("深链接已添加到待处理队列，当前队列大小: ${pendingDeepLinks.size}")
            }
        }
    }
    
    /**
     * 处理待处理的深链接
     */
    private fun processPendingDeepLinks() {
        val navController = getNavController()
        if (navController != null && isNavControllerReady(navController)) {
            // 创建待处理列表的副本，避免并发修改异常
            val pendingLinks = synchronized(pendingDeepLinks) {
                pendingDeepLinks.toList() // 创建副本
            }
            
            for (uri in pendingLinks) {
                // 检查是否仍在待处理队列中（可能已被其他地方处理）
                val stillPending = synchronized(pendingDeepLinks) {
                    pendingDeepLinks.contains(uri)
                }
                
                if (!stillPending) {
                    Timber.d("深链接已被处理，跳过: $uri")
                    continue
                }
                
                // 检查是否已经在处理
                val isProcessing = synchronized(processingDeepLinks) {
                    processingDeepLinks.contains(uri)
                }
                
                if (isProcessing) {
                    Timber.d("深链接正在处理中，跳过: $uri")
                    continue
                }
                
                try {
                    Timber.d("处理待处理深链接: $uri")
                    executeDeepLink(navController, uri)
                } catch (e: Exception) {
                    Timber.e(e, "处理待处理深链接失败: $uri")
                    // 出错时不继续处理其他深链接，稍后重试
                    break
                }
            }
        }
    }
    
    /**
     * 安排重试深链接导航
     */
    private fun scheduleRetryDeepLink(uri: Uri, retryCount: Int) {
        if (retryCount > maxRetryCount) {
            Timber.e("深链接重试次数已达上限，放弃处理: $uri")
            removePendingDeepLink(uri)
            return
        }
        
        handler.postDelayed({
            // 检查深链接是否还在待处理队列中，如果不在说明已被处理
            val stillPending = synchronized(pendingDeepLinks) {
                pendingDeepLinks.contains(uri)
            }
            
            if (!stillPending) {
                Timber.d("深链接已被处理，取消重试: $uri")
                return@postDelayed
            }
            
            // 检查是否已经在处理
            synchronized(processingDeepLinks) {
                if (processingDeepLinks.contains(uri)) {
                    Timber.d("深链接正在处理中，取消重试: $uri")
                    return@postDelayed
                }
            }
            
            val navController = getNavController()
            if (navController != null && isNavControllerReady(navController)) {
                Timber.d("重试第${retryCount}次成功，执行深链接: $uri")
                executeDeepLink(navController, uri)
                removePendingDeepLink(uri)
            } else {
                Timber.d("重试第${retryCount}次失败，NavController仍未就绪: $uri")
                scheduleRetryDeepLink(uri, retryCount + 1)
            }
        }, retryDelayMs)
    }
    
    /**
     * 从待处理队列中移除深链接
     */
    private fun removePendingDeepLink(uri: Uri) {
        synchronized(pendingDeepLinks) {
            pendingDeepLinks.remove(uri)
        }
    }
    
    /**
     * 执行深链接导航
     */
    private fun executeDeepLink(navController: NavController, uri: Uri) {
        // 检查是否已经在处理（双重检查）
        synchronized(processingDeepLinks) {
            if (processingDeepLinks.contains(uri)) {
                Timber.w("深链接已在处理中，跳过重复执行: $uri")
                return
            }
            // 标记为正在处理
            processingDeepLinks.add(uri)
        }
        
        try {
            Timber.d("开始执行深链接导航: $uri")
            navigateDeeplink(navController, uri)
            Timber.d("深链接导航执行成功: $uri")
            
            // 成功执行后，立即从待处理队列中移除，防止重复处理
            removePendingDeepLink(uri)
            
        } catch (e: Exception) {
            Timber.e(e, "深链接导航执行失败: $uri")
        } finally {
            // 清理处理标记
            synchronized(processingDeepLinks) {
                processingDeepLinks.remove(uri)
            }
        }
    }
} 