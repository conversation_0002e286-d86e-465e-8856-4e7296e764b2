package one.srp.gensmo.utils.metrics

import android.os.Bundle
import com.google.firebase.analytics.ParametersBuilder
import one.srp.core.analytics.types.EventRefer
import one.srp.core.analytics.types.METRIC_VERSION
import one.srp.core.analytics.types.STRING_DEFAULT
import one.srp.gensmo.data.store.ABDataStoreManager
import one.srp.gensmo.data.store.DeviceDataStoreManager
import one.srp.gensmo.data.store.UserDataStoreManager
import one.srp.gensmo.utils.env.MetaInfo
import timber.log.Timber

object MetricData {
    private val _referStack = mutableListOf<EventRefer>()

    fun registerRefer(current: EventRefer) {
        if (_referStack.isNotEmpty() && _referStack.last() == current) return

        _referStack.add(current)
        if (_referStack.size >= 10) _referStack.removeAt(0)
    }

    fun getPreRefer(): EventRefer {
        if (_referStack.size < 2) return EventRefer.Default
        return _referStack[_referStack.size - 2]
    }

    fun getRefer(): EventRefer {
        if (_referStack.isEmpty()) return EventRefer.Default
        return _referStack.last()
    }

    suspend fun getAbInfo(): String? {
        return ABDataStoreManager.getABTestConfigJsonString()
    }

    suspend fun getUserId(): String? {
        return UserDataStoreManager.getUserId()
    }

    suspend fun getUserType(): String? {
        return when (UserDataStoreManager.getLoginType()) {
            1 -> {
                "login"
            }

            else -> {
                "guest"
            }
        }
    }

    suspend fun getActiveId(): String? {
        return DeviceDataStoreManager.getDeviceId()
    }

    suspend fun getUserSourceId(): String? {
        return null
    }

    private var _appsflyerId: String? = null
    private var _appsflyerLogEventFn: ((String, Map<String, Any>) -> Unit)? = null

    fun getAppsflyerId(): String? {
        return _appsflyerId
    }

    fun setAppsflyerId(id: String) {
        _appsflyerId = id
    }

    fun setAppsflyerLogEvent(instance: AppsflyerManager, fn: (String, Map<String, Any>) -> Unit) {
        _appsflyerLogEventFn = fn
    }

    fun logEventAF(eventName: String, eventValue: Map<String, Any> = hashMapOf()) {
        Timber.d("logEventAF: $eventName, $eventValue")
        _appsflyerLogEventFn?.invoke(eventName, eventValue)
    }
}

data class ParamsGen(
    val builder: (ParametersBuilder.() -> Unit),
    val bundle: (Bundle.() -> Unit),
)

fun buildParams(): ParamsGen {
    return ParamsGen(
        builder = {
            param("platform", MetaInfo.PLATFORM)
            param("version", MetaInfo.VERSION)
            param("event_version", METRIC_VERSION)
        },
        bundle = {
            putString("platform", MetaInfo.PLATFORM)
            putString("version", MetaInfo.VERSION)
            putString("event_version", METRIC_VERSION)
        }
    )
}

suspend fun runtimeParams(): ParamsGen {
    val timestamp = (System.currentTimeMillis() * 1000).toString()

    val abInfo = MetricData.getAbInfo() ?: ""

    val userId = MetricData.getUserId() ?: STRING_DEFAULT
    val userType = MetricData.getUserType() ?: STRING_DEFAULT
    val activeId = MetricData.getActiveId() ?: STRING_DEFAULT
    val userSourceId = MetricData.getUserSourceId() ?: STRING_DEFAULT
    val appsflyerId = MetricData.getAppsflyerId() ?: STRING_DEFAULT

    return ParamsGen(
        builder = {
            param("gensmo_timestamp", timestamp)

            param("ab_info", abInfo)

            param("gensmo_user_id", userId)
            param("gensmo_user_type", userType)
            param("gensmo_active_id", activeId)
            param("gensmo_user_source_id", userSourceId)
            param("appsflyer_id", appsflyerId)
        },
        bundle = {
            putString("gensmo_timestamp", timestamp)

            putString("ab_info", abInfo)

            putString("gensmo_user_id", userId)
            putString("gensmo_user_type", userType)
            putString("gensmo_active_id", activeId)
            putString("gensmo_user_source_id", userSourceId)
            putString("appsflyer_id", appsflyerId)
        }
    )
}