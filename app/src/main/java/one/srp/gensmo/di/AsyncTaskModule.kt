package one.srp.gensmo.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import one.srp.gensmo.utils.asyncTask.AsyncTaskManager
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AsyncTaskModule {
    @Provides
    @Singleton
    fun provideAsyncTaskManager(
    ): AsyncTaskManager {
        return AsyncTaskManager()
    }
}
