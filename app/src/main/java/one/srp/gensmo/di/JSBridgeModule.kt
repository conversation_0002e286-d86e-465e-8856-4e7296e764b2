package one.srp.gensmo.di

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import one.srp.gensmo.utils.bridge.JSBridgeManager
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object JSBridgeModule {
    @Provides
    @Singleton
    fun provideJSBridgeManager(
    ): JSBridgeManager {
        return JSBridgeManager()
    }
}
