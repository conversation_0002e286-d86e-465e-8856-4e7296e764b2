<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />

    <uses-feature android:name="android.hardware.camera.any" />

    <application
        android:name=".MainApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/${networkSecurityConfig}"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Gensmo"
        tools:replace="android:dataExtractionRules, android:fullBackupContent"
        tools:targetApi="31">

        <activity
            android:name=".MainActivity"
            android:enableOnBackInvokedCallback="true"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Gensmo.Starting"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="gensmo.onelink.me"
                    android:scheme="https" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="gensmo" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="gensmo.com"
                    android:pathPrefix="/app-entry"
                    android:scheme="https" />
            </intent-filter>

        </activity>

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <!-- Required: set your sentry.io project identifier (DSN) -->
        <meta-data
            android:name="io.sentry.dsn"
            android:value="https://<EMAIL>/4509047975575552" />

        <!-- Add data like request headers, user ip address and device name, see https://docs.sentry.io/platforms/android/data-management/data-collected/ for more info -->
        <meta-data
            android:name="io.sentry.send-default-pii"
            android:value="true" />

        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <meta-data
            android:name="io.sentry.traces.user-interaction.enable"
            android:value="true" />
        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
        <meta-data
            android:name="io.sentry.attach-screenshot"
            android:value="true" />
        <!-- enable view hierarchy for crashes -->
        <meta-data
            android:name="io.sentry.attach-view-hierarchy"
            android:value="true" />

        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <meta-data
            android:name="io.sentry.traces.sample-rate"
            android:value="1.0" />

        <!-- enable profiling when starting transactions, adjust in production env -->
        <meta-data
            android:name="io.sentry.traces.profiling.sample-rate"
            android:value="0.1" />
        <!-- enable app start profiling -->
        <meta-data
            android:name="io.sentry.traces.profiling.enable-app-start"
            android:value="true" />

        <meta-data
            android:name="google_analytics_deferred_deep_link_enabled"
            android:value="true" />
    </application>

</manifest>